{"analysis_date": "2025-07-29T00:07:37.577021", "data_summary": {"total_payments": 103, "total_invoices": 50, "total_bank_transactions": 1878, "receive_transactions": 137}, "orphaned_payments": {"count": 58, "percentage": 56.***************, "total_by_currency": {"EUR": 800.0, "GBP": 47353.0, "USD": 7498.26}, "top_contacts": [["L'atelier Nawbar", 21], ["KITU KALI LIMITED", 11], ["Packegha", 8], ["L’atelier Nawbar", 7], ["<PERSON>", 3], ["ESC S.A.L", 2], ["Lines and Arts s.a.r.l", 2], ["High Tech XL Group B.V", 1], ["INCORP", 1], ["Lumi SAL", 1]], "status_distribution": {"AUTHORISED": 57, "DELETED": 1}, "payments": [{"PaymentID": "0bfe9e16-9936-482b-a7a6-d491a6af4b4e", "Date": "/Date(*************+0000)/", "BankAmount": 800.0, "Amount": 800.0, "Reference": "", "CurrencyRate": 1.20745, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "664a2475-c35f-4aca-a6b3-55bb7e927134", "InvoiceNumber": "INV-0014", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "4fcb7eef-0d7d-4b4d-981b-d564a59cd0be", "Name": "High Tech XL Group B.V", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "EUR"}, "HasValidationErrors": false}, {"PaymentID": "c4ffa8e7-1339-4a0b-b8f5-2cad1dfb9ef1", "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "DELETED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f0c6677e-7100-44fa-8a83-31bce6486f6b", "InvoiceNumber": "INV-0013", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-09d7-410c-9b05-09f6322ed265", "Date": "/Date(*************+0000)/", "BankAmount": 347.06, "Amount": 400.0, "Reference": "ch_3Ltv62S5gCcGK53K0X2Nb2bt", "CurrencyRate": 1.152538, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a381477d-6ccb-4046-8db1-25c82b169ada", "InvoiceNumber": "INV-0029", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "d2e3bdbe-cd8e-4ebd-bfb7-b128d313c727", "Name": "INCORP", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c14db7ba-c2e9-4dba-837d-8fb46bf25e21", "Date": "/Date(*************+0000)/", "BankAmount": 408.57, "Amount": 479.0, "Reference": "ch_3LzaMWS5gCcGK53K1HxwsTZn", "CurrencyRate": 1.172382, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "d967c303-b2cb-43ea-a4c2-eaff49c4223f", "InvoiceNumber": "INV-0036", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "5a331741-5d73-4c8e-9e19-971718fcd829", "Name": "ESC S.A.L", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "f2127656-625d-4e69-a1b9-d05018811f02", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "38571c15-20da-4091-96b6-582ce028c04c", "InvoiceNumber": "INV-000104", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-d0af-4e13-8952-c278e4e06d72", "Date": "/Date(*************+0000)/", "BankAmount": 596.46, "Amount": 799.26, "Reference": "#*********", "CurrencyRate": 1.34, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "95068ba5-92dc-49e5-bfbc-8e0ae7a6ea94", "InvoiceNumber": "INV-0004", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "487c27d8-2b80-4748-9a9f-0f4511c30479", "Date": "/Date(*************+0000)/", "BankAmount": 2880.0, "Amount": 2880.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "27221c70-8ba6-4d20-840c-dd41a3145101", "InvoiceNumber": "INV-0002", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "f85b73db-6eaa-48c2-9f92-28e13470fa84", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "fe0b8ce5-5a08-48be-8bae-ce110761b6b3", "InvoiceNumber": "INV-0005", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "0bcf9d8a-08c0-4e30-9393-a1fc5e846e3c", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "********-0697-44f0-8f04-44ef673e8f79", "InvoiceNumber": "INV-0008", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "a5639110-c986-4a69-a1a0-722b7d814498", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "Reference": "", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "1a98f499-e1df-4b07-bf93-b4c6a4cedb97", "InvoiceNumber": "INV-0006", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "IsDiscounted": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "LineItems": [], "CurrencyCode": "GBP"}, "HasValidationErrors": false}]}, "unrecorded_bank_receipts": {"total_receive": 137, "unmatched_count": 128, "unmatched_percentage": 93.**************, "top_contacts": [["Toters", 34], ["<PERSON>", 25], ["<PERSON>", 15], ["TBA", 12], ["Bilal itani", 8], ["BD", 8], ["<PERSON>", 7], ["Google", 3], ["Mongodb", 2], ["UPOD MEDICAL LTD", 2]], "amount_ranges": {"Small (<50)": 89, "Medium (50-500)": 14, "Large (500-1000)": 1, "Very Large (>1000)": 24}, "total_by_currency": {"USD": 228395.***********, "GBP": 14187.************, "EUR": 9009.38}, "transactions": [{"BankTransactionID": "a72777b5-ca1c-4e86-b13b-edca4f6eb7d9", "BankAccount": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Name": "WISE USD"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.27841, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-01T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 10.23, "TotalTax": 0.0, "Total": 10.23, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "USD"}, {"BankTransactionID": "3bdda9ce-49cb-4f76-820d-c4e064d6058f", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "Reference": "CARD_TRANSACTION_CASHBACK-835826", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "e097e16a-a50d-422a-add8-002267f3bb0e", "Name": "<PERSON>", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-07-31T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 2.68, "TotalTax": 0.0, "Total": 2.68, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "e3e7364f-ffc3-407d-94c3-916d47e5706a", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "Reference": "CARD-*********", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "57bf6c50-d92f-462f-935f-e2bcaf24419a", "Name": "<PERSON>", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-17T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 2.19, "TotalTax": 0.0, "Total": 2.19, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "769f4ce8-c4ba-4a62-b203-cfa2783a9de4", "BankAccount": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Name": "WISE USD"}, "Type": "RECEIVE", "Reference": "TRANSFER-*********", "IsReconciled": true, "CurrencyRate": 1.2687, "HasAttachments": false, "Contact": {"ContactID": "b5d5be37-4a20-4a9e-9695-8d6a102d2424", "Name": "MAGALI MOUNIR SAWMA", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-14T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1014.39, "TotalTax": 0.0, "Total": 1014.39, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "USD"}, {"BankTransactionID": "********-0937-4dd7-932c-17ce8a597f72", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-09-01T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 4.27, "TotalTax": 0.0, "Total": 4.27, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "c19ec443-0046-435e-abc1-e18325a7efd2", "BankAccount": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Name": "WISE USD"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.2588, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-09-01T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 3.79, "TotalTax": 0.0, "Total": 3.79, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "USD"}, {"BankTransactionID": "dfdcd4b7-278a-418e-9b36-f27cdd4ebf66", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.16072, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-01T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 0.08, "TotalTax": 0.0, "Total": 0.08, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "8394db79-492b-4faa-af93-4b8e0b62989a", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.16697, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-09-01T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 0.08, "TotalTax": 0.0, "Total": 0.08, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "5604c7a7-f7cb-4b49-957b-77e24fe4a55e", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "Name": "Toters", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-10-02T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 0.89, "TotalTax": 0.0, "Total": 0.89, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "1a6de531-f7e1-4383-affc-22458daf40b0", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "c701a41b-26e0-4b50-803d-d891b65eeacc", "Name": "Carrefour", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-10-07T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 0.01, "TotalTax": 0.0, "Total": 0.01, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}]}, "financial_impact": {"orphaned_payments_total": {"EUR": 800.0, "GBP": 47353.0, "USD": 7498.26}, "unrecorded_receipts_total": {"USD": 228395.***********, "GBP": 14187.************, "EUR": 9009.38}}}
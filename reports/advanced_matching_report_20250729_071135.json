{"matches": [{"payment_id": "26f29189-efaf-46d8-b41c-b91f7cb2a079", "payment_amount": 6000.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0116", "contact_name": "UPOD MEDICAL LTD", "bank_transaction_id": "dec21604-c20b-401c-9e96-e24fc7475c43", "bank_amount": 4412.8, "bank_date": "2024-11-20T00:00:00", "bank_reference": "Payment for invoice INV-0116", "confidence": 1.0, "matched_rules": ["invoice_ref_in_bank"], "currency": "GBP"}, {"payment_id": "********-909f-4c13-acce-5404c5c3f774", "payment_amount": 2500.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0118", "contact_name": "UPOD MEDICAL LTD", "bank_transaction_id": "e82f127b-263f-4cee-a335-4b3a0b7d7563", "bank_amount": 703.0, "bank_date": "2021-07-09T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "ed0c77f5-8591-478c-ad39-de99a9565927", "payment_amount": 2500.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0121", "contact_name": "UPOD MEDICAL LTD", "bank_transaction_id": "fb36cc67-4fad-42ed-b123-455a18e73441", "bank_amount": 0.73, "bank_date": "2021-07-14T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "5c0f7688-22c0-4a84-a16e-b8fa29e377fa", "payment_amount": 1500.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0082", "contact_name": "<PERSON><PERSON>", "bank_transaction_id": "0b8dabbf-7e18-46d1-ac1a-e7a7e25f589a", "bank_amount": 0.73, "bank_date": "2021-07-14T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "871abd1c-cd12-4d7e-966e-fe3ecb7f01fb", "payment_amount": 1500.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0094", "contact_name": "<PERSON><PERSON>", "bank_transaction_id": "6a36479f-695a-40e1-8e82-ff648fe58e6e", "bank_amount": 0.75, "bank_date": "2021-10-09T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "0bcf9d8a-08c0-4e30-9393-a1fc5e846e3c", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0008", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "6d04dc61-e58d-496a-abd0-c975d29bf0e7", "bank_amount": 602.48, "bank_date": "2021-11-26T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "363d287d-cb1c-475c-a34d-883483af5134", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0009", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "6a7eb39d-7bed-40b7-98bb-026f75b1d7c8", "bank_amount": 162.96, "bank_date": "2022-01-27T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "7f17692e-5d18-4d84-bd88-7c8a50b73b31", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0010", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "f2bd4c3b-e8cf-44b9-b564-be1f718731ec", "bank_amount": 553.93, "bank_date": "2022-02-27T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f2127656-625d-4e69-a1b9-d05018811f02", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-000104", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "ad59451c-a7a1-4807-be58-8855c0db97c1", "bank_amount": 29.95, "bank_date": "2022-03-12T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f85b73db-6eaa-48c2-9f92-28e13470fa84", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0005", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "5e6b612b-b9c2-4e9c-9235-e540f324705a", "bank_amount": 703.0, "bank_date": "2022-05-09T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "5f05a8d9-567b-4603-84eb-66682d2ba574", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0007", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "06e83f20-f132-4962-b70a-1c845f3161ad", "bank_amount": 0.81, "bank_date": "2022-05-23T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "da057d0a-8e26-4c57-8cb2-39cbf0d367d4", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0016", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "56e95ab4-0ccb-42d2-8511-cb207c0d2044", "bank_amount": 300.0, "bank_date": "2022-07-09T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "8c6ad0d5-29d5-4dd8-8139-b34e41cf65d4", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0012", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "e32d7b96-a1a0-4b7f-b8e4-2c365afa4141", "bank_amount": 100.0, "bank_date": "2022-09-28T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "9e6249ab-4197-41a6-b995-2d00b45f363d", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0027", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "2761540f-8f5f-4c0a-9c98-8ea71a992104", "bank_amount": 1.75, "bank_date": "2022-12-01T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-74251", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "837935d6-0f34-4f8b-961f-34e5b7107f2e", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0032", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "1cd992b6-2675-4d5d-99e5-de5caed45cf6", "bank_amount": 2.25, "bank_date": "2023-01-01T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-187652", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "5acb984f-dbf8-4c44-be84-785025426c9d", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0038", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "0bfcd958-2a2e-48cf-9bd9-c7473fdc09cd", "bank_amount": 5000.0, "bank_date": "2023-01-29T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "3d31fa3f-81bf-49db-9d7e-9792a8191e4c", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0039", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "91edeb04-0b25-4f32-84fd-3c4113ca9874", "bank_amount": 1.67, "bank_date": "2023-02-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "328da062-5252-4126-b329-ad3a464dae15", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0044", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "********-9f66-487f-bc13-3f06134836af", "bank_amount": 1.79, "bank_date": "2023-03-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "c95d0ebc-0712-4e37-90de-97f1fc06fff3", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0049", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "272d4af9-c898-472c-8a16-23a3f2d728f8", "bank_amount": 0.14, "bank_date": "2023-04-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "70bf7e70-fc0a-4374-a95d-61a3da2704fa", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0053", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "f8d48bd1-09dc-4d24-b33a-abc4a3482e18", "bank_amount": 0.54, "bank_date": "2023-05-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "7072061a-5b7e-4abc-b2d1-e7513b64ad35", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0058", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "26b6d424-f8b6-472e-9ff0-f4b3cce559c1", "bank_amount": 0.67, "bank_date": "2023-06-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "886b05d2-7fc5-4895-bcfa-fbd997f4b368", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0061", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "d1be4287-0d99-49f1-8e42-e551dadbf5f8", "bank_amount": 1.05, "bank_date": "2023-07-02T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-716004", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "6850d2d0-5665-4f81-b2ad-96bcac572bec", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0065", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "3bdda9ce-49cb-4f76-820d-c4e064d6058f", "bank_amount": 2.68, "bank_date": "2023-07-31T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-835826", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "ef9d9aeb-6cae-4547-a22a-653470c8e3b1", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0067", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "e3e7364f-ffc3-407d-94c3-916d47e5706a", "bank_amount": 2.19, "bank_date": "2023-08-17T00:00:00", "bank_reference": "CARD-*********", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "75287d39-6670-47d5-859d-ad4da5b124c4", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0070", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "********-0937-4dd7-932c-17ce8a597f72", "bank_amount": 4.27, "bank_date": "2023-09-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "4789f7f6-2f80-40d3-a1f9-1baa85ab4f0f", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0077", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "5604c7a7-f7cb-4b49-957b-77e24fe4a55e", "bank_amount": 0.89, "bank_date": "2023-10-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f8aab87d-149a-4ea2-999f-0b738333beb7", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0081", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "1a6de531-f7e1-4383-affc-22458daf40b0", "bank_amount": 0.01, "bank_date": "2023-10-07T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "1b5a40b9-687e-4a52-aeb6-83fb29fb91ed", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0086", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "f1d653ac-01a9-4f8c-afbb-e5fd50895014", "bank_amount": 4.53, "bank_date": "2023-10-22T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f36893a4-f427-4028-a166-4123a2a7342a", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0089", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "af5b2d69-f8fb-4430-ba80-e1c5149e6b09", "bank_amount": 1.15, "bank_date": "2023-11-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "eb3ddfb8-dc45-47c7-8b0a-a5fdf6ce0df2", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0090", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "70577cad-e03d-473c-adf3-8c7a5b236178", "bank_amount": 0.66, "bank_date": "2023-12-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f7f1c37a-c5d6-464f-83b6-4d27cd2d2ed6", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0099", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "39d466eb-b37f-413c-aea3-3e9309072f81", "bank_amount": 0.65, "bank_date": "2024-01-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "405a7370-f574-42b3-b6ed-8f1747ade11c", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0095", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "aed1dc73-6669-44df-bacf-58d422b2f15a", "bank_amount": 0.78, "bank_date": "2024-02-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "839f3ce8-4870-42d9-986b-b084213cdef7", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0106", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "abe7d800-840c-4bb2-b2ec-b06764e387d5", "bank_amount": 4.08, "bank_date": "2024-02-29T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "b0eb35a7-e866-40eb-99e2-4f7505d28023", "payment_amount": 703.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0025", "contact_name": "L’atelier Nawbar", "bank_transaction_id": "3d47665d-b684-43ee-8c95-d800cae54d03", "bank_amount": 0.56, "bank_date": "2024-03-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "f302380e-2a2c-48dc-92db-d4404c3f407d", "payment_amount": 703.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0031", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "64341f6a-eb47-49d4-aae1-c562c25f9695", "bank_amount": 4.37, "bank_date": "2024-03-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "98a986ee-0a1a-41eb-81b6-8d2d6d79cb91", "payment_amount": 686.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0030", "contact_name": "L'atelier Nawbar", "bank_transaction_id": "8de3f2e0-60a8-4c01-9bda-efe7cc07d717", "bank_amount": 7.15, "bank_date": "2024-03-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "3f31f3ca-9f82-47ad-8b24-fd8485980818", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0037", "contact_name": "Packegha", "bank_transaction_id": "76abf6eb-d7ee-43c1-af52-b5ee92dfeb8b", "bank_amount": 312.15, "bank_date": "2022-08-12T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "615711d1-fa13-429b-9e56-82c99b694c84", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0035", "contact_name": "Lines and Arts s.a.r.l", "bank_transaction_id": "65fbf7b8-a70d-40e2-8c6f-8f869cf2a607", "bank_amount": 16.99, "bank_date": "2024-03-03T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "cbdfa68e-d303-4ace-95b1-cba45edc4a70", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0041", "contact_name": "Lines and Arts s.a.r.l", "bank_transaction_id": "3f55923e-bd55-4618-82ed-1aa10fece290", "bank_amount": 3.85, "bank_date": "2024-04-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "c8c7b95a-83c6-436e-aab5-8d3da1b7216a", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0043", "contact_name": "Packegha", "bank_transaction_id": "f8729808-9f40-4c57-9168-5831a2cd21af", "bank_amount": 110.48, "bank_date": "2022-11-15T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "78609c89-c530-4f2b-ba4f-e80b23957fda", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0040", "contact_name": "Packegha", "bank_transaction_id": "a305e5c5-24bd-43f5-9ed9-162a54aa9fd6", "bank_amount": 7.31, "bank_date": "2022-12-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "7bcd066f-4f0c-4fae-a638-210b902645c8", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0052", "contact_name": "Packegha", "bank_transaction_id": "538c5580-7b20-412b-b342-903dac52b530", "bank_amount": 10.29, "bank_date": "2023-01-01T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-134024", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "b93e5d1c-369f-4d9d-a492-034bdd6cc929", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0048", "contact_name": "Packegha", "bank_transaction_id": "2cab20e3-5ff8-470e-9b44-3d0618f9929c", "bank_amount": 3.47, "bank_date": "2023-02-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "56e235d5-06f4-4fc6-bec7-2fee4e288a52", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0057", "contact_name": "Packegha", "bank_transaction_id": "ae77768b-0e92-461b-8b75-f4fbdbb1f021", "bank_amount": 4.81, "bank_date": "2023-03-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "35522bc0-ff3d-4386-8286-6873b9cbb036", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0060", "contact_name": "Packegha", "bank_transaction_id": "8a7d770b-6922-4c6b-b81e-416178e2c3ec", "bank_amount": 2.08, "bank_date": "2023-04-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "b0d59df8-23f4-48f2-95f3-f248fdd46ccb", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0069", "contact_name": "Packegha", "bank_transaction_id": "b51f3c90-9166-4a72-b395-9b8620e4a3d8", "bank_amount": 1.07, "bank_date": "2023-05-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "2a6a80cf-575b-4ff4-9d76-80768aee5a89", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0087", "contact_name": "Packegha", "bank_transaction_id": "33ff24d9-199f-489b-8d1a-f84a1a1f25c1", "bank_amount": 2.02, "bank_date": "2023-06-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "d99ca525-44bb-4dbc-a2eb-adfb39c8dab6", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0088", "contact_name": "Packegha", "bank_transaction_id": "1596b6af-4a2c-4f73-a731-aeeb5cb1bd41", "bank_amount": 9.98, "bank_date": "2023-07-02T00:00:00", "bank_reference": "CARD_TRANSACTION_CASHBACK-667966", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "be0d2b94-87f5-4328-a07a-fa56b399edee", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0091", "contact_name": "Packegha", "bank_transaction_id": "2885bd79-b23d-47f4-a87d-7fe287d8b211", "bank_amount": 5.95, "bank_date": "2023-07-11T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "a5dfeb4f-b9e8-4205-bdf7-f5ad0bab8171", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0096", "contact_name": "Packegha", "bank_transaction_id": "a72777b5-ca1c-4e86-b13b-edca4f6eb7d9", "bank_amount": 10.23, "bank_date": "2023-08-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "d6405e5e-1618-4f2d-a7ad-f45e097577e0", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0103", "contact_name": "Packegha", "bank_transaction_id": "769f4ce8-c4ba-4a62-b203-cfa2783a9de4", "bank_amount": 1014.39, "bank_date": "2023-08-14T00:00:00", "bank_reference": "TRANSFER-*********", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "2efb9d90-af51-4058-877c-3d2bc3b541b2", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0107", "contact_name": "Packegha", "bank_transaction_id": "c19ec443-0046-435e-abc1-e18325a7efd2", "bank_amount": 3.79, "bank_date": "2023-09-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "97b00792-ebb6-4158-a86b-c288d83635c3", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0110", "contact_name": "Packegha", "bank_transaction_id": "ea09e19c-e7ee-464b-aade-f66f327cd82b", "bank_amount": 6.03, "bank_date": "2023-10-02T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "c0f82a4d-c6c0-4cbb-a602-643e1f7a956f", "payment_amount": 300.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0112", "contact_name": "Packegha", "bank_transaction_id": "600530c0-01a8-40f3-9ff0-db40f5633041", "bank_amount": 20.0, "bank_date": "2023-10-08T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "USD"}, {"payment_id": "9eb01a1b-507e-4136-88b4-d6a38c5f9678", "payment_amount": 240.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0011", "contact_name": "KITU KALI LIMITED", "bank_transaction_id": "9fad90a4-7554-4f79-921b-8119bd299a7a", "bank_amount": 4.33, "bank_date": "2024-05-01T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "********-5bc5-44fb-9112-f2f0fc825e6a", "payment_amount": 240.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0013", "contact_name": "KITU KALI LIMITED", "bank_transaction_id": "1a79c4dd-7305-4381-8b09-c7b141cae3bd", "bank_amount": 101.95, "bank_date": "2024-05-10T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "03a93acb-795f-4c39-99dd-9a19585a7718", "payment_amount": 240.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0021", "contact_name": "KITU KALI LIMITED", "bank_transaction_id": "d9f6a553-9553-401b-8969-50f5ade917dd", "bank_amount": 2.28, "bank_date": "2024-06-03T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "3d6f5eb5-26d6-47af-a12b-d0ac94235c80", "payment_amount": 240.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "0031", "contact_name": "KITU KALI LIMITED", "bank_transaction_id": "7b91d5dc-0456-4a6d-acb9-26261698f626", "bank_amount": 73.36, "bank_date": "2024-06-12T00:00:00", "bank_reference": "", "confidence": 0.85, "matched_rules": ["recurring_payment_pattern"], "currency": "GBP"}, {"payment_id": "959ee1aa-ac31-4fa0-b0c5-0edd3c25eb27", "payment_amount": 30.0, "payment_date": "/Date(*************+0000)/", "invoice_number": "INV-0078", "contact_name": "E-MOOD SAL", "bank_transaction_id": "643bca21-4705-4d78-bf27-23f582253e86", "bank_amount": 30.0, "bank_date": "2023-10-10T00:00:00", "bank_reference": "", "confidence": 0.****************, "matched_rules": ["exact_amount_near_date"], "currency": "USD"}], "unmatched_payments": [{"payment_id": "1c36201b-810c-476e-b474-33e4e01455cb", "amount": 7204.49, "date": "/Date(*************+0000)/", "invoice_number": "INV-0068", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "386303b3-4753-4e9c-aa1f-3a4a439bf29d", "amount": 7200.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0063", "contact": "ESC S.A.L", "currency": "GBP"}, {"payment_id": "247e319a-9919-4f82-ab3c-1b4c43ba4a50", "amount": 4500.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0062", "contact": "B<PERSON>", "currency": "GBP"}, {"payment_id": "521fbd46-4616-4c11-88fc-da86dcd3d692", "amount": 4215.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0104", "contact": "Hosny Homany Industrial Company SAL", "currency": "USD"}, {"payment_id": "3c7462cd-925a-4a3e-8658-cce6a39d5dc4", "amount": 3980.0, "date": "/Date(1712102400000+0000)/", "invoice_number": "INV-0104", "contact": "Hosny Homany Industrial Company SAL", "currency": "USD"}, {"payment_id": "21d1ff44-2fa6-4512-8680-f4c619655fee", "amount": 3796.0, "date": "/Date(1691107200000+0000)/", "invoice_number": "INV-0071", "contact": "W. <PERSON> & Sons.", "currency": "USD"}, {"payment_id": "cb488103-5f30-4812-9797-874c689b2fd3", "amount": 3700.0, "date": "/Date(1692057600000+0000)/", "invoice_number": "INV-0068", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "e62482f3-92c2-40ce-9dde-d64cb3bd9547", "amount": 3322.91, "date": "/Date(1734998400000+0000)/", "invoice_number": "INV-0118", "contact": "UPOD MEDICAL LTD", "currency": "GBP"}, {"payment_id": "4f60f64c-8165-4097-8dbb-e16d5ba362b5", "amount": 3000.0, "date": "/Date(1689638400000+0000)/", "invoice_number": "INV-0047", "contact": "L'atelier Nawbar", "currency": "USD"}, {"payment_id": "bcc2c68b-738b-4223-bde2-05027419bd42", "amount": 3000.0, "date": "/Date(1704844800000+0000)/", "invoice_number": "INV-0092", "contact": "TOMO BOTTLE LLC", "currency": "GBP"}, {"payment_id": "487c27d8-2b80-4748-9a9f-0f4511c30479", "amount": 2880.0, "date": "/Date(1624233600000+0000)/", "invoice_number": "INV-0002", "contact": "L'atelier Nawbar", "currency": "GBP"}, {"payment_id": "c91c88ea-e6a2-4220-b1c2-07354281c2ac", "amount": 2800.0, "date": "/Date(1701648000000+0000)/", "invoice_number": "INV-0085", "contact": "<PERSON> - Sin Dental", "currency": "GBP"}, {"payment_id": "b6b82de5-22e2-41ce-9ee2-16f7f9097643", "amount": 2537.44, "date": "/Date(1695254400000+0000)/", "invoice_number": "INV-0079", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "5cab19ec-e389-4fc9-9d4f-7e7840304a11", "amount": 2514.53, "date": "/Date(1694044800000+0000)/", "invoice_number": "INV-0073", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "379307af-324c-4457-8bdc-ac056fbdbe34", "amount": 1852.3, "date": "/Date(*************+0000)/", "invoice_number": "INV-0084", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "25b40eed-ab84-4c5d-85c8-f12a4a2d7c3c", "amount": 1745.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0120", "contact": "Hosny Homany Industrial Company SAL", "currency": "USD"}, {"payment_id": "1e1b74f2-9f26-439c-89a1-8dacf43a6255", "amount": 1700.0, "date": "/Date(1704412800000+0000)/", "invoice_number": "INV-0093", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "5b24986d-928a-44f4-9ef4-f3de5661253e", "amount": 1500.0, "date": "/Date(1696377600000+0000)/", "invoice_number": "INV-0080", "contact": "<PERSON> - Sin Dental", "currency": "GBP"}, {"payment_id": "ffca65d1-4246-4dd9-ba54-23048b8d5e49", "amount": 1185.47, "date": "/Date(1695254400000+0000)/", "invoice_number": "INV-0073", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "b179eccf-2315-441c-b92a-43b8002b27e2", "amount": 1162.56, "date": "/Date(*************+0000)/", "invoice_number": "INV-0079", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "e04abb5d-febb-4610-8548-d13f395932a3", "amount": 1147.7, "date": "/Date(*************+0000)/", "invoice_number": "INV-0084", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "08226910-0a08-41e2-86d5-96d72be6c815", "amount": 1095.51, "date": "/Date(1694044800000+0000)/", "invoice_number": "INV-0068", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "a5639110-c986-4a69-a1a0-722b7d814498", "amount": 1020.0, "date": "/Date(1633564800000+0000)/", "invoice_number": "INV-0006", "contact": "L'atelier Nawbar", "currency": "GBP"}, {"payment_id": "0bfe9e16-9936-482b-a7a6-d491a6af4b4e", "amount": 800.0, "date": "/Date(1649894400000+0000)/", "invoice_number": "INV-0014", "contact": "High Tech XL Group B.V", "currency": "EUR"}, {"payment_id": "70169869-d0af-4e13-8952-c278e4e06d72", "amount": 799.26, "date": "/Date(1631491200000+0000)/", "invoice_number": "INV-0004", "contact": "KITU KALI LIMITED", "currency": "USD"}, {"payment_id": "c486595a-680f-4cf1-bdd6-d01ef8516ac5", "amount": 720.0, "date": "/Date(1664928000000+0000)/", "invoice_number": "INV-0033", "contact": "Packegha", "currency": "USD"}, {"payment_id": "87639db8-3c0b-40ec-8498-6f0b713ac8c6", "amount": 620.0, "date": "/Date(1695945600000+0000)/", "invoice_number": "INV-0078", "contact": "E-MOOD SAL", "currency": "USD"}, {"payment_id": "fa7e482d-3146-4625-9388-c16b07b5e65a", "amount": 600.0, "date": "/Date(1676851200000+0000)/", "invoice_number": "INV-0045", "contact": "Lumi SAL", "currency": "GBP"}, {"payment_id": "bdcfb606-4cfb-4d08-9879-fdd0fcbf0ed5", "amount": 600.0, "date": "/Date(1710892800000+0000)/", "invoice_number": "INV-0101", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "1b615ed3-4ac9-4802-988e-172849321600", "amount": 600.0, "date": "/Date(1730937600000+0000)/", "invoice_number": "INV-0114", "contact": "Packegha", "currency": "USD"}, {"payment_id": "c14db7ba-c2e9-4dba-837d-8fb46bf25e21", "amount": 479.0, "date": "/Date(1667347200000+0000)/", "invoice_number": "INV-0036", "contact": "ESC S.A.L", "currency": "USD"}, {"payment_id": "0001918b-0dc2-4008-b1d4-1c883442db4e", "amount": 420.0, "date": "/Date(1692316800000+0000)/", "invoice_number": "INV-0072", "contact": "Face Junkie Ltd", "currency": "GBP"}, {"payment_id": "18145765-09d7-410c-9b05-09f6322ed265", "amount": 400.0, "date": "/Date(1665964800000+0000)/", "invoice_number": "INV-0029", "contact": "INCORP", "currency": "USD"}, {"payment_id": "a903f1a7-6975-4a4c-afd4-8242793afead", "amount": 300.0, "date": "/Date(1709078400000+0000)/", "invoice_number": "INV-0098", "contact": "<PERSON> - Sin Dental", "currency": "GBP"}, {"payment_id": "c88e847d-0703-46ea-8a1d-a1d1c7186eaa", "amount": 200.0, "date": "/Date(1659052800000+0000)/", "invoice_number": "INV-0028", "contact": "KITU KALI LIMITED", "currency": "GBP"}, {"payment_id": "348349ae-9bf2-4efb-bbe9-56c11a24a858", "amount": 143.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0028", "contact": "KITU KALI LIMITED", "currency": "GBP"}, {"payment_id": "3c89168e-5c2e-47d4-8bf5-0a351b6c5b5d", "amount": 135.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0017", "contact": "KITU KALI LIMITED", "currency": "GBP"}, {"payment_id": "36354d50-1b5d-4a84-b90d-b363410a1f8d", "amount": 105.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0017", "contact": "KITU KALI LIMITED", "currency": "GBP"}, {"payment_id": "9830cc3e-19f4-456b-a35f-c74ebe29d59f", "amount": 100.0, "date": "/Date(1708128000000+0000)/", "invoice_number": "INV-0097", "contact": "<PERSON>", "currency": "GBP"}, {"payment_id": "15e6fc3c-3593-4ade-8148-561886b9a8ed", "amount": 34.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0030", "contact": "L'atelier Nawbar", "currency": "GBP"}, {"payment_id": "08f02270-79bb-4ade-ad46-667993dd8542", "amount": 30.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0028", "contact": "KITU KALI LIMITED", "currency": "GBP"}, {"payment_id": "b7a4f71a-310b-411a-9d14-685d6a863c52", "amount": 17.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0025", "contact": "L’atelier Nawbar", "currency": "GBP"}, {"payment_id": "1f383a5b-93a5-44c3-9f38-2dea65373af5", "amount": 17.0, "date": "/Date(*************+0000)/", "invoice_number": "INV-0031", "contact": "L'atelier Nawbar", "currency": "GBP"}], "unmatched_transactions": [{"transaction_id": "6bfbb71f-6680-4577-a95a-6da056698b6d", "amount": 6700.0, "date": "2021-07-09T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "375bc278-9fbd-4e13-8c3f-f5abb67c236b", "amount": 0.86, "date": "2021-07-23T00:00:00", "contact": "<PERSON><PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "a4dee236-a212-42fc-a861-3382394a8a46", "amount": 0.86, "date": "2021-08-04T00:00:00", "contact": "Amazon", "reference": "", "currency": "EUR"}, {"transaction_id": "eb74da67-a042-4239-be69-3d1ed92fc781", "amount": 431.28, "date": "2021-08-12T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "fd7778c1-35b0-44f6-a99b-cff902197ca8", "amount": 890.0, "date": "2021-08-12T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "3a515839-ef2c-43d1-bc0c-11f01c83eb6f", "amount": 4484.0, "date": "2021-08-12T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "95d000ab-3b49-4533-9950-803439991ab2", "amount": 1.0, "date": "2021-08-12T00:00:00", "contact": "Adobe", "reference": "", "currency": "EUR"}, {"transaction_id": "cba598f6-7ccd-4d81-a6e0-bee446207d7d", "amount": 12.78, "date": "2021-08-27T00:00:00", "contact": "<PERSON><PERSON>der", "reference": "", "currency": "EUR"}, {"transaction_id": "78c9480a-56f1-421d-bded-ce6a53f4afe9", "amount": 5.8, "date": "2021-09-04T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "e51219ce-c6aa-4690-8f43-d37e71a9b6a8", "amount": 5000.0, "date": "2021-09-10T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "8131c89e-0680-45f0-8871-4e0e551235f5", "amount": 7500.0, "date": "2021-11-08T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "279bef39-ce2d-4412-9add-f0f7797a01be", "amount": 936.76, "date": "2021-11-08T00:00:00", "contact": "<PERSON><PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "5120a5c8-1eca-4c76-9296-94f9ccf412ac", "amount": 6000.0, "date": "2021-12-10T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "742f8050-44f2-44ec-a68a-b7955a4aa3fb", "amount": 6000.0, "date": "2022-01-14T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "da25bc3c-c683-4621-afc9-259058e6ed97", "amount": 9000.0, "date": "2022-02-08T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "973871d4-3252-47b4-a9e5-61701a914ddd", "amount": 246.74, "date": "2022-03-10T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "202f6c80-4232-4ee0-a629-63948103a59e", "amount": 7000.0, "date": "2022-03-10T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "c0d9d4b5-d583-4bc5-9594-ef024007250b", "amount": 456.96, "date": "2022-03-18T00:00:00", "contact": "<PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "90fa00d1-c57e-4c2b-8048-3eb9c132946c", "amount": 457.08, "date": "2022-03-18T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "495a9efe-0490-43a5-bc16-782fd6e4cc05", "amount": 911.63, "date": "2022-03-18T00:00:00", "contact": "MOSTAFA ABED SALLOUM", "reference": "", "currency": "EUR"}, {"transaction_id": "94dec070-b705-4451-8438-a4afd4073eb9", "amount": 1373.72, "date": "2022-03-24T00:00:00", "contact": "<PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "188cdc4a-5134-473c-8762-87db8d737171", "amount": 8000.0, "date": "2022-04-08T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "b7d91c18-5bac-4cdc-bf38-127f97c36859", "amount": 2290.22, "date": "2022-04-14T00:00:00", "contact": "<PERSON><PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "300ff547-f2d7-496f-ac24-3ba7e7022345", "amount": 12.53, "date": "2022-04-14T00:00:00", "contact": "Transferwise", "reference": "", "currency": "EUR"}, {"transaction_id": "2466c2b1-0809-46fd-ad9d-443cb5338607", "amount": 0.29, "date": "2022-04-22T00:00:00", "contact": "Coin base", "reference": "", "currency": "EUR"}, {"transaction_id": "7101f39e-cf04-400d-b628-93ec88e15d1f", "amount": 9000.0, "date": "2022-05-01T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "e0d3704e-554a-4129-8e5c-9a6fddf58eda", "amount": 3573.29, "date": "2022-05-03T00:00:00", "contact": "<PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "df1060ba-7bbd-4fc5-bb59-7f645d6d92fc", "amount": 17.9, "date": "2022-05-03T00:00:00", "contact": "Transferwise", "reference": "", "currency": "EUR"}, {"transaction_id": "786cab0c-4afb-4e1f-9e0d-76849480a75a", "amount": 2300.0, "date": "2022-05-17T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "4c1699f8-5817-46ee-9041-1d4ca0cf1162", "amount": 5000.0, "date": "2022-06-01T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "0409597c-90bc-4df5-9da6-97ec03a4efd9", "amount": 5000.0, "date": "2022-06-02T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "55a0ec55-8390-4f2c-8694-4b5a4bb97ced", "amount": 5000.0, "date": "2022-06-09T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "68044ee2-a686-46ef-ae7d-cf63594e8d27", "amount": 2794.94, "date": "2022-06-09T00:00:00", "contact": "<PERSON><PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "81fe3a7e-66ec-4080-94d1-f848d1821654", "amount": 25.06, "date": "2022-06-09T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "37659106-3c1b-4d2a-a0a7-442b7fb208a2", "amount": 4000.0, "date": "2022-06-23T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "005b69fc-d14e-4267-8f2c-e6d652e7c45f", "amount": 5000.0, "date": "2022-07-05T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "b0005a10-18fd-48c6-8971-edbed4819f91", "amount": 6000.0, "date": "2022-07-21T00:00:00", "contact": "Bilal Itan", "reference": "", "currency": "EUR"}, {"transaction_id": "abab75e8-283d-4ccb-b1a6-9006a03dcf6d", "amount": 1500.0, "date": "2022-07-28T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "9ddcfcd7-6810-4a6f-a441-8679151b4a2e", "amount": 6000.0, "date": "2022-08-12T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "077e1e51-138a-4d49-9844-3eb0016740ee", "amount": 8000.0, "date": "2022-08-22T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "41eeff87-dc4c-46f7-904a-434fba96feec", "amount": 2517.62, "date": "2022-08-30T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "25bb8f6d-996c-4589-96d6-1fa9e0ad982c", "amount": 26.51, "date": "2022-08-30T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "a55ae937-d21d-46b7-a9f5-1aee26252eb8", "amount": 9000.0, "date": "2022-09-06T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "d828e0fd-b0f9-4be1-a938-4cab6db062b5", "amount": 9000.0, "date": "2022-09-14T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "80383f12-0332-4baf-bfa5-a2b51bc88891", "amount": 2500.0, "date": "2022-09-28T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "71977a9c-4e7d-4b6b-bdfc-00e37cf81178", "amount": 12000.0, "date": "2022-10-06T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "08082be4-25f6-4791-a0e6-c4b5bbd5177a", "amount": 15000.0, "date": "2022-10-20T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "550d00b1-5343-4360-81d2-7ac686cd9d66", "amount": 6000.0, "date": "2022-11-02T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "5b4f34cc-e13e-490e-bdc6-ed11370f653f", "amount": 4000.0, "date": "2022-11-11T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "ec56e587-7c72-4a32-893a-fdd0c34a3489", "amount": 5000.0, "date": "2022-11-18T00:00:00", "contact": "Bilal itani", "reference": "TRANSFER-541785867", "currency": "EUR"}, {"transaction_id": "824d86f1-f649-40b3-b30c-6040697c8bd7", "amount": 5500.0, "date": "2022-11-25T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "6ca1d9c9-3a8b-410d-8b6b-70e788d3a6e8", "amount": 0.55, "date": "2022-12-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "eaf4b07a-ad58-410e-9ff4-e2888718dbb4", "amount": 5000.0, "date": "2022-12-07T00:00:00", "contact": "Bilal itani", "reference": "TRANSFER-556218248", "currency": "EUR"}, {"transaction_id": "637e6c47-8536-4644-aa54-133b72cc8c2d", "amount": 1036.04, "date": "2022-12-07T00:00:00", "contact": "<PERSON><PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "7fef53a2-af82-4b3c-a55f-f733fc7039c9", "amount": 18.96, "date": "2022-12-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "34935810-3784-443c-821a-3db7d68736e7", "amount": 2297.72, "date": "2022-12-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "76cddae0-05a3-4929-96ad-3b8ac4cb9f03", "amount": 25.28, "date": "2022-12-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "41dcc802-ca36-4724-9b11-2942d29d2520", "amount": 9000.0, "date": "2022-12-21T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "089d434e-7b90-4e6e-87b7-d53ee36ff154", "amount": 0.44, "date": "2022-12-30T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "e383c853-a5b3-44c2-9aee-9452c5f12c27", "amount": 0.24, "date": "2023-01-01T00:00:00", "contact": "<PERSON>", "reference": "CARD_TRANSACTION_CASHBACK-190414", "currency": "EUR"}, {"transaction_id": "a0ef2f0a-bfd1-439a-92e4-f25b21cf3f50", "amount": 0.24, "date": "2023-02-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "824b2b03-5d6a-4985-ba5a-37ad18de8a12", "amount": 5000.0, "date": "2023-02-20T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "99dfb46d-ab81-4655-85c1-ef8caa80e840", "amount": 0.24, "date": "2023-03-01T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "4d4e7b0d-3d7c-40a8-a2af-14d76e28786f", "amount": 5200.0, "date": "2023-03-02T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "9cd47557-5134-46ae-8f55-1360f0162dbe", "amount": 0.08, "date": "2023-04-01T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "976e2e14-4cde-43c6-8014-2f8ee372f9d0", "amount": 5000.0, "date": "2023-04-04T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "6340d86f-6b76-4fbc-ba8a-01c552bca90e", "amount": 0.08, "date": "2023-05-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "40a7c38d-d45f-4901-bf1d-3aa140eb1291", "amount": 3500.0, "date": "2023-05-26T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "fd99b3b0-1900-4f5e-8535-ff69cd173efe", "amount": 0.08, "date": "2023-06-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "87775b33-39fa-4bde-868c-44025e229c35", "amount": 20000.0, "date": "2023-06-16T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "64819f87-2bc4-4e3b-b1e8-3cf980422e4a", "amount": 0.08, "date": "2023-07-03T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "dfdcd4b7-278a-418e-9b36-f27cdd4ebf66", "amount": 0.08, "date": "2023-08-01T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "8394db79-492b-4faa-af93-4b8e0b62989a", "amount": 0.08, "date": "2023-09-01T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "5c5ffa48-9b3f-46ee-971a-fc5870d64865", "amount": 20.0, "date": "2023-10-10T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "9f12c057-19af-49cb-a369-ecbc6552a894", "amount": 20.0, "date": "2023-10-11T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "a2a3adb7-c351-4c38-9dd0-adc38a1f175b", "amount": 45.0, "date": "2023-10-11T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "1e1668e4-0483-42ef-9298-43f3e49a333c", "amount": 0.05, "date": "2023-10-11T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "a5d1af7d-5a86-414c-b0f2-836b1a8acb0c", "amount": 25.0, "date": "2023-10-12T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "9b58c63d-a7a5-4267-9310-7367d9d85758", "amount": 70.0, "date": "2023-10-12T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "30e9c084-3e55-4d0b-9959-d32981f1f8c1", "amount": 25.0, "date": "2023-10-13T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "2f57b5cb-ac9b-451f-bcb8-171c54ee9366", "amount": 15.0, "date": "2023-10-14T00:00:00", "contact": "BD", "reference": "", "currency": "USD"}, {"transaction_id": "2b1240e4-6453-4f73-9662-008f5488f674", "amount": 2.76, "date": "2023-11-01T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "bec10e3c-731a-493b-af00-49d862533a01", "amount": 3500.0, "date": "2023-11-07T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "55d8be1a-30ae-490c-a12b-88dc45fd6b34", "amount": 11.6, "date": "2023-11-23T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "62fe8514-deb4-43ce-953d-05f9d47c585c", "amount": 1.14, "date": "2023-12-01T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "a4c16d09-d0f5-4f06-adee-2a5c80d47648", "amount": 0.98, "date": "2024-01-02T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "bcd940d1-a7ae-4f92-a73f-57c5d7b14fd7", "amount": 1000.0, "date": "2024-01-26T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "00c11f82-523f-4497-a0e0-2de9fb42f9aa", "amount": 5.7, "date": "2024-02-01T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "3f39eddf-0b67-4ad4-bb2b-d94def4a71c5", "amount": 4559.8, "date": "2024-02-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "6e4307c3-901e-4ed5-a495-d9831e1f5d04", "amount": 4544.23, "date": "2024-02-21T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "c9bb0256-78a0-4f74-9e55-ce739605d4c3", "amount": 2000.0, "date": "2024-02-21T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "9829b398-55a4-4af4-9a36-35a3f7bcc1d7", "amount": 0.23, "date": "2024-03-01T00:00:00", "contact": "Toters", "reference": "", "currency": "EUR"}, {"transaction_id": "9f037439-0aad-4423-aa39-7e244bf7c072", "amount": 23.55, "date": "2024-03-01T00:00:00", "contact": "Card cash back", "reference": "", "currency": "USD"}, {"transaction_id": "de82c458-ad5e-4c71-abfe-cb221483b09d", "amount": 31733.88, "date": "2024-03-05T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "7b970ea7-c5a5-45f0-97ee-872d7b8105c6", "amount": 23.85, "date": "2024-03-08T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "78f51720-328d-4d85-a786-2ec635bb49ec", "amount": 35.0, "date": "2024-03-10T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "8e9bd737-fdb8-47d0-a03f-a02ef7439ae4", "amount": 22.46, "date": "2024-04-02T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "ae424659-6ada-4b36-8357-e1c549adb696", "amount": 31620.25, "date": "2024-04-04T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "9f8f57ae-871a-4ec1-84f7-590b12bb0311", "amount": 1.0, "date": "2024-04-04T00:00:00", "contact": "Mongodb", "reference": "", "currency": "USD"}, {"transaction_id": "9ddcbc64-30ee-45da-99e5-0be45fc67332", "amount": 19.87, "date": "2024-04-21T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "9c96eeaa-5603-4229-ba64-b770ac28155c", "amount": 22.63, "date": "2024-05-01T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "3b472580-a9af-4cfc-abe5-6005ca535f58", "amount": 31403.5, "date": "2024-05-06T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "2dcfbdae-7064-4fde-bc10-dc2ad896510d", "amount": 16.89, "date": "2024-06-01T00:00:00", "contact": "Patzilla.com", "reference": "", "currency": "USD"}, {"transaction_id": "b833c0e9-c951-44bf-bbba-c46d6fe68650", "amount": 23.4, "date": "2024-06-03T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "bb619fe7-bfd1-47f7-9d7a-a5075a903acb", "amount": 32.26, "date": "2024-06-03T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "977291ae-836b-4f5a-a301-0012c6642454", "amount": 21.53, "date": "2024-06-04T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "e8b185ae-40fe-47e3-a858-db54a3f4d898", "amount": 800.0, "date": "2024-06-04T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "3e90886d-2a60-41be-82b6-f326e1c11932", "amount": 31980.0, "date": "2024-06-06T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "344ac7bd-ce4b-49c8-9cc8-6197bfe7e5ea", "amount": 1.89, "date": "2024-06-18T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "ba70a555-382b-48ce-9c1e-cd0320b99f50", "amount": 16.45, "date": "2024-06-20T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "8268707e-8740-403e-a49c-f62c4d5d79a1", "amount": 44.4, "date": "2024-07-01T00:00:00", "contact": "TBA", "reference": "", "currency": "USD"}, {"transaction_id": "7f830f02-7ecc-4be5-a4c0-bdfd5575b96e", "amount": 1.76, "date": "2024-07-01T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "c96c828e-25f3-49a7-b085-b4d553f38f83", "amount": 31679.12, "date": "2024-07-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "ee608116-5b2c-4175-ba4c-5bd8d221154f", "amount": 60.3, "date": "2024-07-09T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "df4dd5f3-b40c-4150-ad2a-7a9e95c62bb1", "amount": 22.07, "date": "2024-07-14T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "8db8e04c-de6d-417d-87c3-736c65b5566b", "amount": 0.03, "date": "2024-07-22T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "b7017a2c-6419-4649-961e-f9f88bae56b6", "amount": 1.1, "date": "2024-07-25T00:00:00", "contact": "AWS", "reference": "", "currency": "USD"}, {"transaction_id": "1cc6e415-e20b-4a0e-a241-869738db21bc", "amount": 30.92, "date": "2024-08-01T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "581085ea-d142-4a68-a490-f22bb12ec0be", "amount": 4.91, "date": "2024-08-01T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "51832eb9-2efc-4aef-8375-d966a9717eac", "amount": 8.99, "date": "2024-08-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "EUR"}, {"transaction_id": "25b2fdaa-e537-4613-bbdd-296911674299", "amount": 31695.72, "date": "2024-08-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "43f7284b-a5a0-4b49-a77f-834c96e401e3", "amount": 73.82, "date": "2024-08-08T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "dcd0aaa5-9876-4f5a-b228-40c78f455bc2", "amount": 15.75, "date": "2024-08-24T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "2c452194-be40-4110-a369-a386b69bb7f7", "amount": 41.79, "date": "2024-09-02T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "e94c920a-a0b1-4d1f-8b9e-abbfcbae9155", "amount": 6.17, "date": "2024-09-02T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "f579ad44-aa9c-4c4d-8c63-f6899f108dad", "amount": 0.14, "date": "2024-09-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "b5c2712d-480d-4ee6-a89b-f2093fc57ded", "amount": 0.09, "date": "2024-09-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "6c0f5e1d-615d-427a-9594-7e9176130eb8", "amount": 5000.0, "date": "2024-09-06T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "b9a78dd0-0bd5-4b90-a2d9-4744fcda092d", "amount": 16.42, "date": "2024-09-06T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "84e1bc9c-2021-4b19-865d-6c7a46a95e38", "amount": 65.87, "date": "2024-09-11T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "87f269ae-2d93-489b-abda-c6c261785c15", "amount": 1.53, "date": "2024-09-14T00:00:00", "contact": "Amazon", "reference": "", "currency": "USD"}, {"transaction_id": "4f72ad73-1bb2-4bf7-8b2a-c959e416cfcc", "amount": 4.33, "date": "2024-10-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "44093cb7-c7a3-43bd-9c9f-7cb69ef4f482", "amount": 33.01, "date": "2024-10-01T00:00:00", "contact": "TBA", "reference": "", "currency": "USD"}, {"transaction_id": "cd130891-ba2d-48f3-9497-5c4f5bc69281", "amount": 2500.0, "date": "2024-10-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "75afca60-0445-4ead-bc55-51341fb4e2e0", "amount": 1500.0, "date": "2024-10-23T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "7ae69c4b-351e-474d-a861-06511161b4ab", "amount": 1000.0, "date": "2024-11-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "a226c4a4-2300-47ca-86d5-18d6e2580e45", "amount": 3.3, "date": "2024-11-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "50c5fdea-5f44-4552-a0b5-9d096b4fbcc4", "amount": 1.06, "date": "2024-11-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "b4f24c1d-adea-4d88-9ca1-369bc73700d4", "amount": 2000.0, "date": "2024-11-07T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "04953e90-4082-446b-833f-fec1cc925a40", "amount": 2500.0, "date": "2024-11-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "83a2494b-dc14-4443-98fb-de60400308e6", "amount": 3134.0, "date": "2024-11-07T00:00:00", "contact": "<PERSON> - Sin Dental", "reference": "", "currency": "GBP"}, {"transaction_id": "2c14e39f-3816-4039-8483-b4aa4dcd4b68", "amount": 1500.0, "date": "2024-11-19T00:00:00", "contact": "UPOD MEDICAL LTD", "reference": "Prepayment", "currency": "GBP"}, {"transaction_id": "2fbcab67-b6fe-44b9-ba42-bf877b7d0720", "amount": 4412.8, "date": "2024-11-20T00:00:00", "contact": "UPOD MEDICAL LTD", "reference": "50% upfront payment", "currency": "GBP"}, {"transaction_id": "462b002a-5233-4fe8-adc4-afb1558730d0", "amount": 23.8, "date": "2024-11-24T00:00:00", "contact": "Total", "reference": "", "currency": "GBP"}, {"transaction_id": "7d85e786-cd5f-4494-b98d-8bdb78285260", "amount": 0.26, "date": "2024-12-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "02e3565a-4179-49a3-8d41-afec4efe2408", "amount": 500.0, "date": "2024-12-02T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "EUR"}, {"transaction_id": "13e93b08-a8b6-4596-bec9-b8fd740e1905", "amount": 17.29, "date": "2024-12-02T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "21975207-f0e3-4c90-a256-abe561b6d563", "amount": 7500.0, "date": "2024-12-05T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "3f945e75-b3f1-4c91-a5c1-5356dcbbf6c7", "amount": 34.03, "date": "2024-12-27T00:00:00", "contact": "RJ", "reference": "", "currency": "GBP"}, {"transaction_id": "97ef3365-70b6-4a2a-9874-7ecf3ee161cc", "amount": 3.97, "date": "2025-01-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "32eebcee-bf92-459a-a350-e033248875fb", "amount": 1.47, "date": "2025-01-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "f48c1a6b-2a31-411b-abf3-58ed03b75f74", "amount": 2500.0, "date": "2025-01-07T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "b00d50a2-70d1-4c44-b447-29c9eb22de5e", "amount": 59.82, "date": "2025-01-07T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "2dca32e1-e0ae-4c5f-8973-181689e174f1", "amount": 5000.0, "date": "2025-01-29T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "ef0a4e9a-fb16-4863-8116-1de82297bcce", "amount": 3.27, "date": "2025-02-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "8f4bc4b8-1e9c-452f-a0b6-9b1a3348a59d", "amount": 0.19, "date": "2025-02-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "02597e1c-0f44-4eae-9855-235810db3761", "amount": 20.0, "date": "2025-02-09T00:00:00", "contact": "A.station", "reference": "", "currency": "USD"}, {"transaction_id": "87cf810e-3b98-4c56-9636-6f2400a52850", "amount": 1.0, "date": "2025-02-10T00:00:00", "contact": "Mongodb", "reference": "", "currency": "USD"}, {"transaction_id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "amount": 1023.53, "date": "2025-02-13T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "GBP"}, {"transaction_id": "66128823-4133-48d8-b0e1-5dc0579aa72b", "amount": 8.32, "date": "2025-02-16T00:00:00", "contact": "Toters", "reference": "", "currency": "USD"}, {"transaction_id": "bda06d26-03e4-4133-a404-01d7254c7fbc", "amount": 1.57, "date": "2025-02-16T00:00:00", "contact": "Toters", "reference": "", "currency": "GBP"}, {"transaction_id": "0f89af09-0bec-4148-b26d-fd509efbd9b7", "amount": 313.13, "date": "2025-02-22T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "GBP"}, {"transaction_id": "c5da908c-7cf9-4973-a5eb-79a00b091842", "amount": 2500.0, "date": "2025-02-24T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "7d6f9a5b-75ed-4872-a7cc-216c6ed651b4", "amount": 234.19, "date": "2025-02-28T00:00:00", "contact": "Bilal itani", "reference": "", "currency": "GBP"}, {"transaction_id": "65f1d723-224c-482f-9c27-093c376ac14f", "amount": 101.12, "date": "2025-03-03T00:00:00", "contact": "TBA", "reference": "", "currency": "GBP"}, {"transaction_id": "7ccf7c41-f08e-4e2a-a8e0-52bc1c9ffe3a", "amount": 8.73, "date": "2025-03-03T00:00:00", "contact": "TBA", "reference": "", "currency": "USD"}, {"transaction_id": "d4bc39df-1cc5-4103-8fdc-35e468b63fa4", "amount": 3.03, "date": "2025-03-03T00:00:00", "contact": "TBA", "reference": "", "currency": "USD"}, {"transaction_id": "51ecee0a-394b-4013-92e9-05ab52dca2d5", "amount": 2500.0, "date": "2025-03-20T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "3b45df8f-af7e-40d2-86b0-a11b33ed40f7", "amount": 1.54, "date": "2025-04-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "6440384c-bab6-436f-85a6-1b29f423f31d", "amount": 0.04, "date": "2025-04-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "35fcd353-df19-4587-a96a-f076b2a9505f", "amount": 106.38, "date": "2025-04-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "c3907779-e986-430a-a0d5-77e8559bacff", "amount": 101.31, "date": "2025-04-03T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "0def9c26-c86a-4640-9a10-7c61f25ba25b", "amount": 101.31, "date": "2025-04-05T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "c2579a0f-cf85-4bc0-a170-5133feeb807c", "amount": 101.31, "date": "2025-04-10T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "16ca73cc-e2dd-4a69-9e9e-38aae3f8c090", "amount": 35.0, "date": "2025-04-14T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "05206999-8258-4ba3-900f-78a2e8134394", "amount": 2700.0, "date": "2025-04-17T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "3ca31ba1-d40c-443b-87a3-a2f27b10492e", "amount": 2.28, "date": "2025-07-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "61399774-df70-4756-8c24-3f0c61dd3dcd", "amount": 10.13, "date": "2025-07-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "b60d3736-14a1-4a2d-9f49-2d2cc3d3cde7", "amount": 4.46, "date": "2025-07-01T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "GBP"}, {"transaction_id": "151afc4d-02fa-4721-9b86-b90e9c4c3866", "amount": 2961.47, "date": "2025-07-15T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}, {"transaction_id": "4184cd05-da23-4fc2-a176-c51b920fb414", "amount": 2700.0, "date": "2025-07-21T00:00:00", "contact": "<PERSON>", "reference": "", "currency": "USD"}], "summary": {"total_payments": 102, "total_matched": 59, "total_unmatched": 43, "match_rate": 57.**************, "average_confidence": 0.****************, "confidence_distribution": {"high": 2, "medium": 57}, "most_used_rules": [["recurring_payment_pattern", 57], ["invoice_ref_in_bank", 1], ["exact_amount_near_date", 1]]}}
#!/usr/bin/env python3
"""
Resume Extraction Script
Continues extraction from where it left off, handling rate limits gracefully
"""

import sys
import json
from pathlib import Path
from datetime import datetime, timezone, timedelta
import argparse

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from lib.xero_client import XeroClient
from lib.data_extractors import DataExtractors
from lib.utils import save_structured_data, create_metadata

class ResumeExtractor:
    """Handle resuming interrupted extractions"""
    
    def __init__(self):
        """Initialize the resume extractor"""
        self.client = XeroClient()
        self.data_dir = Path(__file__).parent.parent / "data"
        self.extractor = DataExtractors(self.client, self.data_dir)
        self.checkpoint_file = self.data_dir / "extraction_checkpoint.json"
        
    def load_checkpoint(self) -> dict:
        """Load the last checkpoint if it exists"""
        if self.checkpoint_file.exists():
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        return {}
    
    def save_checkpoint(self, checkpoint_data: dict):
        """Save checkpoint data"""
        checkpoint_data['last_updated'] = datetime.now(timezone.utc).isoformat()
        save_structured_data(checkpoint_data, self.checkpoint_file)
        print(f"✅ Checkpoint saved at {datetime.now().strftime('%H:%M:%S')}")
    
    def get_latest_transaction_date(self) -> str:
        """Get the latest bank transaction date from existing data"""
        bank_file = self.data_dir / "core" / "bank_transactions.json"
        if not bank_file.exists():
            return "2020-01-01"
            
        with open(bank_file, 'r') as f:
            data = json.load(f)
            
        transactions = data.get('bankTransactions', [])
        if not transactions:
            return "2020-01-01"
            
        # Find the latest transaction date
        latest_date = None
        for txn in transactions:
            if 'Date' in txn:
                date_str = txn['Date']
                if date_str.startswith('/Date('):
                    timestamp = int(date_str[6:-7]) / 1000
                    date = datetime.fromtimestamp(timestamp)
                    if latest_date is None or date > latest_date:
                        latest_date = date
        
        if latest_date:
            # Add one day to start from the next day
            next_day = latest_date + timedelta(days=1)
            return next_day.strftime('%Y-%m-%d')
        
        return "2020-01-01"
    
    def merge_transactions(self, existing_txns: list, new_txns: list) -> list:
        """Merge new transactions with existing ones, avoiding duplicates"""
        # Create a set of existing transaction IDs
        existing_ids = {txn['BankTransactionID'] for txn in existing_txns if 'BankTransactionID' in txn}
        
        # Add new transactions that aren't duplicates
        merged = existing_txns.copy()
        added_count = 0
        
        for txn in new_txns:
            if 'BankTransactionID' in txn and txn['BankTransactionID'] not in existing_ids:
                merged.append(txn)
                added_count += 1
        
        print(f"   Added {added_count} new transactions (skipped {len(new_txns) - added_count} duplicates)")
        return merged
    
    def resume_bank_transactions(self, from_date: str = None):
        """Resume bank transaction extraction from where it left off"""
        print("\n📊 Resuming Bank Transaction Extraction")
        print("=" * 50)
        
        # Determine the starting date
        if not from_date:
            from_date = self.get_latest_transaction_date()
        
        print(f"📅 Starting from: {from_date}")
        
        # Load existing data
        bank_file = self.data_dir / "core" / "bank_transactions.json"
        existing_data = {}
        existing_txns = []
        
        if bank_file.exists():
            with open(bank_file, 'r') as f:
                existing_data = json.load(f)
                existing_txns = existing_data.get('bankTransactions', [])
                print(f"📂 Existing transactions: {len(existing_txns)}")
        
        # Extract new transactions
        print(f"\n🔍 Fetching transactions from {from_date} onwards...")
        
        extra_params = {'ModifiedAfter': from_date}
        new_txns = self.client.get_paginated_data('BankTransactions', extra_params=extra_params)
        
        if new_txns:
            print(f"✅ Retrieved {len(new_txns)} transactions")
            
            # Merge with existing data
            merged_txns = self.merge_transactions(existing_txns, new_txns)
            
            # Sort by date
            def get_txn_date(txn):
                if 'Date' in txn and txn['Date'].startswith('/Date('):
                    return int(txn['Date'][6:-7])
                return 0
            
            merged_txns.sort(key=get_txn_date)
            
            # Prepare the complete data structure
            complete_data = {
                "metadata": create_metadata(
                    "Bank Transactions",
                    self.client.organization_name,
                    self.client.tenant_id,
                    len(merged_txns)
                ),
                "bankTransactions": merged_txns,
                "summary": {
                    "total_count": len(merged_txns),
                    "date_range": {
                        "start": self._get_date_string(merged_txns[0]) if merged_txns else None,
                        "end": self._get_date_string(merged_txns[-1]) if merged_txns else None
                    }
                }
            }
            
            # Save the updated data
            if save_structured_data(complete_data, bank_file):
                print(f"✅ Total transactions saved: {len(merged_txns)}")
                return True
        else:
            print("ℹ️  No new transactions found")
            return True
            
        return False
    
    def _get_date_string(self, txn: dict) -> str:
        """Extract date string from transaction"""
        if 'Date' in txn and txn['Date'].startswith('/Date('):
            timestamp = int(txn['Date'][6:-7]) / 1000
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
        return None
    
    def show_rate_limit_status(self):
        """Display current rate limit status"""
        stats = self.client.get_rate_limit_stats()
        print(f"\n📊 Rate Limit Status:")
        print(f"   Requests made: {stats['requests_made']}/{stats['rate_limit_per_minute']}")
        print(f"   Requests remaining: {stats['requests_remaining']}")
        print(f"   Window resets in: {stats['window_seconds_remaining']:.0f} seconds")
    
    def run(self, target: str = 'bank_transactions', from_date: str = None):
        """Run the resume extraction"""
        print(f"\n🚀 Xero Resume Extraction Tool")
        print(f"   Organization: {self.client.organization_name}")
        print(f"   Target: {target}")
        
        # Check authentication
        if not self.client.is_authenticated():
            print("❌ Not authenticated. Please run authentication first.")
            return False
        
        # Check token expiry
        token_info = self.client.get_token_info()
        if token_info['minutes_remaining'] < 5:
            print(f"⚠️  Token expires in {token_info['minutes_remaining']} minutes. Please refresh authentication.")
            return False
        
        print(f"✅ Token valid for {token_info['minutes_remaining']} minutes")
        
        # Run extraction based on target
        if target == 'bank_transactions':
            success = self.resume_bank_transactions(from_date)
        else:
            print(f"❌ Unsupported target: {target}")
            return False
        
        # Show final rate limit status
        self.show_rate_limit_status()
        
        return success

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Resume Xero data extraction')
    parser.add_argument('--target', default='bank_transactions', 
                       help='What to extract (default: bank_transactions)')
    parser.add_argument('--from-date', help='Override start date (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    extractor = ResumeExtractor()
    success = extractor.run(args.target, args.from_date)
    
    if success:
        print("\n✅ Resume extraction completed successfully!")
    else:
        print("\n❌ Resume extraction failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
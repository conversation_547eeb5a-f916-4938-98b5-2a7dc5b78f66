#!/usr/bin/env python3
"""
Extract Historical Xero Data (2020 onwards)
Specialized script for extracting all invoices and bank transactions from 2020
"""

import sys
import json
from datetime import datetime
from pathlib import Path
import argparse

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from lib.xero_client import XeroClient
from lib.data_extractors import DataExtractors
from lib.utils import save_structured_data, create_metadata

class HistoricalDataExtractor:
    """
    Specialized extractor for historical data from 2020 onwards
    """
    
    def __init__(self, data_dir: Path = None):
        """Initialize historical data extractor"""
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = data_dir or self.base_dir / "data" / "historical"
        self.from_date = "2020-01-01"
        
        # Initialize client and extractors
        print("🚀 MCX3D Financials - Historical Data Extractor")
        print("=" * 50)
        print(f"📅 Extracting data from: {self.from_date}")
        print("=" * 50)
        
        self.client = XeroClient()
        if not self.client.is_authenticated():
            print("❌ Authentication required. Please run:")
            print("   python auth/oauth2_step1.py")
            print("   python auth/oauth2_step2.py")
            sys.exit(1)
        
        self.extractors = DataExtractors(self.client, self.data_dir)
        
        # Display connection info
        token_info = self.client.get_token_info()
        print(f"✅ Authenticated as: {token_info['organization']}")
        print(f"   Token expires in: {token_info['minutes_remaining']} minutes")
        
        # Create historical data directory
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def extract_historical_invoices(self) -> dict:
        """Extract all invoices from 2020 onwards"""
        print(f"\n🧾 Extracting invoices from {self.from_date}...")
        
        try:
            # Use ModifiedAfter to get all invoices modified since 2020
            extra_params = {'ModifiedAfter': self.from_date}
            
            # Get all invoices
            all_invoices = []
            page = 1
            
            while True:
                params = {'page': page}
                params.update(extra_params)
                
                response = self.client.make_request("Invoices", params=params)
                
                if not response or 'Invoices' not in response:
                    break
                
                invoices = response['Invoices']
                if not invoices:
                    break
                
                all_invoices.extend(invoices)
                print(f"   Page {page}: Retrieved {len(invoices)} invoices (Total: {len(all_invoices)})")
                
                # Check if we have more pages
                if len(invoices) < 100:  # Default page size
                    break
                
                page += 1
            
            # Filter by date if needed (in case ModifiedAfter doesn't work as expected)
            filtered_invoices = []
            for invoice in all_invoices:
                invoice_date = invoice.get('Date') or invoice.get('DateString')
                if invoice_date:
                    # Parse date and check if it's after our from_date
                    filtered_invoices.append(invoice)
            
            print(f"✅ Total invoices retrieved: {len(filtered_invoices)}")
            
            # Analyze date range
            if filtered_invoices:
                dates = []
                for inv in filtered_invoices:
                    date_str = inv.get('Date') or inv.get('DateString')
                    if date_str and '/' in date_str:
                        # Parse Xero date format
                        import re
                        match = re.search(r'/Date\((\d+)', date_str)
                        if match:
                            timestamp = int(match.group(1)) / 1000
                            dates.append(datetime.fromtimestamp(timestamp))
                
                if dates:
                    min_date = min(dates)
                    max_date = max(dates)
                    print(f"   Date range: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
            
            return {
                "count": len(filtered_invoices),
                "invoices": filtered_invoices,
                "extraction_date": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error extracting invoices: {e}")
            return {"count": 0, "invoices": [], "error": str(e)}
    
    def extract_historical_bank_transactions(self) -> dict:
        """Extract all bank transactions from 2020 onwards"""
        print(f"\n🏦 Extracting bank transactions from {self.from_date}...")
        
        try:
            # Use ModifiedAfter to get all transactions modified since 2020
            extra_params = {'ModifiedAfter': self.from_date}
            
            # Get all bank transactions
            all_transactions = []
            page = 1
            
            while True:
                params = {'page': page}
                params.update(extra_params)
                
                response = self.client.make_request("BankTransactions", params=params)
                
                if not response or 'BankTransactions' not in response:
                    break
                
                transactions = response['BankTransactions']
                if not transactions:
                    break
                
                all_transactions.extend(transactions)
                print(f"   Page {page}: Retrieved {len(transactions)} transactions (Total: {len(all_transactions)})")
                
                # Check if we have more pages
                if len(transactions) < 100:  # Default page size
                    break
                
                page += 1
            
            print(f"✅ Total bank transactions retrieved: {len(all_transactions)}")
            
            # Analyze date range
            if all_transactions:
                dates = []
                for txn in all_transactions:
                    date_str = txn.get('Date') or txn.get('DateString')
                    if date_str and '/' in date_str:
                        # Parse Xero date format
                        import re
                        match = re.search(r'/Date\((\d+)', date_str)
                        if match:
                            timestamp = int(match.group(1)) / 1000
                            dates.append(datetime.fromtimestamp(timestamp))
                
                if dates:
                    min_date = min(dates)
                    max_date = max(dates)
                    print(f"   Date range: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
            
            return {
                "count": len(all_transactions),
                "transactions": all_transactions,
                "extraction_date": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error extracting bank transactions: {e}")
            return {"count": 0, "transactions": [], "error": str(e)}
    
    def save_results(self, invoice_data: dict, transaction_data: dict):
        """Save extraction results"""
        print("\n💾 Saving results...")
        
        # Save invoices
        invoice_file = self.data_dir / "invoices_2020_onwards.json"
        invoice_metadata = create_metadata(
            "Historical Invoices (2020 onwards)",
            self.client.organization_name,
            self.client.tenant_id,
            invoice_data["count"],
            {"from_date": self.from_date}
        )
        
        invoice_output = {
            "metadata": invoice_metadata,
            "summary": {
                "total_invoices": invoice_data["count"],
                "extraction_date": invoice_data["extraction_date"],
                "from_date": self.from_date
            },
            "invoices": invoice_data["invoices"]
        }
        
        if save_structured_data(invoice_output, invoice_file):
            print(f"✅ Invoices saved: {invoice_file}")
        
        # Save bank transactions
        transaction_file = self.data_dir / "bank_transactions_2020_onwards.json"
        transaction_metadata = create_metadata(
            "Historical Bank Transactions (2020 onwards)",
            self.client.organization_name,
            self.client.tenant_id,
            transaction_data["count"],
            {"from_date": self.from_date}
        )
        
        transaction_output = {
            "metadata": transaction_metadata,
            "summary": {
                "total_transactions": transaction_data["count"],
                "extraction_date": transaction_data["extraction_date"],
                "from_date": self.from_date
            },
            "bankTransactions": transaction_data["transactions"]
        }
        
        if save_structured_data(transaction_output, transaction_file):
            print(f"✅ Bank transactions saved: {transaction_file}")
        
        # Create summary report
        summary = {
            "extraction_summary": {
                "extraction_date": datetime.now().isoformat(),
                "from_date": self.from_date,
                "organization": self.client.organization_name,
                "results": {
                    "invoices": {
                        "count": invoice_data["count"],
                        "file": str(invoice_file.name)
                    },
                    "bank_transactions": {
                        "count": transaction_data["count"],
                        "file": str(transaction_file.name)
                    }
                }
            }
        }
        
        summary_file = self.data_dir / "extraction_summary.json"
        if save_structured_data(summary, summary_file):
            print(f"✅ Summary saved: {summary_file}")
    
    def run(self):
        """Run the historical data extraction"""
        start_time = datetime.now()
        print(f"\n⏱️ Starting extraction at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test connection
        if not self.client.test_connection():
            print("❌ Connection test failed. Cannot proceed.")
            return False
        
        # Extract invoices
        invoice_data = self.extract_historical_invoices()
        
        # Extract bank transactions
        transaction_data = self.extract_historical_bank_transactions()
        
        # Save results
        self.save_results(invoice_data, transaction_data)
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        print("🎉 HISTORICAL DATA EXTRACTION COMPLETE")
        print("=" * 50)
        print(f"📋 Invoices extracted: {invoice_data['count']}")
        print(f"🏦 Bank transactions extracted: {transaction_data['count']}")
        print(f"⏱️ Duration: {duration}")
        print(f"📁 Data location: {self.data_dir}")
        
        return True


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Extract historical Xero data from 2020 onwards")
    
    parser.add_argument(
        "--data-dir",
        type=Path,
        help="Custom data directory (default: ./data/historical)"
    )
    
    parser.add_argument(
        "--from-date",
        type=str,
        default="2020-01-01",
        help="Start date for extraction (default: 2020-01-01)"
    )
    
    args = parser.parse_args()
    
    try:
        extractor = HistoricalDataExtractor(data_dir=args.data_dir)
        if args.from_date:
            extractor.from_date = args.from_date
        
        success = extractor.run()
        
        if success:
            print("\n✅ Historical data extraction completed successfully!")
        else:
            print("\n❌ Historical data extraction failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Extraction interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
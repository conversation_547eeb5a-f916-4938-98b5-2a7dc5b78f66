#!/usr/bin/env python3
"""
Master Xero Data Extraction Script
Single command to extract all available Xero data for MCX3D LTD
"""

import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from lib.xero_client import XeroClient
from lib.data_extractors import DataExtractors
from lib.utils import get_data_inventory, save_structured_data

class MasterExtractor:
    """
    Master extraction coordinator that orchestrates all data extraction
    """
    
    def __init__(self, data_dir: Path = None):
        """Initialize master extractor"""
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = data_dir or self.base_dir / "data"
        
        # Initialize client and extractors
        print("🚀 MCX3D Financials - Master Xero Data Extractor v2.0")
        print("=" * 65)
        
        self.client = XeroClient()
        if not self.client.is_authenticated():
            print("❌ Authentication required. Please run:")
            print("   python auth/oauth2_step1.py")
            print("   python auth/oauth2_step2.py")
            sys.exit(1)
        
        self.extractors = DataExtractors(self.client, self.data_dir)
        
        # Display connection info
        token_info = self.client.get_token_info()
        print(f"✅ Authenticated as: {token_info['organization']}")
        print(f"   Token expires in: {token_info['minutes_remaining']} minutes")
        print(f"   Scopes: {', '.join(token_info['scopes'])}")
    
    def extract_core_data(self, from_date: str = None) -> bool:
        """Extract all core business data with optional date filtering
        
        Args:
            from_date: Start date for filtering (YYYY-MM-DD format)
        """
        print(f"\n🏢 === EXTRACTING CORE BUSINESS DATA{' FROM ' + from_date if from_date else ''} ===")
        
        extractors = [
            ("Organization Details", self.extractors.extract_organization),
            ("Chart of Accounts", self.extractors.extract_accounts),
            ("Contacts", self.extractors.extract_contacts),
            ("Invoices", lambda: self.extractors.extract_invoices(from_date)),
            ("Bank Transactions", lambda: self.extractors.extract_bank_transactions(from_date))
        ]
        
        success_count = 0
        for name, extractor_func in extractors:
            print(f"\n📋 {name}")
            if extractor_func():
                success_count += 1
                print(f"✅ {name} completed successfully")
            else:
                print(f"❌ {name} failed")
        
        success_rate = (success_count / len(extractors)) * 100
        print(f"\n📊 Core Data Summary: {success_count}/{len(extractors)} successful ({success_rate:.1f}%)")
        
        return success_count == len(extractors)
    
    def extract_financial_reports(self, years: list = None) -> bool:
        """Extract comprehensive financial reports"""
        print("\n📈 === EXTRACTING FINANCIAL REPORTS ===")
        
        if years is None:
            years = [2020, 2021, 2022, 2023, 2024, 2025]
        
        print(f"📅 Processing years: {', '.join(map(str, years))}")
        success = self.extractors.extract_financial_reports(years)
        
        if success:
            print("✅ Financial reports completed successfully")
        else:
            print("❌ Some financial reports failed")
        
        return success
    
    def extract_transaction_data(self) -> bool:
        """Extract transaction and payment data"""
        print("\n💰 === EXTRACTING TRANSACTION DATA ===")
        
        print("\n📋 Payments")
        success = self.extractors.extract_payments()
        
        if success:
            print("✅ Transaction data completed successfully")
        else:
            print("❌ Transaction data extraction failed")
        
        return success
    
    def extract_supplementary_data(self) -> bool:
        """Extract all supplementary business data"""
        print("\n📁 === EXTRACTING SUPPLEMENTARY DATA ===")
        
        success = self.extractors.extract_supplementary_data()
        
        if success:
            print("✅ Supplementary data completed successfully")
        else:
            print("❌ Some supplementary data extractions failed")
        
        return success
    
    def extract_additional_reports(self) -> bool:
        """Extract additional executive reports"""
        print("\n📊 === EXTRACTING ADDITIONAL REPORTS ===")
        
        additional_reports = [
            ("Bank Summary", "Reports/BankSummary", "executive"),
            ("Budget Summary", "Reports/BudgetSummary", "executive"),
            ("Executive Summary", "Reports/ExecutiveSummary", "executive")
        ]
        
        success_count = 0
        
        for report_name, endpoint, category in additional_reports:
            print(f"  📈 Extracting {report_name}...")
            
            try:
                response = self.client.make_request(endpoint)
                
                if response:
                    # Structure the data
                    report_data = {
                        "metadata": {
                            "reportType": report_name,
                            "extractedDate": datetime.now().isoformat(),
                            "organisation": self.client.organization_name,
                            "tenantId": self.client.tenant_id
                        },
                        "report": response.get('Reports', [{}])[0] if response.get('Reports') else response
                    }
                    
                    # Save to file
                    filename = report_name.lower().replace(' ', '-')
                    filepath = self.data_dir / "reports" / category / f"{filename}.json"
                    
                    if save_structured_data(report_data, filepath):
                        print(f"    ✅ {report_name} saved")
                        success_count += 1
                    else:
                        print(f"    ❌ Failed to save {report_name}")
                else:
                    print(f"    ❌ No data returned for {report_name}")
                    
            except Exception as e:
                print(f"    ❌ Error extracting {report_name}: {e}")
        
        success_rate = (success_count / len(additional_reports)) * 100
        print(f"\n📊 Additional Reports Summary: {success_count}/{len(additional_reports)} successful ({success_rate:.1f}%)")
        
        return success_count == len(additional_reports)
    
    def generate_final_summary(self) -> bool:
        """Generate comprehensive extraction summary"""
        print("\n📋 === GENERATING FINAL SUMMARY ===")
        
        try:
            # Save extraction log
            self.extractors.save_extraction_log()
            
            # Generate data inventory
            inventory = get_data_inventory(self.data_dir)
            inventory_path = self.data_dir / "summaries" / "data_inventory.json"
            save_structured_data(inventory, inventory_path)
            
            # Create master summary
            master_summary = {
                "extraction_completed": datetime.now().isoformat(),
                "organization": self.client.organization_name,
                "tenant_id": self.client.tenant_id,
                "version": "2.0.0",
                "data_inventory": inventory,
                "extraction_statistics": {
                    "total_files": inventory["total_files"],
                    "total_size_mb": round(inventory["total_size_mb"], 2),
                    "categories": len(inventory["categories"]),
                    "extraction_duration": "N/A"  # Could be calculated if needed
                },
                "next_steps": [
                    "Review data inventory in summaries/data_inventory.json",
                    "Check extraction log in summaries/extraction_log.json", 
                    "Use data files for financial analysis and reporting",
                    "Re-run authentication if tokens expire (30 minutes)"
                ]
            }
            
            summary_path = self.data_dir / "summaries" / "master_summary.json"
            save_structured_data(master_summary, summary_path)
            
            print("✅ Final summary generated successfully")
            print(f"   📁 Data Inventory: {inventory_path}")
            print(f"   📋 Extraction Log: {self.data_dir / 'summaries' / 'extraction_log.json'}")
            print(f"   📊 Master Summary: {summary_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error generating final summary: {e}")
            return False
    
    def run_complete_extraction(self, years: list = None, skip_existing: bool = False, from_date: str = None) -> bool:
        """
        Run complete data extraction process
        
        Args:
            years: Years for financial reports (default: 2020-2025)
            skip_existing: Whether to skip existing data files
            from_date: Start date for filtering transactions (YYYY-MM-DD format)
            
        Returns:
            bool: True if all extractions successful
        """
        start_time = datetime.now()
        print(f"🚀 Starting complete Xero data extraction at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if from_date:
            print(f"📅 Filtering transactions from: {from_date}")
        
        # Test connection first
        if not self.client.test_connection():
            print("❌ Connection test failed. Cannot proceed with extraction.")
            return False
        
        extraction_results = []
        
        # Core business data
        extraction_results.append(("Core Data", self.extract_core_data(from_date)))
        
        # Financial reports
        extraction_results.append(("Financial Reports", self.extract_financial_reports(years)))
        
        # Transaction data
        extraction_results.append(("Transaction Data", self.extract_transaction_data()))
        
        # Supplementary data
        extraction_results.append(("Supplementary Data", self.extract_supplementary_data()))
        
        # Additional reports
        extraction_results.append(("Additional Reports", self.extract_additional_reports()))
        
        # Generate final summary
        extraction_results.append(("Final Summary", self.generate_final_summary()))
        
        # Calculate results
        successful_extractions = len([result for _, result in extraction_results if result])
        total_extractions = len(extraction_results)
        success_rate = (successful_extractions / total_extractions) * 100
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Print final results
        print("\n" + "=" * 65)
        print("🎉 EXTRACTION COMPLETE - FINAL RESULTS")
        print("=" * 65)
        
        for category, success in extraction_results:
            status = "✅" if success else "❌"
            print(f"{status} {category}")
        
        print(f"\n📊 Overall Success Rate: {successful_extractions}/{total_extractions} ({success_rate:.1f}%)")
        print(f"⏱️ Total Duration: {duration}")
        print(f"📁 Data Location: {self.data_dir}")
        
        if successful_extractions == total_extractions:
            print("\n🎉 ALL EXTRACTIONS COMPLETED SUCCESSFULLY!")
            print("Your Xero data is now available in the data/ directory.")
        else:
            print(f"\n⚠️ {total_extractions - successful_extractions} extraction(s) failed.")
            print("Check the extraction log for details on failed extractions.")
        
        return successful_extractions == total_extractions

def main():
    """Main execution function with command line arguments"""
    parser = argparse.ArgumentParser(description="Master Xero Data Extractor for MCX3D LTD")
    
    parser.add_argument(
        "--years", 
        nargs="+", 
        type=int, 
        default=[2020, 2021, 2022, 2023, 2024, 2025],
        help="Years for financial reports (default: 2020-2025)"
    )
    
    parser.add_argument(
        "--data-dir",
        type=Path,
        help="Custom data directory (default: ./data)"
    )
    
    parser.add_argument(
        "--skip-existing",
        action="store_true",
        help="Skip extraction if data files already exist"
    )
    
    parser.add_argument(
        "--test-connection",
        action="store_true", 
        help="Test connection only, don't extract data"
    )
    
    parser.add_argument(
        "--from-date",
        type=str,
        help="Start date for filtering transactions (YYYY-MM-DD format, e.g., 2020-01-01)"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize extractor
        extractor = MasterExtractor(data_dir=args.data_dir)
        
        # Test connection only
        if args.test_connection:
            if extractor.client.test_connection():
                print("✅ Connection test successful!")
                return
            else:
                print("❌ Connection test failed!")
                sys.exit(1)
        
        # Run complete extraction
        success = extractor.run_complete_extraction(
            years=args.years,
            skip_existing=args.skip_existing,
            from_date=args.from_date
        )
        
        if success:
            print("\n🎉 Master extraction completed successfully!")
        else:
            print("\n❌ Master extraction completed with errors.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Extraction interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during extraction: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
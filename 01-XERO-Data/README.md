# Xero Financial Reports Extractor

This Python script extracts comprehensive financial reports from Xero API by calendar year (2020-2025).

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r ../requirements.txt
```

### 2. Xero API Configuration

The `.env` file is already configured with the correct credentials:
- ✅ **Client ID**: 475EC4359EA4461DBDB16C4282A67410
- ✅ **Client Secret**: [Pre-configured with PKCE support]
- ✅ **Tenant ID**: 22f8acc9-3fea-4bae-9b97-91f90b532eea (MCX3D LTD)
- ✅ **Redirect URI**: http://localhost:8000/api/auth/xero/callback

### 3. Complete OAuth2 Authorization (Two-Step Process)

**Step 1: Generate Authorization URL**
```bash
python scripts/xero_auth_step1.py
```
This will:
- Generate a secure PKCE-based authorization URL
- Open your browser to Xero login
- Save session data for step 2

**Step 2: Process Authorization**
```bash
python scripts/xero_auth_step2.py
```
Then:
1. Copy the callback URL from your browser (even if it shows an error page)
2. Paste it when prompted
3. Tokens will be automatically saved to `xero_token.json`

### 4. Run the Financial Reports Extractor

```bash
python extract_financial_reports_v2.py
```

## What It Extracts

For each year (2020-2025), the script extracts:

1. **Profit & Loss Report** - Full year income and expenses
2. **Balance Sheet Report** - Year-end financial position  
3. **Trial Balance Report** - Year-end account balances

## Output Structure

```
financial-reports/
├── profit-loss-2020.json
├── balance-sheet-2020-12-31.json
├── trial-balance-2020-12-31.json
├── profit-loss-2021.json
├── balance-sheet-2021-12-31.json
├── trial-balance-2021-12-31.json
├── ... (continuing for all years)
└── financial-reports-summary.json
```

## Report Data Structure

Each report includes:
- **Metadata**: Report type, year, extraction date, currency, organization
- **Report Data**: Structured financial data with account details
- **Summary**: Key figures and totals

## Troubleshooting

### Common Issues:

1. **Missing .env file or credentials**
   - Solution: Ensure `.env` file exists with proper credentials

2. **Token expired (401 Unauthorized)**
   - Solution: Run the two-step OAuth2 process again:
     ```bash
     python scripts/xero_auth_step1.py
     python scripts/xero_auth_step2.py
     ```

3. **403 Forbidden Error** 
   - Solution: Ensure your Xero app has permission to access financial data

4. **PKCE verification failed**
   - Solution: Make sure to complete both steps quickly (codes expire in minutes)
   - Don't reuse old callback URLs

5. **State parameter mismatch**
   - Solution: Always run step1 before step2, don't skip session files

### Getting Fresh Tokens:

Tokens expire every 30 minutes. To get fresh tokens:
```bash
python scripts/xero_auth_step1.py
python scripts/xero_auth_step2.py
```

The PKCE-based flow ensures secure token exchange.

## File Descriptions

- `extract_financial_reports_v2.py` - Main extraction script with token-based auth
- `scripts/xero_auth_step1.py` - Generate PKCE-based authorization URL
- `scripts/xero_auth_step2.py` - Process callback and exchange tokens
- `.env` - API credentials and configuration
- `xero_token.json` - Generated token file (created after OAuth2)
- `xero_session.json` - Temporary session file (auto-cleaned)
- `../requirements.txt` - Python dependencies (in project root)
- `README.md` - This documentation

## Security Note

⚠️ **Important**: Never commit sensitive files to version control:

The `.gitignore` should include:
```
.env
xero_token.json
xero_session.json
*.log
financial-reports/
```

- **PKCE Flow**: Uses Proof Key for Code Exchange for enhanced security
- **Short-Lived Tokens**: Access tokens expire in 30 minutes
- **Session Management**: Temporary session files are auto-cleaned
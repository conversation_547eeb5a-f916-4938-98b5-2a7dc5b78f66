#!/usr/bin/env python3
"""
MCX3D Financials - Configuration Settings
Centralized configuration management for Xero data extraction
"""

import os
from pathlib import Path
from typing import Dict, List, Any

class XeroConfig:
    """
    Centralized configuration for Xero API integration
    """
    
    # API Configuration
    BASE_URL = "https://api.xero.com/api.xro/2.0/"
    AUTH_URL = "https://login.xero.com/identity/connect/authorize"
    TOKEN_URL = "https://identity.xero.com/connect/token"
    
    # Default settings
    DEFAULT_SCOPES = [
        "accounting.transactions",
        "accounting.contacts", 
        "accounting.reports.read",
        "accounting.settings",
        "offline_access"
    ]
    
    DEFAULT_REDIRECT_URI = "http://localhost:8000/api/auth/xero/callback"
    DEFAULT_ORGANIZATION = "MCX3D LTD"
    DEFAULT_CURRENCY = "GBP"
    
    # Financial report years
    DEFAULT_REPORT_YEARS = [2020, 2021, 2022, 2023, 2024, 2025]
    
    # Data extraction settings
    EXTRACTION_SETTINGS = {
        "max_retries": 3,
        "retry_delay": 1,  # seconds
        "request_timeout": 30,  # seconds
        "page_size": 100,
        "max_pages": None,  # None for unlimited
        "backup_existing": True,
        "cleanup_sessions": True
    }
    
    # File and directory settings  
    DATA_STRUCTURE = {
        "core": ["organization", "accounts", "contacts", "invoices", "bank_transactions"],
        "reports": {
            "financial": ["profit-loss", "balance-sheet", "trial-balance"],
            "aged": ["aged-receivables", "aged-payables"],
            "executive": ["bank-summary", "budget-summary", "executive-summary"]
        },
        "supplementary": {
            "payments": ["payments"],
            "items": ["items"],
            "users": ["users"],
            "misc": ["credit_notes", "quotes", "manual_journals", "tracking_categories", "tax_rates", "branding_themes", "repeating_invoices"]
        },
        "summaries": ["extraction_log", "data_inventory", "master_summary"]
    }
    
    @classmethod
    def get_environment_config(cls) -> Dict[str, Any]:
        """Get configuration from environment variables"""
        return {
            "client_id": os.getenv("XERO_CLIENT_ID"),
            "client_secret": os.getenv("XERO_CLIENT_SECRET"),
            "redirect_uri": os.getenv("XERO_REDIRECT_URI", cls.DEFAULT_REDIRECT_URI),
            "scopes": os.getenv("XERO_SCOPES", " ".join(cls.DEFAULT_SCOPES)).split(),
            "tenant_id": os.getenv("XERO_TENANT_ID"),
            "organization_name": os.getenv("ORGANIZATION_NAME", cls.DEFAULT_ORGANIZATION)
        }
    
    @classmethod
    def validate_config(cls) -> tuple[bool, List[str]]:
        """Validate configuration and return status with any errors"""
        config = cls.get_environment_config()
        errors = []
        
        # Check required fields
        required_fields = ["client_id", "client_secret"]
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Missing required field: {field.upper()}")
        
        # Check tenant_id if tokens should exist
        token_file = Path(__file__).parent / "xero_token.json"
        if token_file.exists() and not config.get("tenant_id"):
            errors.append("Missing XERO_TENANT_ID - required when tokens exist")
        
        return len(errors) == 0, errors
    
    @classmethod
    def get_data_directories(cls, base_dir: Path) -> Dict[str, Path]:
        """Get all data directory paths"""
        directories = {}
        
        # Core directories
        for category in cls.DATA_STRUCTURE["core"]:
            directories[f"core_{category}"] = base_dir / "core"
        
        # Report directories
        for report_category, report_types in cls.DATA_STRUCTURE["reports"].items():
            directories[f"reports_{report_category}"] = base_dir / "reports" / report_category
        
        # Supplementary directories
        for supp_category, supp_types in cls.DATA_STRUCTURE["supplementary"].items():
            directories[f"supplementary_{supp_category}"] = base_dir / "supplementary" / supp_category
        
        # Summary directory
        directories["summaries"] = base_dir / "summaries"
        
        return directories
    
    @classmethod
    def get_api_endpoints(cls) -> Dict[str, str]:
        """Get all Xero API endpoints used in extraction"""
        return {
            # Core data
            "organization": "Organisation",
            "accounts": "Accounts",
            "contacts": "Contacts", 
            "invoices": "Invoices",
            "bank_transactions": "BankTransactions",
            
            # Financial reports
            "profit_loss": "Reports/ProfitAndLoss",
            "balance_sheet": "Reports/BalanceSheet",
            "trial_balance": "Reports/TrialBalance",
            
            # Additional reports
            "bank_summary": "Reports/BankSummary",
            "budget_summary": "Reports/BudgetSummary", 
            "executive_summary": "Reports/ExecutiveSummary",
            
            # Transaction data
            "payments": "Payments",
            
            # Supplementary data  
            "credit_notes": "CreditNotes",
            "quotes": "Quotes",
            "manual_journals": "ManualJournals",
            "items": "Items",
            "tracking_categories": "TrackingCategories",
            "tax_rates": "TaxRates",
            "users": "Users",
            "branding_themes": "BrandingThemes",
            "repeating_invoices": "RepeatingInvoices"
        }

# Global configuration instance
config = XeroConfig()
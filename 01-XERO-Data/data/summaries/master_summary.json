{"extraction_completed": "2025-07-29T06:38:22.575804", "organization": "MCX3D LTD", "tenant_id": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "version": "2.0.0", "data_inventory": {"generated": "2025-07-29T06:38:22.572090", "data_directory": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data", "categories": {"core": {"files": {"core/organization.backup.20250729_063728.json": {"exists": true, "size_bytes": 1324, "size_mb": 0.0, "modified": "2025-07-28T22:12:16.150193", "created": "2025-07-29T06:37:36.302088"}, "core/invoices.json": {"exists": true, "size_bytes": 241083, "size_mb": 0.23, "modified": "2025-07-29T06:37:33.043514", "created": "2025-07-29T06:37:47.705708"}, "core/accounts.backup.20250729_063728.json": {"exists": true, "size_bytes": 61037, "size_mb": 0.06, "modified": "2025-07-28T22:24:48.398072", "created": "2025-07-29T06:37:36.300994"}, "core/bank_transactions.json": {"exists": true, "size_bytes": 4025577, "size_mb": 3.84, "modified": "2025-07-29T06:38:01.532991", "created": "2025-07-29T06:38:01.532991"}, "core/bank_transactions.backup.20250729_063801.json": {"exists": true, "size_bytes": 1545943, "size_mb": 1.47, "modified": "2025-07-28T22:24:33.920441", "created": "2025-07-29T06:38:05.489610"}, "core/organization.json": {"exists": true, "size_bytes": 1947, "size_mb": 0.0, "modified": "2025-07-29T06:37:28.102737", "created": "2025-07-29T06:37:36.295908"}, "core/invoices.backup.20250729_063733.json": {"exists": true, "size_bytes": 104156, "size_mb": 0.1, "modified": "2025-07-28T22:24:07.406215", "created": "2025-07-29T06:37:47.703637"}, "core/accounts.json": {"exists": true, "size_bytes": 60409, "size_mb": 0.06, "modified": "2025-07-29T06:37:28.683366", "created": "2025-07-29T06:37:36.298727"}, "core/contacts.backup.20250729_063731.json": {"exists": true, "size_bytes": 470241, "size_mb": 0.45, "modified": "2025-07-28T22:24:56.420619", "created": "2025-07-29T06:37:44.304992"}, "core/contacts.json": {"exists": true, "size_bytes": 267991, "size_mb": 0.26, "modified": "2025-07-29T06:37:31.678589", "created": "2025-07-29T06:37:44.307708"}}, "file_count": 10, "total_size_mb": 6.***************}, "supplementary": {"files": {"supplementary/misc/repeating_invoices.json": {"exists": true, "size_bytes": 10217, "size_mb": 0.01, "modified": "2025-07-29T06:38:18.129384", "created": "2025-07-29T06:38:18.248231"}, "supplementary/misc/manual-journals.json": {"exists": true, "size_bytes": 585, "size_mb": 0.0, "modified": "2025-07-28T23:18:55.591259", "created": "2025-07-28T23:36:47.544277"}, "supplementary/misc/purchase-orders.json": {"exists": true, "size_bytes": 234, "size_mb": 0.0, "modified": "2025-07-28T23:26:22.764006", "created": "2025-07-28T23:36:41.227969"}, "supplementary/misc/manual_journals.json": {"exists": true, "size_bytes": 702, "size_mb": 0.0, "modified": "2025-07-29T06:38:14.330382", "created": "2025-07-29T06:38:14.345225"}, "supplementary/misc/quotes.backup.20250729_063813.json": {"exists": true, "size_bytes": 200, "size_mb": 0.0, "modified": "2025-07-28T23:18:55.046175", "created": "2025-07-29T06:38:14.470827"}, "supplementary/misc/repeating-invoices.json": {"exists": true, "size_bytes": 10890, "size_mb": 0.01, "modified": "2025-07-28T23:26:27.142685", "created": "2025-07-28T23:36:46.986990"}, "supplementary/misc/tracking-categories.json": {"exists": true, "size_bytes": 216, "size_mb": 0.0, "modified": "2025-07-28T23:18:56.980984", "created": "2025-07-28T23:36:47.375271"}, "supplementary/misc/tax-rates.json": {"exists": true, "size_bytes": 11685, "size_mb": 0.01, "modified": "2025-07-28T23:18:58.540188", "created": "2025-07-28T23:36:47.383121"}, "supplementary/misc/tax_rates.json": {"exists": true, "size_bytes": 11829, "size_mb": 0.01, "modified": "2025-07-29T06:38:16.075160", "created": "2025-07-29T06:38:16.157963"}, "supplementary/misc/branding-themes.json": {"exists": true, "size_bytes": 916, "size_mb": 0.0, "modified": "2025-07-28T23:26:16.552173", "created": "2025-07-28T23:36:46.984730"}, "supplementary/misc/quotes.json": {"exists": true, "size_bytes": 283, "size_mb": 0.0, "modified": "2025-07-29T06:38:13.809378", "created": "2025-07-29T06:38:13.809378"}, "supplementary/misc/extraction-summary.json": {"exists": true, "size_bytes": 950, "size_mb": 0.0, "modified": "2025-07-28T23:18:58.540367", "created": "2025-07-28T23:36:46.964154"}, "supplementary/misc/receipts.json": {"exists": true, "size_bytes": 227, "size_mb": 0.0, "modified": "2025-07-28T23:26:24.291850", "created": "2025-07-28T23:36:46.998707"}, "supplementary/misc/branding_themes.json": {"exists": true, "size_bytes": 1014, "size_mb": 0.0, "modified": "2025-07-29T06:38:17.420394", "created": "2025-07-29T06:38:17.434472"}, "supplementary/misc/tracking_categories.json": {"exists": true, "size_bytes": 296, "size_mb": 0.0, "modified": "2025-07-29T06:38:15.533396", "created": "2025-07-29T06:38:15.550090"}, "supplementary/misc/additional-extraction-summary.json": {"exists": true, "size_bytes": 1574, "size_mb": 0.0, "modified": "2025-07-28T23:26:27.143633", "created": "2025-07-28T23:36:47.230238"}, "supplementary/misc/prepayments.json": {"exists": true, "size_bytes": 230, "size_mb": 0.0, "modified": "2025-07-28T23:26:22.074239", "created": "2025-07-28T23:36:47.277863"}, "supplementary/misc/overpayments.json": {"exists": true, "size_bytes": 231, "size_mb": 0.0, "modified": "2025-07-28T23:26:20.271067", "created": "2025-07-28T23:36:47.238525"}, "supplementary/misc/linked-transactions.json": {"exists": true, "size_bytes": 238, "size_mb": 0.0, "modified": "2025-07-28T23:26:18.665034", "created": "2025-07-28T23:36:47.041174"}, "supplementary/misc/credit-notes.json": {"exists": true, "size_bytes": 1944, "size_mb": 0.0, "modified": "2025-07-28T23:18:53.489389", "created": "2025-07-28T23:36:47.072562"}, "supplementary/misc/credit_notes.json": {"exists": true, "size_bytes": 1651, "size_mb": 0.0, "modified": "2025-07-29T06:38:13.300971", "created": "2025-07-29T06:38:13.314642"}, "supplementary/payments/payments-summary.backup.20250729_063812.json": {"exists": true, "size_bytes": 297, "size_mb": 0.0, "modified": "2025-07-28T23:12:41.890881", "created": "2025-07-29T06:38:13.835441"}, "supplementary/payments/payments.json": {"exists": true, "size_bytes": 106492, "size_mb": 0.1, "modified": "2025-07-29T06:38:12.797664", "created": "2025-07-29T06:38:12.797664"}, "supplementary/payments/payments.backup.20250729_063812.json": {"exists": true, "size_bytes": 139977, "size_mb": 0.13, "modified": "2025-07-28T23:12:41.890261", "created": "2025-07-29T06:38:13.834075"}, "supplementary/payments/payments-summary.json": {"exists": true, "size_bytes": 224, "size_mb": 0.0, "modified": "2025-07-29T06:38:12.797965", "created": "2025-07-29T06:38:12.797965"}, "supplementary/users/users.backup.20250729_063816.json": {"exists": true, "size_bytes": 2108, "size_mb": 0.0, "modified": "2025-07-28T23:26:16.020116", "created": "2025-07-29T06:38:18.734116"}, "supplementary/users/users.json": {"exists": true, "size_bytes": 2227, "size_mb": 0.0, "modified": "2025-07-29T06:38:16.747306", "created": "2025-07-29T06:38:16.747306"}, "supplementary/items/items.backup.20250729_063814.json": {"exists": true, "size_bytes": 6613, "size_mb": 0.01, "modified": "2025-07-28T23:18:56.131996", "created": "2025-07-29T06:38:16.920880"}, "supplementary/items/items.json": {"exists": true, "size_bytes": 6759, "size_mb": 0.01, "modified": "2025-07-29T06:38:14.850897", "created": "2025-07-29T06:38:14.850897"}}, "file_count": 29, "total_size_mb": 0.*****************}, "historical": {"files": {"historical/bank_transactions_2020_onwards.json": {"exists": true, "size_bytes": 6204029, "size_mb": 5.92, "modified": "2025-07-29T06:37:14.896957", "created": "2025-07-29T06:37:40.852135"}, "historical/invoices_2020_onwards.json": {"exists": true, "size_bytes": 273600, "size_mb": 0.26, "modified": "2025-07-29T06:37:14.776944", "created": "2025-07-29T06:37:23.054323"}, "historical/extraction_summary.json": {"exists": true, "size_bytes": 383, "size_mb": 0.0, "modified": "2025-07-29T06:37:14.898355", "created": "2025-07-29T06:37:23.056887"}}, "file_count": 3, "total_size_mb": 6.18}, "summaries": {"files": {"summaries/extraction_log.json": {"exists": true, "size_bytes": 2856, "size_mb": 0.0, "modified": "2025-07-29T06:38:22.572071", "created": "2025-07-29T06:38:22.572071"}, "summaries/migration_summary.json": {"exists": true, "size_bytes": 21373, "size_mb": 0.02, "modified": "2025-07-28T23:36:32.804895", "created": "2025-07-28T23:36:47.386710"}, "summaries/cleanup_summary.json": {"exists": true, "size_bytes": 3389, "size_mb": 0.0, "modified": "2025-07-28T23:37:46.205638", "created": "2025-07-28T23:37:55.971702"}}, "file_count": 3, "total_size_mb": 0.02}, "reports": {"files": {"reports/financial/profit-loss-2023.backup.20250729_063806.json": {"exists": true, "size_bytes": 11144, "size_mb": 0.01, "modified": "2025-07-28T23:10:27.663608", "created": "2025-07-29T06:38:07.553743"}, "reports/financial/trial-balance-2021-12-31.json": {"exists": true, "size_bytes": 38651, "size_mb": 0.04, "modified": "2025-07-29T06:38:04.392504", "created": "2025-07-29T06:38:14.980894"}, "reports/financial/profit-loss-2021.backup.20250729_063803.json": {"exists": true, "size_bytes": 12998, "size_mb": 0.01, "modified": "2025-07-28T23:10:21.167444", "created": "2025-07-29T06:38:09.350134"}, "reports/financial/trial-balance-2023-12-31.backup.20250729_063807.json": {"exists": true, "size_bytes": 45068, "size_mb": 0.04, "modified": "2025-07-28T23:10:29.721067", "created": "2025-07-29T06:38:09.221899"}, "reports/financial/balance-sheet-2020-12-31.json": {"exists": true, "size_bytes": 1178, "size_mb": 0.0, "modified": "2025-07-28T23:10:17.954644", "created": "2025-07-28T23:36:47.550145"}, "reports/financial/balance-sheet-2025-12-31.json": {"exists": true, "size_bytes": 19132, "size_mb": 0.02, "modified": "2025-07-29T06:38:10.845061", "created": "2025-07-29T06:38:10.845061"}, "reports/financial/financial-reports-summary.backup.20250729_063811.json": {"exists": true, "size_bytes": 449310, "size_mb": 0.43, "modified": "2025-07-28T23:10:38.605465", "created": "2025-07-29T06:38:12.532530"}, "reports/financial/balance-sheet-2024-12-31.backup.20250729_063809.json": {"exists": true, "size_bytes": 19018, "size_mb": 0.02, "modified": "2025-07-28T23:10:31.236948", "created": "2025-07-29T06:38:09.939701"}, "reports/financial/trial-balance-2024-12-31.json": {"exists": true, "size_bytes": 49416, "size_mb": 0.05, "modified": "2025-07-29T06:38:09.846100", "created": "2025-07-29T06:38:09.846100"}, "reports/financial/trial-balance-2023-12-31.json": {"exists": true, "size_bytes": 45175, "size_mb": 0.04, "modified": "2025-07-29T06:38:07.969655", "created": "2025-07-29T06:38:07.969655"}, "reports/financial/profit-loss-2020.json": {"exists": true, "size_bytes": 1456, "size_mb": 0.0, "modified": "2025-07-28T23:10:16.773606", "created": "2025-07-28T23:36:47.059651"}, "reports/financial/balance-sheet-2023-12-31.backup.20250729_063807.json": {"exists": true, "size_bytes": 18994, "size_mb": 0.02, "modified": "2025-07-28T23:10:28.416741", "created": "2025-07-29T06:38:08.658618"}, "reports/financial/balance-sheet-2022-12-31.json": {"exists": true, "size_bytes": 16436, "size_mb": 0.02, "modified": "2025-07-29T06:38:05.411912", "created": "2025-07-29T06:38:20.063124"}, "reports/financial/trial-balance-2024-12-31.backup.20250729_063809.json": {"exists": true, "size_bytes": 49309, "size_mb": 0.05, "modified": "2025-07-28T23:10:34.607629", "created": "2025-07-29T06:38:10.643206"}, "reports/financial/profit-loss-2024.backup.20250729_063808.json": {"exists": true, "size_bytes": 13033, "size_mb": 0.01, "modified": "2025-07-28T23:10:30.490666", "created": "2025-07-29T06:38:09.225508"}, "reports/financial/profit-loss-2021.json": {"exists": true, "size_bytes": 13105, "size_mb": 0.01, "modified": "2025-07-29T06:38:03.181821", "created": "2025-07-29T06:38:09.346316"}, "reports/financial/trial-balance-2022-12-31.backup.20250729_063806.json": {"exists": true, "size_bytes": 45126, "size_mb": 0.04, "modified": "2025-07-28T23:10:26.508360", "created": "2025-07-29T06:38:06.854334"}, "reports/financial/profit-loss-2022.json": {"exists": true, "size_bytes": 14397, "size_mb": 0.01, "modified": "2025-07-29T06:38:04.903780", "created": "2025-07-29T06:38:14.976235"}, "reports/financial/balance-sheet-2024-12-31.json": {"exists": true, "size_bytes": 19125, "size_mb": 0.02, "modified": "2025-07-29T06:38:09.177435", "created": "2025-07-29T06:38:09.177435"}, "reports/financial/profit-loss-2022.backup.20250729_063804.json": {"exists": true, "size_bytes": 14290, "size_mb": 0.01, "modified": "2025-07-28T23:10:24.086262", "created": "2025-07-29T06:38:14.988053"}, "reports/financial/balance-sheet-2025-12-31.backup.20250729_063810.json": {"exists": true, "size_bytes": 19025, "size_mb": 0.02, "modified": "2025-07-28T23:10:36.670799", "created": "2025-07-29T06:38:11.272997"}, "reports/financial/trial-balance-2025-12-31.json": {"exists": true, "size_bytes": 36673, "size_mb": 0.03, "modified": "2025-07-29T06:38:11.600223", "created": "2025-07-29T06:38:11.600223"}, "reports/financial/financial-reports-summary.json": {"exists": true, "size_bytes": 582, "size_mb": 0.0, "modified": "2025-07-29T06:38:11.600580", "created": "2025-07-29T06:38:11.600580"}, "reports/financial/trial-balance-2020-12-31.json": {"exists": true, "size_bytes": 1423, "size_mb": 0.0, "modified": "2025-07-28T23:10:20.057787", "created": "2025-07-28T23:36:47.048991"}, "reports/financial/balance-sheet-2021-12-31.json": {"exists": true, "size_bytes": 11504, "size_mb": 0.01, "modified": "2025-07-29T06:38:03.766790", "created": "2025-07-29T06:38:14.974112"}, "reports/financial/profit-loss-2023.json": {"exists": true, "size_bytes": 11251, "size_mb": 0.01, "modified": "2025-07-29T06:38:06.520990", "created": "2025-07-29T06:38:06.520990"}, "reports/financial/trial-balance-2021-12-31.backup.20250729_063804.json": {"exists": true, "size_bytes": 38544, "size_mb": 0.04, "modified": "2025-07-28T23:10:22.797641", "created": "2025-07-29T06:38:14.989527"}, "reports/financial/profit-loss-2024.json": {"exists": true, "size_bytes": 13140, "size_mb": 0.01, "modified": "2025-07-29T06:38:08.583706", "created": "2025-07-29T06:38:08.583706"}, "reports/financial/balance-sheet-2022-12-31.backup.20250729_063805.json": {"exists": true, "size_bytes": 16329, "size_mb": 0.02, "modified": "2025-07-28T23:10:24.719419", "created": "2025-07-29T06:38:20.066067"}, "reports/financial/trial-balance-2022-12-31.json": {"exists": true, "size_bytes": 45233, "size_mb": 0.04, "modified": "2025-07-29T06:38:06.021158", "created": "2025-07-29T06:38:06.021158"}, "reports/financial/profit-loss-2025.json": {"exists": true, "size_bytes": 11249, "size_mb": 0.01, "modified": "2025-07-29T06:38:10.317150", "created": "2025-07-29T06:38:10.317150"}, "reports/financial/profit-loss-2025.backup.20250729_063810.json": {"exists": true, "size_bytes": 11142, "size_mb": 0.01, "modified": "2025-07-28T23:10:35.317690", "created": "2025-07-29T06:38:11.270711"}, "reports/financial/balance-sheet-2021-12-31.backup.20250729_063803.json": {"exists": true, "size_bytes": 11397, "size_mb": 0.01, "modified": "2025-07-28T23:10:21.863985", "created": "2025-07-29T06:38:14.982821"}, "reports/financial/balance-sheet-2023-12-31.json": {"exists": true, "size_bytes": 19101, "size_mb": 0.02, "modified": "2025-07-29T06:38:07.185848", "created": "2025-07-29T06:38:07.185848"}, "reports/financial/trial-balance-2025-12-31.backup.20250729_063811.json": {"exists": true, "size_bytes": 36566, "size_mb": 0.03, "modified": "2025-07-28T23:10:38.594638", "created": "2025-07-29T06:38:12.530375"}, "reports/aged/aged-payables-2025-07-28.json": {"exists": true, "size_bytes": 28989, "size_mb": 0.03, "modified": "2025-07-28T23:17:53.142412", "created": "2025-07-28T23:36:47.217075"}, "reports/aged/aged-reports-summary.json": {"exists": true, "size_bytes": 390, "size_mb": 0.0, "modified": "2025-07-28T23:17:53.143222", "created": "2025-07-28T23:36:47.001974"}, "reports/aged/aged-receivables-2025-07-28.json": {"exists": true, "size_bytes": 29115, "size_mb": 0.03, "modified": "2025-07-28T23:17:33.508606", "created": "2025-07-28T23:36:47.547036"}, "reports/executive/bank-summary.json": {"exists": true, "size_bytes": 5910, "size_mb": 0.01, "modified": "2025-07-29T06:38:18.885115", "created": "2025-07-29T06:38:18.885115"}, "reports/executive/executive-summary.json": {"exists": true, "size_bytes": 10428, "size_mb": 0.01, "modified": "2025-07-29T06:38:22.571819", "created": "2025-07-29T06:38:22.571819"}, "reports/executive/executive-summary.backup.20250729_063822.json": {"exists": true, "size_bytes": 10428, "size_mb": 0.01, "modified": "2025-07-28T23:26:15.292142", "created": "2025-07-29T06:38:22.571344"}, "reports/executive/bank-summary.backup.20250729_063818.json": {"exists": true, "size_bytes": 5910, "size_mb": 0.01, "modified": "2025-07-28T23:26:09.596322", "created": "2025-07-29T06:38:20.048577"}, "reports/executive/budget-summary.json": {"exists": true, "size_bytes": 4494, "size_mb": 0.0, "modified": "2025-07-29T06:38:19.675380", "created": "2025-07-29T06:38:19.675380"}, "reports/executive/budget-summary.backup.20250729_063819.json": {"exists": true, "size_bytes": 4494, "size_mb": 0.0, "modified": "2025-07-28T23:26:12.552343", "created": "2025-07-29T06:38:20.182338"}}, "file_count": 44, "total_size_mb": 1.****************}}, "total_files": 89, "total_size_mb": 14.***************}, "extraction_statistics": {"total_files": 89, "total_size_mb": 14.17, "categories": 5, "extraction_duration": "N/A"}, "next_steps": ["Review data inventory in summaries/data_inventory.json", "Check extraction log in summaries/extraction_log.json", "Use data files for financial analysis and reporting", "Re-run authentication if tokens expire (30 minutes)"]}
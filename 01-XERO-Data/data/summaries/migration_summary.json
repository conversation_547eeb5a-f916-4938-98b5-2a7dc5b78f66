{"migration_completed": "2025-07-28T23:36:32.804573", "total_operations": 48, "successful_operations": 48, "failed_operations": 0, "success_rate": 100.0, "migration_log": [{"timestamp": "2025-07-28T23:36:32.792502", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/organization-details.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/core/organization.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.792910", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/accounts_latest.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/core/accounts.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.793404", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/contacts_latest.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/core/contacts.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.793805", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/invoices-all.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/core/invoices.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.794785", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/bank_transactions_latest.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/core/bank_transactions.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.795203", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2021-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2021-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.795436", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2020-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2020-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.795708", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2025-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2025-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.795952", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2024-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2024-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.796192", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2023-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2023-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.796382", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2020.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2020.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.796608", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2022-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2022-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.796791", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2021.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2021.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.796953", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2022.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2022.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.797131", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2024-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2024-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.797365", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2025-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2025-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.797713", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/financial-reports-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/financial-reports-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.797930", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2020-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2020-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.798166", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2021-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2021-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.798443", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2023.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2023.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.798736", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2024.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2024.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.798968", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/trial-balance-2022-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/trial-balance-2022-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.799211", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/profit-loss-2025.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/profit-loss-2025.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.799416", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/financial-reports/balance-sheet-2023-12-31.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/financial/balance-sheet-2023-12-31.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.799693", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/aged-reports/aged-payables-2025-07-28.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/aged/aged-payables-2025-07-28.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.799970", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/aged-reports/aged-reports-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/aged/aged-reports-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.800147", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/aged-reports/aged-receivables-2025-07-28.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/aged/aged-receivables-2025-07-28.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.800433", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/payments-data/payments.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/payments/payments.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.800663", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/payments-data/payments-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/payments/payments-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.800908", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/manual-journals.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/manual-journals.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.801175", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/tracking-categories.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/tracking-categories.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.801473", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/tax-rates.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/tax-rates.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.801767", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/items.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/items/items.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.801947", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/quotes.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/quotes.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.802123", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/extraction-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/extraction-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.802306", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/remaining-data/credit-notes.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/credit-notes.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.802538", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/purchase-orders.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/purchase-orders.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.802829", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/bank-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/executive/bank-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.802991", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/repeating-invoices.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/repeating-invoices.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.803153", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/branding-themes.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/branding-themes.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.803314", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/executive-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/executive/executive-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.803463", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/users.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/users/users.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.803637", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/receipts.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/receipts.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.803868", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/additional-extraction-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/additional-extraction-summary.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.804051", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/prepayments.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/prepayments.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.804210", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/overpayments.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/overpayments.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.804365", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/linked-transactions.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/supplementary/misc/linked-transactions.json", "success": true, "error": null}, {"timestamp": "2025-07-28T23:36:32.804570", "action": "copy", "source": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/additional-data/budget-summary.json", "destination": "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/01-XERO-Data/data/reports/executive/budget-summary.json", "success": true, "error": null}]}
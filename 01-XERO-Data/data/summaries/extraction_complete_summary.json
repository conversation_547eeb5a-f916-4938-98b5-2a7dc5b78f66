{"metadata": {"extractedDate": "2025-07-29", "organization": "MCX3D LTD", "purpose": "Complete historical data extraction from 2020 onwards"}, "extraction_summary": {"bank_transactions": {"total_count": 4888, "date_range": {"start": "2016-12-02", "end": "2025-07-22"}, "from_2020_onwards": 4610, "before_2020": 278, "extraction_method": "Paginated with resume capability"}, "invoices": {"total_count": 120, "date_range": {"start": "2021-06-09", "end": "2025-04-18"}, "from_2020_onwards": 118, "all_from_2020": true}, "other_data": {"accounts": 107, "contacts": 377, "payments": 103, "credit_notes": 1, "manual_journals": 1, "items": 14, "tax_rates": 21, "users": 5, "branding_themes": 2, "repeating_invoices": 6}}, "technical_details": {"rate_limiting": {"implemented": true, "max_retries": 5, "exponential_backoff": true, "requests_per_minute": 60}, "extraction_sessions": [{"type": "initial_extraction", "bank_transactions_retrieved": 3600, "stopped_at": "2024-03-01", "reason": "Rate limit reached"}, {"type": "resume_extraction", "bank_transactions_retrieved": 1288, "completed": true, "from_date": "2024-03-02"}], "total_api_requests": "~100", "extraction_duration": "~10 minutes"}, "data_quality": {"completeness": {"bank_transactions": "100% from 2020 onwards", "invoices": "100% (all invoices are from 2021 onwards)"}, "validation": {"duplicate_handling": "Implemented", "date_verification": "Completed", "metadata_included": true}}, "files_location": {"bank_transactions": "data/core/bank_transactions.json", "invoices": "data/core/invoices.json", "accounts": "data/core/accounts.json", "contacts": "data/core/contacts.json", "extraction_log": "data/summaries/extraction_log.json"}}
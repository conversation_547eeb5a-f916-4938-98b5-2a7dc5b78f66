{"metadata": {"dataType": "Contacts", "extractedDate": "2025-07-29T06:37:31.656570", "organization": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "recordCount": 377, "version": "2.0.0"}, "contacts": [{"ContactID": "2692d6a4-da65-4e65-b9b5-1fbbdb344251", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609777631917+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "51e5453e-3f23-45ff-8b16-6b9b48068040", "ContactStatus": "ACTIVE", "Name": "Bilal itani", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609777830667+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9b18c5d4-8f24-4683-b8b4-4a50f08ca9e1", "ContactStatus": "ACTIVE", "Name": "Google", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609782681093+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bcc5f409-f280-472f-9ac0-91d72704dae3", "ContactStatus": "ACTIVE", "Name": "Photoshop", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609782803657+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "222c17d4-ad8f-4e04-979c-205fd68ff8a4", "ContactStatus": "ACTIVE", "Name": "Grammarly", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609783223170+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a3f19542-8f68-4ecd-bb98-c961edd0927d", "ContactStatus": "ACTIVE", "Name": "The Hoxton Mix", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609783774433+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8c5b91f3-c164-40d2-b238-262f3d08c79b", "ContactStatus": "ACTIVE", "Name": "Webflow", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609783902087+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6876c84b-acdf-4a9c-8c8f-1e8ba4a18a0b", "ContactStatus": "ACTIVE", "Name": "Siteground", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784053867+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c6685a74-4cfe-4305-8d4a-aa991eb06475", "ContactStatus": "ACTIVE", "Name": "Tailor Brands", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784104107+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1a31a469-7cf2-48a8-8914-8254494cb4cd", "ContactStatus": "ACTIVE", "Name": "Vimeo", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784143743+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f81bb253-9522-45d9-a21e-48d5796fc7c2", "ContactStatus": "ACTIVE", "Name": "Befunky", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784169767+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "36b01822-90af-47ce-bd06-fe0c2247639f", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784222360+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c0609209-4bb7-4aa6-be20-681d6a226687", "ContactStatus": "ACTIVE", "Name": "Facebook", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784269537+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8ef61a23-a740-47fe-ae74-205ba9944fef", "ContactStatus": "ACTIVE", "Name": "Microsoft", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784376103+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0a993520-d124-4ee7-bb8c-f3f1ad8df5ed", "ContactStatus": "ACTIVE", "Name": "SBR", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784418020+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "75afbf7f-08da-4fdc-82ad-0779816d97d0", "ContactStatus": "ACTIVE", "Name": "Zoom", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784459180+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "11762fee-6eea-403f-994d-328c142c76c7", "ContactStatus": "ACTIVE", "Name": "Wix", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784656053+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bec95d29-7d2a-450c-8964-f6773eb19924", "ContactStatus": "ACTIVE", "Name": "Facbook", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784747507+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3ecaaf9a-cd0a-4f80-911c-529d2a890710", "ContactStatus": "ACTIVE", "Name": "Prysm", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784830093+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "46472a72-70e8-4c3a-a581-edf5e4e63d71", "ContactStatus": "ACTIVE", "Name": "Designer", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784867517+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "491dee8a-5726-4350-9265-1a232cb551e2", "ContactStatus": "ACTIVE", "Name": "Transferwise", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609784916997+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e03a7916-802c-4a9d-81fc-d72a4851b0e5", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609785151073+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "55aa7905-d711-41f8-bf48-08a2c47df3ef", "ContactStatus": "ACTIVE", "Name": "Domestika", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609785244637+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "60ad4052-fc97-452c-bc9a-49a22e846487", "ContactStatus": "ACTIVE", "Name": "Turbosquidi", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1ecab701-e4fb-4856-a919-ea611855bb1e", "ContactStatus": "ACTIVE", "Name": "Bank", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8197d197-ab8c-4d76-b8d5-1503a6423169", "ContactStatus": "ACTIVE", "Name": "Revolut", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "aecf3a15-c7f9-491a-95e3-39467bd0746b", "ContactStatus": "ACTIVE", "Name": "Apple", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609839527890+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "be1aa2b7-dbe7-4771-8aee-3fd205f00281", "ContactStatus": "ACTIVE", "Name": "Toters", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609839713093+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ccbd275d-a8a2-46c9-8ef2-4f2837e4a31e", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609839812813+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "af44f764-a3a1-4f50-9ddb-436084a8e553", "ContactStatus": "ACTIVE", "Name": "Paddle", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609840084563+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "600fe5ce-4c1c-4ae7-830c-1077e210e9ed", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609840554870+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "afa44c2a-f0d4-4cf2-8c50-951bd66a118d", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609840828163+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "43b482d1-79d8-407a-9489-8e48aa1bf550", "ContactStatus": "ACTIVE", "Name": "Creativetime", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609840917273+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "be542734-c087-431a-bcb5-121af1277157", "ContactStatus": "ACTIVE", "Name": "Biteable", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609841122847+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "76ccdf40-745e-4695-83df-7d7d44355a2a", "ContactStatus": "ACTIVE", "Name": "Placeit Empowerkit", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609841193537+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e1bfbf25-38dd-4ec0-bb7a-332442b1fdda", "ContactStatus": "ACTIVE", "Name": "The Digital Marketing Services", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609842309590+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3b0b1b26-ed6b-437c-b567-9ee2fb1cbad1", "ContactStatus": "ACTIVE", "Name": "Fissure Robin", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609844376310+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c9870ff2-7741-4197-8e07-2d04908339d4", "ContactStatus": "ACTIVE", "Name": "Fossori <PERSON> <PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609844479923+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "473a7eca-a3a8-4986-8813-2892a417b168", "ContactStatus": "ACTIVE", "Name": "Sketchfab", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609844564643+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9f59afaf-feee-4a1a-bc57-d62d12276731", "ContactStatus": "ACTIVE", "Name": "Edreams", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609844673240+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "13e0c942-24b2-4902-bfbe-622628c29355", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609844963560+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e8a4e723-b293-4e05-9808-526b7100e0d7", "ContactStatus": "ACTIVE", "Name": "Kleyman & Co", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845034590+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c0414936-1355-49b6-80e3-6ac02dab82b8", "ContactStatus": "ACTIVE", "Name": "The digital marketing bureau", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845096137+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e5c1bcae-9380-42ff-a8b0-e5d4ed7cb784", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845250670+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4d228c8a-94db-46c5-af5a-a558813aad1e", "ContactStatus": "ACTIVE", "Name": "MPSWORKS", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845289563+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3c46aaf6-33c4-44e9-accc-795d77536c22", "ContactStatus": "ACTIVE", "Name": "Ministry of Innovation", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845315267+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bc2a0527-5d41-4f36-9e57-f20830b31685", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845420560+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2a2cf292-686c-49a8-ac1b-265e98178e1c", "ContactStatus": "ACTIVE", "Name": "Brandcrowd", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845545217+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4985180a-4cca-4f83-8715-80fd3ec950af", "ContactStatus": "ACTIVE", "Name": "Al Falamanki", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845576440+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2e120385-465b-470a-8e80-23ede68ae4b8", "ContactStatus": "ACTIVE", "Name": "Ariss Co-Petroliss", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845662880+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "79045138-3c96-4db2-a55a-83747b4fa24b", "ContactStatus": "ACTIVE", "Name": "Hardees", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845897143+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "530c86ce-d283-497a-80de-5ec904e2832c", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609845920193+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0424029e-e668-4a20-8f24-e78656e811e3", "ContactStatus": "ACTIVE", "Name": "Government", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609952905240+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1755ee06-c209-40c8-949f-db4f55aa8407", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609952983813+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1996a4bf-0c3c-468b-a43f-0505bb5d01f4", "ContactStatus": "ACTIVE", "Name": "Instruments Garage", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953084707+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5b5c3afe-dfbc-4dd4-8944-f7362b3ca3f7", "ContactStatus": "ACTIVE", "Name": "Flying Formations", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953139807+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a2a1d8fe-2a7c-4c76-ae75-f5a36673e908", "ContactStatus": "ACTIVE", "Name": "Zaatar w Zeit", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953236097+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bd465ef8-6a21-4f37-a7f0-1be4cf81f4c1", "ContactStatus": "ACTIVE", "Name": "Sandwich w Noss", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953263503+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ca7825fe-9ec6-4a59-ac09-667ffbda790a", "ContactStatus": "ACTIVE", "Name": "Middle East Air", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953344980+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9e0dfce7-fb25-4813-9554-e90194ca5dc8", "ContactStatus": "ACTIVE", "Name": "<PERSON>ter Diner", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953363570+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5e0f83cd-f69f-45ba-bf04-8ef6d4e4b139", "ContactStatus": "ACTIVE", "Name": "Germania Aero", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953420160+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "73426616-8451-40a8-aaa9-1b9223654c22", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609953575563+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "98f6b387-dac7-4362-8e7e-85a8a22e14c7", "ContactStatus": "ACTIVE", "Name": "Boxcar", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958117253+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "19c260e7-8e36-4a00-bb37-b72227dab3a6", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958310367+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e99c6d31-a933-4887-ae49-a8435db6a836", "ContactStatus": "ACTIVE", "Name": "Airbnb", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958348450+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f70230a6-1d82-4774-8465-fef6759697cd", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958405597+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c7da486f-22c7-4fac-b4f0-84f5fc3e62e0", "ContactStatus": "ACTIVE", "Name": "The Natural Kitchen", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958497140+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "774d59de-4eb9-4f6c-85fa-910be3d1afb3", "ContactStatus": "ACTIVE", "Name": "LeadMaster Operating Company", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958568417+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a8d570cc-b093-4c7f-b281-c8f9a37b2e2e", "ContactStatus": "ACTIVE", "Name": "Selfridges London", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958710450+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "86459527-42f2-4a1e-b399-714242c02e54", "ContactStatus": "ACTIVE", "Name": "<PERSON> Toni <PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958789153+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9003f045-c81a-479d-b0e5-81886820afb4", "ContactStatus": "ACTIVE", "Name": "Post Office", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609958993237+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5a7fc838-c54d-4386-a9e0-52d4958d81f8", "ContactStatus": "ACTIVE", "Name": "EMERIX IP", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1609959226667+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ac4f3a79-75c8-4982-a86f-7f8143b2cd8d", "ContactStatus": "ACTIVE", "Name": "TFL Travel Authorisation", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610015626127+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "54ddd9e0-c521-4336-9550-f1138457bdd5", "ContactStatus": "ACTIVE", "Name": "G News", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610015650860+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ea995969-002a-4a3f-be70-c6ea99550aa8", "ContactStatus": "ACTIVE", "Name": "Blandfords Cafe", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610015699627+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5426a019-a037-4c0e-8df0-c178c78e6d8f", "ContactStatus": "ACTIVE", "Name": "Jamendo Sa", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016095263+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b5bd0c96-c1fd-45e3-b718-c6fdea209196", "ContactStatus": "ACTIVE", "Name": "Shell", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016168430+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d419856b-3ff8-4d9b-a3bd-29a9e66b7718", "ContactStatus": "ACTIVE", "Name": "Bstn Store", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016340640+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "33c5fe4d-16a7-4e41-8349-6ef045c799b8", "ContactStatus": "ACTIVE", "Name": "Aral Station", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c2b91be8-537c-4da8-9790-a844380ffaf6", "ContactStatus": "ACTIVE", "Name": "Muenchner Bank Eg", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "34d094ff-79d1-4045-955b-fe730ea2fb60", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "GBP", "Balances": {"AccountsReceivable": {"Outstanding": 300.0, "Overdue": 300.0}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f216e716-47fc-4e0f-bfa1-8852b481cbeb", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5117490f-ccb0-4a93-a522-cc9ca8686448", "ContactStatus": "ACTIVE", "Name": "Vereinigte Spk Im Maer", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e341f17d-8405-4319-bbd8-029ede2d3769", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016848517+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "795957a8-1435-4383-a942-d7bd8924e049", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016906930+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "915a39c1-f1f3-45ce-844f-d8127a280c1d", "ContactStatus": "ACTIVE", "Name": "Arial Station", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016935040+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6e0e193d-d821-4785-830e-5db20917ca26", "ContactStatus": "ACTIVE", "Name": "Lindemannstr. 65", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610016959793+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5c567e88-ff4b-4b1e-ae16-b7e184a3b3d7", "ContactStatus": "ACTIVE", "Name": "Urbaniste", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610017160007+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1876354b-7456-4f4a-8e4e-b9cfcb65e05b", "ContactStatus": "ACTIVE", "Name": "Crew Hut", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610017214100+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1c14adcd-f075-4ce6-90ca-384b35c96416", "ContactStatus": "ACTIVE", "Name": "Satsuma consultancy", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610017468717+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5c2a39ee-9108-4977-9816-f32ca9a8dc48", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610017573930+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "7146bded-3e8c-447c-b20a-5abbb15a5f51", "ContactStatus": "ACTIVE", "Name": "Pegasus", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610018476423+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8cd4708d-1f51-42bd-9a01-da52c836e7e9", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610018826960+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "45f923c4-ddfe-4f4a-8ae7-44fd2baaed18", "ContactStatus": "ACTIVE", "Name": "Salon Beyrouth", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610018880533+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "47b7f390-63d6-45e4-92e0-b762eafa86ab", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610018913773+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "71472536-038a-48c8-b1cb-33f1801c6142", "ContactStatus": "ACTIVE", "Name": "3 Colours Rule", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019037383+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "065de904-cb0b-4a58-988b-041846aa2cba", "ContactStatus": "ACTIVE", "Name": "Phoenicia Pharmacy", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019103040+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1f892379-23f7-4a8e-a315-2c20f43bdcf5", "ContactStatus": "ACTIVE", "Name": "Kings Center", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019125883+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "80f8c8b6-a87b-427b-97f5-0981fe71f44c", "ContactStatus": "ACTIVE", "Name": "Spinneys", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019179053+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a44700c1-2ffa-422f-a914-24c00b2cfef4", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>’s", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019202477+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "54a05355-ac66-4310-bb9c-87eb132733b1", "ContactStatus": "ACTIVE", "Name": "Linkedin", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019234460+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6114d132-75fc-4ff4-b4d9-b46ee5ead414", "ContactStatus": "ACTIVE", "Name": "Kababji", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019262663+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f1f9d9b0-b6be-4fe9-8762-956541f31efd", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019392320+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d3264ab8-e1e7-4879-bf96-c61c734e8c76", "ContactStatus": "ACTIVE", "Name": "Paname", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019413837+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1f68a0d8-c937-4598-805b-51e56d84d43f", "ContactStatus": "ACTIVE", "Name": "Bilal Itan", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019891273+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3967c606-dd10-4cb1-94ee-297008a1bcd5", "ContactStatus": "ACTIVE", "Name": "Hotel Phoenix", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019932037+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "981208b7-04ee-4462-8b15-881300ee8716", "ContactStatus": "ACTIVE", "Name": "NY Suites", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610019979273+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d79faf4b-8515-44fe-92d8-25ff247b8945", "ContactStatus": "ACTIVE", "Name": "Urbanista", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610020025147+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1b6d7e6f-ac56-43f7-9c5a-c5d0d6f44c37", "ContactStatus": "ACTIVE", "Name": "Le Relais De Venise", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610021646627+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b05030bc-3c32-4586-a66d-b8ceeedf1fed", "ContactStatus": "ACTIVE", "Name": "Eurowings", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610021808423+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "25769e8a-6a64-47a3-85d7-fa53a22baaaa", "ContactStatus": "ACTIVE", "Name": "BT", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610021871853+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "11132e04-348c-46b5-98a1-13b5e3c012e7", "ContactStatus": "ACTIVE", "Name": "BA Internet", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610021898497+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a21e2117-7b18-4006-907b-8ece25e4c219", "ContactStatus": "ACTIVE", "Name": "Total Service", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610050699503+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1f000025-5178-4926-bd92-a910590aaa0b", "ContactStatus": "ACTIVE", "Name": "Deliveroo", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610050827273+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ff1810e7-36ff-4a2a-acab-82ef6a1865a0", "ContactStatus": "ACTIVE", "Name": "Ikea", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610050874553+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "31cb610f-bfc4-4182-a0d9-a01d48f5dccf", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051011977+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0644e39e-766a-4ed5-94fa-4fd93e387128", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051443187+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "25dc552b-775b-4977-b5da-fce12964ca0b", "ContactStatus": "ACTIVE", "Name": "Station LBP", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051479780+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "7a374ed5-738b-4c51-ab7b-c9b29826fa11", "ContactStatus": "ACTIVE", "Name": "Flexwebs", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051584043+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e45221ac-7553-4ba5-bea4-bc2681fcd0da", "ContactStatus": "ACTIVE", "Name": "Mailtrack", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051702043+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "55b9dfd2-6f0d-441d-b09a-32237f19bb38", "ContactStatus": "ACTIVE", "Name": "Esso Station", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051854553+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a85bffb1-45ac-4903-953b-e7534c2c4e06", "ContactStatus": "ACTIVE", "Name": "Thai Square", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051889920+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c7da0bec-9e0e-4784-b853-c0ca55639690", "ContactStatus": "ACTIVE", "Name": "Netpoint Internetcall", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610051979610+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6755fed3-f437-452a-b673-30f89d493cb0", "ContactStatus": "ACTIVE", "Name": "Cab24 Gmbh", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610052419657+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "265ad1be-2c94-4a4c-ae55-6c7b893f0846", "ContactStatus": "ACTIVE", "Name": "Matratzen Direct", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610054744643+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "25403cc7-13b8-42be-aafe-9f7bc59bda30", "ContactStatus": "ACTIVE", "Name": "LeadMaster Operating Compan", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610054815360+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "85678d01-7420-49e9-8e6c-f563f31e21c7", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610054851160+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "baefcf01-a2fe-4281-808c-a00c83dc66cb", "ContactStatus": "ACTIVE", "Name": "Caliber Sal", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1610054898820+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c217f8b8-bd6d-4be3-9b30-2d934dd66e84", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621094370633+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5969a934-0d78-4ad2-8706-313c0c574905", "ContactStatus": "ACTIVE", "Name": "Xero", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621094662840+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "731500d5-1134-47ea-bc57-6a4e1188c55f", "ContactStatus": "ACTIVE", "Name": "Itunes", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621094757660+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "99d2b6e9-8d17-4028-81ba-48f019a850c0", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621094875520+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "db466414-2ef9-4edb-812a-6d8be57cd471", "ContactStatus": "ACTIVE", "Name": "Wellers", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621095025120+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "727cf150-5f3b-461d-a51c-cfd20761b036", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621097173503+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d1bb5ae8-aa38-4cae-b5fc-000c473e3dfe", "ContactStatus": "ACTIVE", "Name": "Octavia 2020", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1621940853630+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "ContactStatus": "ACTIVE", "Name": "L'atelier Nawbar", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "GBP", "Balances": {"AccountsReceivable": {"Outstanding": 1440.0, "Overdue": 1440.0}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a13ea20d-8ea1-434a-a64b-74aa3b5b49b0", "ContactStatus": "ACTIVE", "Name": "Seed Legals", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "eaed0e02-2b6d-4c1e-9f7c-b67fe9cdb483", "ContactStatus": "ACTIVE", "Name": "Skype", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c75e01b6-f534-4509-9b63-4021231e1dac", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1625135189390+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "70b9d5b0-d7c8-4dd4-95a9-fa2eeed26b14", "ContactStatus": "ACTIVE", "Name": "AWS", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1625827487517+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "927fbb99-7224-4583-9b3f-283a43c717f9", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1625827509063+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "ContactStatus": "ACTIVE", "Name": "L’atelier Nawbar", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1639127091617+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4fcb7eef-0d7d-4b4d-981b-d564a59cd0be", "ContactStatus": "ACTIVE", "Name": "High Tech XL Group B.V", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1646992377063+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4458fda2-3e3d-412d-8bb0-490d5555e394", "ContactStatus": "ACTIVE", "Name": "Amazon", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649149527837+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9915b1db-87ce-4951-9c54-6af73ffa4983", "ContactStatus": "ACTIVE", "Name": "Copyshark", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649149661663+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "686d22db-4a9a-4f0f-87ae-18074153e26e", "ContactStatus": "ACTIVE", "Name": "Uber", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649149770857+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c9fee6b0-f0eb-4999-8cda-c7a9b8860d42", "ContactStatus": "ACTIVE", "Name": "Steam", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150358960+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c6d2acf3-aeb3-4fd6-8a36-c55ec3fc2b8d", "ContactStatus": "ACTIVE", "Name": "Domain.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150377803+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "acda4133-496a-452a-90fa-e5332442391f", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150769153+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4a5dce32-2fb8-4414-a130-940021deebd0", "ContactStatus": "ACTIVE", "Name": "UK soft", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150829803+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "22e4064c-2b4d-408a-bcb6-59d470e1c15d", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150891350+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "32fa1542-c597-482a-86ee-62e35edcf0e2", "ContactStatus": "ACTIVE", "Name": "Netflix", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649150914070+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2dc4a793-ef18-4e57-a373-0a6266fb9883", "ContactStatus": "ACTIVE", "Name": "Playstation", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151038317+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "156a4d12-3364-4697-a5b3-8c6327611a2d", "ContactStatus": "ACTIVE", "Name": "<PERSON>ge", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151144893+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "22982dce-7fc3-49fd-9a5a-ab8c377dcfce", "ContactStatus": "ACTIVE", "Name": "Western union", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151453187+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "35ac8046-5d75-483c-b0c8-3245bc1eca2e", "ContactStatus": "ACTIVE", "Name": "Gray<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151526977+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4fb0030a-980b-408b-b860-7d0cedc4516f", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151633323+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4fe3cbe1-9e84-4cf9-849d-f7a5138de9dd", "ContactStatus": "ACTIVE", "Name": "HDR light studio", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151753977+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "19d68113-ea28-4a79-ac23-4347008c5fe2", "ContactStatus": "ACTIVE", "Name": "Shutterstock", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151826393+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "26bfb5aa-11ea-4054-9f20-ec20ad7ad2c7", "ContactStatus": "ACTIVE", "Name": "ABC", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649151835630+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "62047acd-919e-4443-98c7-4550cb667e50", "ContactStatus": "ACTIVE", "Name": "MOSTAFA ABED SALLOUM", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152050453+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9b6bc09e-1bbb-4f99-ab35-e4a914d664a0", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152198020+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "886320ce-0ce8-4ad4-a48d-268228ac6fef", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152287243+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c29a0b6a-d777-4e99-9d4a-a39505e16a1b", "ContactStatus": "ACTIVE", "Name": "Bio lab", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152386343+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "23c42e19-1431-401e-aa7b-3d34c41fbd9b", "ContactStatus": "ACTIVE", "Name": "Ministry of Health", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152444350+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "aadc3309-a25a-4326-866b-359bc029d19c", "ContactStatus": "ACTIVE", "Name": "Current Exchange", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152567953+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2c655c79-0507-404a-b154-2a4e347d7dc7", "ContactStatus": "ACTIVE", "Name": "Bitfinder", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152780597+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d5ead98d-4c80-4baa-9675-cac827f1c6c2", "ContactStatus": "ACTIVE", "Name": "Creative Market", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152921750+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0a7be3eb-e136-448f-9e07-ef669d46b9e4", "ContactStatus": "ACTIVE", "Name": "Art Station", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649152945820+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c63f6465-b949-418e-8d72-d34369d3f44d", "ContactStatus": "ACTIVE", "Name": "Adobe", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153059787+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d21427eb-4945-4807-8bd6-d738c5088dd8", "ContactStatus": "ACTIVE", "Name": "Greyscaleg<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153090737+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "10ac45af-005b-4bf1-97de-c4755ab2f9c3", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153116553+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f9b0aa9a-e2c6-4f5a-9c19-b1cbc8898acc", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>der", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153138693+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2293d72e-4f5a-4e2c-8ac0-0fdd04ff6afd", "ContactStatus": "ACTIVE", "Name": "Parisian", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153217823+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "602e7580-3eaf-45fd-8bc1-6c3fcbe7d489", "ContactStatus": "ACTIVE", "Name": "Hellolux", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153243413+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b9ad79f0-e540-401b-9803-e51ac2aa0eaa", "ContactStatus": "ACTIVE", "Name": "Knaldtech", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153285743+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "368d6360-63ed-4f94-b772-c016942d06e6", "ContactStatus": "ACTIVE", "Name": "Bel<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649153302083+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a52a074a-879d-4341-837d-f87070bcb044", "ContactStatus": "ACTIVE", "Name": "Parisian Hotel", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649158627673+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "57d1b603-b59d-4515-bfa3-8590ba8425b1", "ContactStatus": "ACTIVE", "Name": "Unknown", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1649159215370+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "50ebcd69-60b9-41ef-9d59-6dbfb0050a28", "ContactStatus": "ACTIVE", "Name": "Extra exchange", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1650022325383+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "35c794e7-fbfb-4c53-a626-b1fa26327dac", "ContactStatus": "ACTIVE", "Name": "Gocardless", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1650022489570+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "058588ef-5530-4cc7-a03d-d9a63c70972a", "ContactStatus": "ACTIVE", "Name": "Western Union (Bilal Itani)", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1650024119983+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "82e6f3b0-c596-464d-af1b-9fc0b5471dbf", "ContactStatus": "ACTIVE", "Name": "Currency Exchange", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1650025405193+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "ContactStatus": "ACTIVE", "Name": "KITU KALI LIMITED", "FirstName": "Kushil", "LastName": "LAKHANI", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX", "AddressLine1": "Flat 62", "AddressLine2": "Waterside Apartments", "AddressLine3": "Goodchild Road", "AddressLine4": "LONDON, Flat 62", "City": "London", "Region": "Middlesex", "PostalCode": "N4 2AJ", "Country": "GBR"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "Balances": {"AccountsReceivable": {"Outstanding": 107.0, "Overdue": 107.0}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "74f1fd08-9285-4355-b876-648b1b9fced6", "ContactStatus": "ACTIVE", "Name": "Beirut Duty Free", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b8de9ef4-5e7a-409c-b036-979642<PERSON><PERSON>c", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6f5a11e1-9b42-4694-8910-f056f10ccc96", "ContactStatus": "ACTIVE", "Name": "Lieferando.de", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739067410+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "16e20ad0-30b0-46a4-a2ee-01ea2942594e", "ContactStatus": "ACTIVE", "Name": "Coinbas", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739104593+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "25a13795-abba-4ba2-8408-3b97d044b156", "ContactStatus": "ACTIVE", "Name": "Coin base", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739115363+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "301985b5-b779-4d30-896b-c8a06af4a4d8", "ContactStatus": "ACTIVE", "Name": "Texaco", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739157820+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e9f5dd0a-abc0-4231-9038-b96f9eaf78e0", "ContactStatus": "ACTIVE", "Name": "Star Tankstelle", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739172987+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "32dcbc18-1951-4907-a3fa-f3dafe7cb2c3", "ContactStatus": "ACTIVE", "Name": "Media Market", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739196890+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9e8c5d87-2bb0-47ab-bdf7-1fd2b65b147c", "ContactStatus": "ACTIVE", "Name": "Amazon Market", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739228540+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1fa26cd8-385d-4d6d-a69a-0182fa7ee692", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739362173+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "183e18d9-865f-4fb3-8cf3-5d294652acd0", "ContactStatus": "ACTIVE", "Name": "<PERSON>ern<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739372493+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5ee83588-2117-4906-95fc-637801cfe323", "ContactStatus": "ACTIVE", "Name": "Istanbul Airport", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739496743+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "80a9b18c-3082-4561-98db-098c272a428a", "ContactStatus": "ACTIVE", "Name": "International Driver Association", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1651739559003+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3dc9b218-a791-4602-aad4-eba90d75168c", "ContactStatus": "ACTIVE", "Name": "Nabula", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1653931776550+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "32652e65-8386-40c9-98f1-edfd3c987e8d", "ContactStatus": "ACTIVE", "Name": "Mazen al AKKAOUI", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1656001477233+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "27aef98c-8c62-47cc-b025-e7549a6bc972", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON> has hat <PERSON>ma kat", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1656001587450+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "588c936e-b199-4048-bf3a-8d43669cb1a2", "ContactStatus": "ACTIVE", "Name": "Youtube", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1656001615090+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9c45eb8a-662a-4a1c-a385-e6365644f748", "ContactStatus": "ACTIVE", "Name": "The Jazz", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1656485432373+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9e73f3a8-b490-47fe-9418-478bb0f4d48b", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1656931572697+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d2e3bdbe-cd8e-4ebd-bfb7-b128d313c727", "ContactStatus": "ACTIVE", "Name": "INCORP", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "1321 5th St", "City": "Boulder", "Region": "CO", "PostalCode": "80302-5860", "Country": "USA"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1657040074437+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c4cc8f85-22e6-48c1-912f-dcbd044eed12", "ContactStatus": "ACTIVE", "Name": "Hq Automaten Rheinbahn", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1657620839990+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d37325c0-5bc6-4972-a6a1-83d5c927cbbf", "ContactStatus": "ACTIVE", "Name": "Time In Jazz", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1657620907490+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "712fe562-6fe7-4e60-84f9-51eb7620a8d5", "ContactStatus": "ACTIVE", "Name": "Marche Movenpick Dtl. Gmb", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1657621347017+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "100f0d28-efb1-4345-b59b-cedb5b804596", "ContactStatus": "ACTIVE", "Name": "Big Chefs Sabiha Gok", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1657621365100+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b233584a-1b1b-4479-b4e6-50bdd57c90a1", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1658324953527+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5a640ddb-9854-4bf8-9de8-4b01e9feadc8", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1662543049643+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e097e16a-a50d-422a-add8-002267f3bb0e", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1662543266713+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a1492643-1ffc-413f-989e-8d0786c1da17", "ContactStatus": "ACTIVE", "Name": "Neb<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1662543572583+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9cfab9b9-0a0a-489e-bd86-4e862aa5bf34", "ContactStatus": "ACTIVE", "Name": "Indesign", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1663151766833+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2484ac36-43d6-4349-afd2-b0d224fdd32c", "ContactStatus": "ACTIVE", "Name": "London Law Collective", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1664444225780+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d3136e6e-c7f9-4fab-8eb9-56cc41bd5cb8", "ContactStatus": "ACTIVE", "Name": "Stripe", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1664975102167+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cba23de1-c0c2-4add-87db-055c894d62d2", "ContactStatus": "ACTIVE", "Name": "Wix.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1665657122757+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4125b0f4-d988-4c26-b2b4-57745d00b860", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1665657301393+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "34f2a5e1-d094-48f1-bf76-de5ffdbab2d1", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1665657325867+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6695b78b-1e8b-413c-91e5-d46d858f422d", "ContactStatus": "ACTIVE", "Name": "Class", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1665657387170+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "568f5b90-1905-4e2f-8484-ac87529c9f03", "ContactStatus": "ACTIVE", "Name": "Lines and Arts s.a.r.l", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1666257590083+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8a633d0f-0f9d-4fae-b23f-015adff74216", "ContactStatus": "ACTIVE", "Name": "ICO", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1666261465507+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5a331741-5d73-4c8e-9e19-971718fcd829", "ContactStatus": "ACTIVE", "Name": "ESC S.A.L", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX", "AddressLine1": "Patchi Building, Caracas, Kazaa Street", "City": "Beirut", "Region": "Beirut", "Country": "Lebanon"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1667325764313+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "ContactStatus": "ACTIVE", "Name": "Packegha", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "Balances": {"AccountsReceivable": {"Outstanding": 673.83, "Overdue": 673.83}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "df098493-c3b3-4732-8972-ab3d226e0760", "ContactStatus": "ACTIVE", "Name": "Arayess Furniture", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "960cb640-f3d5-484a-8b6a-2fca2e97e854", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ef6cc594-d1a9-40d1-9185-d1af03c71b8f", "ContactStatus": "ACTIVE", "Name": "WHISH Money", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1669024665577+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a523aa85-7c66-4a8a-b056-55b6678ca47d", "ContactStatus": "ACTIVE", "Name": "Local Computer Vendor", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1669024881443+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "04b4cd0b-2ea5-4933-94dd-7afc3568c26b", "ContactStatus": "ACTIVE", "Name": "Udemy", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1669743339367+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d4c7b941-0364-4bda-b217-ae20922ef904", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1672744871383+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "96ae6042-7d86-4405-9947-df32a932928f", "ContactStatus": "ACTIVE", "Name": "Lumi SAL", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1672745052497+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "40d1a8ee-045f-49a1-9614-0aaae327fc1c", "ContactStatus": "ACTIVE", "Name": "<PERSON>er", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1673879197623+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e30cd60b-9205-4936-bf94-f37cbca82a87", "ContactStatus": "ACTIVE", "Name": "Amazon DE", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1673879383060+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "EUR", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9a94d557-c61e-485f-9370-c074abee53da", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1675859903947+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a03bc807-5e4b-4e0b-b31d-b226d0fed163", "ContactStatus": "ACTIVE", "Name": "MAZEN AKKAOUI", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1675859919243+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f2277e3b-0f41-4a29-9662-e6358ab81ebb", "ContactStatus": "ACTIVE", "Name": "Cloudfare", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1677260682050+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f9ca41d8-0bca-4115-8351-b680399a6b31", "ContactStatus": "ACTIVE", "Name": "The Web Addicts", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1678710697617+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1f75f07b-c8d6-4ab3-b721-2ca70558bd11", "ContactStatus": "ACTIVE", "Name": "Chatgpt", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1680525361303+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8d56c42a-7ac9-4248-8b49-c6b35f5227a7", "ContactStatus": "ACTIVE", "Name": "Netlify", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1684238214563+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "********-c47f-467e-8c08-0e76a67c6cfa", "CompanyNumber": "********", "ContactStatus": "ACTIVE", "Name": "B<PERSON>", "FirstName": "<PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 7600.0, "Overdue": 7600.0}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "939a9762-cf4a-4117-ba00-af4d398f6f71", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9042eb5b-2f2b-40b6-9af2-8e0338b4c668", "ContactStatus": "ACTIVE", "Name": "TBA", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b31f92d3-77db-4f82-9641-b00d18b07f5b", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1687774532073+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "53c4c9f8-1f45-4ce0-91a1-30f89863e5a0", "ContactStatus": "ACTIVE", "Name": "Open ai", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1687774835013+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9f1e1b8f-89b9-4b34-b613-2cf30c3e32a8", "ContactStatus": "ACTIVE", "Name": "Companies house", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1687774926963+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d6e5e6fb-883e-4f25-adcd-20b760dc81c7", "ContactStatus": "ACTIVE", "Name": "Sara I<PERSON> al natour", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1687774981843+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX", "AddressLine1": "28 E 10th Street", "City": "New York", "Region": "New York", "Country": "USA"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1688974346283+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3a3cfd91-15ee-4639-af07-d05033c5aebf", "ContactStatus": "ACTIVE", "Name": "W. <PERSON> & Sons.", "EmailAddress": "<PERSON>.<PERSON><PERSON>@gmail.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1691078282287+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "dcd3d599-f5df-4c89-825a-5a579964bcff", "ContactStatus": "ACTIVE", "Name": "Face Junkie Ltd", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1691132844213+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "09aef4c5-6940-4b4f-a7a1-28cbfff60cb2", "ContactStatus": "ACTIVE", "Name": "Chaos", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692026996110+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c701a41b-26e0-4b50-803d-d891b65eeacc", "ContactStatus": "ACTIVE", "Name": "Carrefour", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692027065863+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9dcc566e-0658-47f6-a997-b772a86092f6", "ContactStatus": "ACTIVE", "Name": "Talabat", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692027158847+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6e6bd6bf-71d5-465a-9ba1-713574782318", "ContactStatus": "ACTIVE", "Name": "Adobe *Id Creative Cl", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692028659007+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ba89ef2e-6c39-4da2-9b89-e3f7942d07c9", "ContactStatus": "ACTIVE", "Name": "Chatgpt Subscription", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692028725083+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5d91048e-7d2c-48d6-b76e-db49b42b9a9e", "ContactStatus": "ACTIVE", "Name": "Facebk R94xps7bf2", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776270493+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "34726003-45db-458f-98b2-d8e64a21932a", "ContactStatus": "ACTIVE", "Name": "Xero Uk Inv-16158308", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776293710+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d12c5971-6755-4597-82e6-671fe7062519", "ContactStatus": "ACTIVE", "Name": "Playstation Network", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776323803+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5c28f666-c64e-45f5-b703-a4df1440d82c", "ContactStatus": "ACTIVE", "Name": "Nasrawi Stores", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776345853+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e4835a1d-cc19-4527-94d9-e6fb9b45cc9f", "ContactStatus": "ACTIVE", "Name": "Apple.com/us", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776396340+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d49e99b7-5538-4951-a105-ea01cf12b4b7", "ContactStatus": "ACTIVE", "Name": "Carrefour Jordan", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776408537+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "57bf6c50-d92f-462f-935f-e2bcaf24419a", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776425927+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b5d5be37-4a20-4a9e-9695-8d6a102d2424", "ContactStatus": "ACTIVE", "Name": "MAGALI MOUNIR SAWMA", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776449977+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e3327f5e-7582-4f5d-b357-5ccabe4dc138", "ContactStatus": "ACTIVE", "Name": "Openai", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1692776513910+0000)/", "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "USD", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1e375b2a-e809-41eb-9db7-ded249e860f4", "ContactStatus": "ACTIVE", "Name": "Warmupinbox", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1694419772957+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "78c1bce0-5afa-430b-b89b-ce67e3a74463", "ContactStatus": "ACTIVE", "Name": "Iconscout.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1694419826253+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1bc7590d-3234-4663-996d-4bbf84cbebfb", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c353fc0e-9f29-4f7a-a4e5-c33a236168db", "CompanyNumber": "3857916-601", "AccountNumber": "4002", "ContactStatus": "ACTIVE", "Name": "E-MOOD SAL", "FirstName": "Bernadette", "LastName": "Saliba", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "ContactPersons": [{"FirstName": "<PERSON>", "LastName": "<PERSON><PERSON><PERSON>", "EmailAddress": "<EMAIL>", "IncludeInEmails": true}, {"FirstName": "<PERSON><PERSON><PERSON>", "LastName": "<PERSON><PERSON><PERSON>", "EmailAddress": "<EMAIL>", "IncludeInEmails": true}], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1b1f97fc-7b69-4855-9270-4a0ea91abf3f", "ContactStatus": "ACTIVE", "Name": "HMRC", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1695108617243+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9161710c-2780-4956-834c-d727891e754d", "ContactStatus": "ACTIVE", "Name": "Pitch.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1695108635817+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a4c0361d-4af8-4e19-af2c-278563a501ac", "ContactStatus": "ACTIVE", "Name": "Apollo", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1695108680707+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "235d1e7b-db2a-41da-adb3-02e9e4719cae", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1695879478327+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "24db7fe0-2a19-4bb0-af43-aaacce2fff94", "ContactStatus": "ACTIVE", "Name": "<PERSON> - Sin Dental", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5256b41f-fcd7-404b-aff3-7621ff5ab761", "CompanyNumber": "********", "AccountNumber": "Decision Logic Ltd", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "Tactopia Building Olympic Way,", "City": "Warrington", "Region": "England", "PostalCode": "WA2 0YL", "Country": "United Kingdom"}, {"AddressType": "STREET", "AddressLine1": "actopia Building Olympic Way,", "City": "Warrington", "Region": "England", "PostalCode": "WA2 0YL", "Country": "United Kingdom"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "Website": "chillblast.com", "Balances": {"AccountsReceivable": {"Outstanding": 224.61, "Overdue": 224.61}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0aaeddc0-89c2-4934-8454-2c6439e5e749", "ContactStatus": "ACTIVE", "Name": "Information Commissioners Office", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d718e1ff-9b86-42ca-a277-24cabe98acef", "ContactStatus": "ACTIVE", "Name": "Loom", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c359d494-15fa-4571-b4c0-5f921127a231", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1701618148770+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "aac81061-b083-4b06-86e6-6c5d1244f9cc", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1701618188797+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "214f21c1-b56a-4c7d-b141-377f9dda5e3d", "ContactStatus": "ACTIVE", "Name": "BD", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1701618337613+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ace93e50-9431-46cd-ae98-f6c0c613a445", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1701618562687+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4f5a326c-98af-41c7-9ffa-af60382396cb", "ContactStatus": "ACTIVE", "Name": "TOMO BOTTLE LLC", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "123 <PERSON>, West", "City": "Hartford", "Region": "Connecticut", "PostalCode": "06117", "Country": "United States of America", "AttentionTo": "<PERSON>"}, {"AddressType": "STREET", "AddressLine1": "123 <PERSON>, West", "City": "Hartford", "Region": "Connecticut", "PostalCode": "06117", "Country": "United States of America", "AttentionTo": "<PERSON>"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1704876522910+0000)/", "IsSupplier": false, "IsCustomer": true, "ContactPersons": [{"EmailAddress": "<EMAIL>", "IncludeInEmails": true}, {"EmailAddress": "<EMAIL>", "IncludeInEmails": true}], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "048b6679-aa59-4535-a40f-c317aa657cb7", "ContactStatus": "ACTIVE", "Name": "Digital-ur.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1707151555183+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a0037636-6701-4738-9c67-f8dde70be7f2", "ContactStatus": "ACTIVE", "Name": "Rooftop Resto", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709550850253+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "269b42a4-8345-4778-9d13-22b3b8ea1d98", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709550887060+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bf758863-2d9f-4195-805b-771df7275b03", "ContactStatus": "ACTIVE", "Name": "Taxi Qatar", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709550917773+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0153c879-05f2-43cc-b670-6a697768de71", "ContactStatus": "ACTIVE", "Name": "Spectrum Solution", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709550942497+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cc6952b6-f5ef-4cde-a7b2-d2fd4417742f", "ContactStatus": "ACTIVE", "Name": "Pizza Maria", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551015653+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e187b321-b961-48dc-a2e0-ade6ed07c6a5", "ContactStatus": "ACTIVE", "Name": "W <PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551096793+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b717f3e3-8a7a-4a35-8900-57253a2ae2e2", "ContactStatus": "ACTIVE", "Name": "<PERSON>f Food Stuff", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551151853+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "234eb741-2f05-4481-ad3d-f3758318bfc8", "ContactStatus": "ACTIVE", "Name": "Blue Salon Sale", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551256150+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3b16f6dd-2ffc-43f6-956c-6dd49cf267b3", "ContactStatus": "ACTIVE", "Name": "Berries and Melons", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551282113+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "782e9512-0144-43c5-ab04-191ea81884a5", "ContactStatus": "ACTIVE", "Name": "Oryx Corniche Development", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551388200+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "82113cd1-0482-4044-9753-11347406cdf8", "ContactStatus": "ACTIVE", "Name": "Lily Cafe", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551510293+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6bd53a05-0dab-4f0c-ab70-ad9c0c0bc344", "ContactStatus": "ACTIVE", "Name": "ATM Ain el Mr<PERSON>eh", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709551726143+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "23e6e10e-74fa-424c-87a9-f4322878aa87", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710169794567+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "52faf256-c6f2-4e82-9ffa-330f9573345c", "ContactStatus": "ACTIVE", "Name": "Calude.ai", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710169829767+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c20e85b1-5bb6-481b-9efa-ca8227dbe04c", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710169878633+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c0d06ea2-4a36-41e9-af24-126ccf7317ff", "ContactStatus": "ACTIVE", "Name": "Medco", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710169927110+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "461d37f9-1af7-4fb3-ab43-8eafd7778a01", "ContactStatus": "ACTIVE", "Name": "Zara Home", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710170036130+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c573bd5d-8568-4bdf-b94b-fce2ef315135", "ContactStatus": "ACTIVE", "Name": "Card cash back", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710170131217+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8f0d629b-5e13-449b-b449-3a9eddb417cb", "ContactStatus": "ACTIVE", "Name": "Qwilr.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710170240300+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "edda5459-0013-47c8-a8ab-85a53e156a62", "ContactStatus": "ACTIVE", "Name": "Home Industrial", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1710945017387+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d289c5cc-12dd-4cc8-baf2-a18ba5413746", "ContactStatus": "ACTIVE", "Name": "La Calle", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1711165895043+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6d3415cc-7d1d-490c-9124-946880a7e079", "ContactStatus": "ACTIVE", "Name": "Virgin Megastores", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1711166036733+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9d49b453-52e6-46af-a9c5-7ece6ee9906b", "ContactStatus": "ACTIVE", "Name": "Phoenicia Beirut", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1711166253860+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "acc4b185-82a7-48d7-a459-4b6170fa3293", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1711167165613+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4189c0a3-29dd-49af-909b-eb5b117733f4", "CompanyNumber": "3124486", "ContactStatus": "ACTIVE", "Name": "Hosny Homany Industrial Company SAL", "EmailAddress": "<EMAIL>", "TaxNumber": "3124486", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "<PERSON><PERSON> - <PERSON><PERSON> str", "City": "<PERSON><PERSON><PERSON><PERSON>", "Region": "South Lebanon", "Country": "Lebanon"}, {"AddressType": "STREET", "AddressLine1": "<PERSON><PERSON> - <PERSON><PERSON> str", "City": "<PERSON><PERSON><PERSON><PERSON>", "Region": "South Lebanon", "Country": "Lebanon"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT", "PhoneNumber": "760004", "PhoneAreaCode": "76", "PhoneCountryCode": "+961"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "USD", "Website": "homanyindustrial.com", "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b84e500b-6d9f-4107-94c5-d2ba8c3ddeba", "ContactStatus": "ACTIVE", "Name": "Bank Audi", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "212c5bfa-88d9-468e-a64f-f6f695809152", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>ck", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "db1b95af-9557-4915-b02b-8cbaa3f3442e", "ContactStatus": "ACTIVE", "Name": "Pillvery", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1713264268517+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "eac37ea3-363e-4cce-a0db-0b853b1ca0eb", "ContactStatus": "ACTIVE", "Name": "Rustic", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715003940093+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "61a89787-e433-4f10-8172-e049067bb48f", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715003957367+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9e961fc6-1759-420c-a223-c09a0ecd3a91", "ContactStatus": "ACTIVE", "Name": "<PERSON>l", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715003979907+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2dc617eb-7800-4e44-a2ce-45aac591f07b", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004117673+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "fa92f711-81ed-451e-83fd-db4c07044c08", "ContactStatus": "ACTIVE", "Name": "Total", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004167953+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "13a1bb30-e37c-4fe0-8345-4dd68bf82331", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004192893+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "394b1fce-8cd7-4c92-8624-dea44181ad9b", "ContactStatus": "ACTIVE", "Name": "<PERSON>ma tabbara", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004223510+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a023bf21-d242-4b01-945d-0127d8790a88", "ContactStatus": "ACTIVE", "Name": "Mongodb", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004244067+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4943ab18-8aff-4821-9e00-30417d7cd680", "ContactStatus": "ACTIVE", "Name": "Le Relais <PERSON>cote", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004281883+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e3a800af-8525-4e1c-84ff-0af9ffa22a11", "ContactStatus": "ACTIVE", "Name": "Turbosquidshutterstock", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004307920+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ea1f6b78-6c44-463f-87c5-954cbff90fff", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>ani", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715004398527+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9219076f-a9e0-458c-b1d2-2c8225bc22cd", "ContactStatus": "ACTIVE", "Name": "Shortwave", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1715783392893+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "64b0941c-4c6a-481e-b40d-7966801131e4", "ContactStatus": "ACTIVE", "Name": "The Valis Corner", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1717501475587+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6d14f9ca-9bed-481d-b27d-656fe61e362a", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1717501524490+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0cfb99d7-d80e-4722-b257-93b528c11153", "ContactStatus": "ACTIVE", "Name": "Patzilla.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1717501813313+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bdd026f2-f594-416b-a1c1-4ddd3ab2e838", "ContactStatus": "ACTIVE", "Name": "<PERSON> ai", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1718877080177+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "814bdd0c-d580-4753-8fac-2013bfafb349", "ContactStatus": "ACTIVE", "Name": "Alfa", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1718976476407+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2023f3ee-c580-4e68-84ff-1dc6bf9cf308", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1718976498413+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d7b48f1b-4f84-40db-806c-bc51d1c23dfc", "ContactStatus": "ACTIVE", "Name": "Classic Phone", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1718976542907+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f94984a2-72b2-48b4-b4df-a1dab4fff2cc", "ContactStatus": "ACTIVE", "Name": "Mysodapdfs.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1718976603160+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "882e6a12-7e0e-4a1e-a11b-e0d984051979", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1720627695650+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1f93b072-8101-442d-ac04-811dd7afdf62", "ContactStatus": "ACTIVE", "Name": "Machrou3 Cafe", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1720627897180+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "dce04a2f-7899-41a0-b0ae-2f800eee959e", "ContactStatus": "ACTIVE", "Name": "The Nook", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1720627946673+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b6ecfc1c-3aa0-4bf8-92df-ad51a0852909", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1720627961530+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f74bd2d0-3e71-4fc9-9fb3-6208e9e32edc", "ContactStatus": "ACTIVE", "Name": "Go daddy", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1720628049947+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "29731877-54a6-4b77-86ea-88d14f2c718f", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311224683+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cdfb4142-185a-41a1-b326-9b0b108ef3c1", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311231533+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "89044016-792f-4c07-8a7b-fd10f071f9e5", "ContactStatus": "ACTIVE", "Name": "Ejm Culinary Concepts", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311273960+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cb98be6e-7180-435c-9826-dbcfaed40b72", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311282403+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0bb826d0-0a0e-44ed-aed3-5ed55044249f", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311289290+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "7f0c6c54-e63d-4118-8db8-36457f4732ec", "ContactStatus": "ACTIVE", "Name": "Pccomponentes", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311303510+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1ca34dc4-be66-4721-8b12-0b1ac37e3745", "ContactStatus": "ACTIVE", "Name": "Rustic Bar And Eater", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311358480+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "da006b77-94e2-447c-98b8-4e6e19028d93", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1721311391077+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f946db40-3a06-4c24-96c1-beb1ca60cedb", "ContactStatus": "ACTIVE", "Name": "Mass", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969675623+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "168fb2a9-d978-4562-b2ba-565d44c60d00", "ContactStatus": "ACTIVE", "Name": "Masswise", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969681060+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "19282d2c-3103-4f82-bb50-3fc1d1eecf0a", "ContactStatus": "ACTIVE", "Name": "Apple.com", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969759147+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "03a4cbdf-e03f-45db-90c1-b92fe00d030c", "ContactStatus": "ACTIVE", "Name": "Boho", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969767127+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "48997104-02b9-4b35-acd6-6fbe396a5e37", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969793727+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "dd33981a-c170-45aa-a0c3-e313f9d1e7dc", "ContactStatus": "ACTIVE", "Name": "Anthropic", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969828370+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "36215f7c-3c1b-4d94-a92e-2e229e78b280", "ContactStatus": "ACTIVE", "Name": "Wadi", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969867303+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "d61480fd-1b60-49dd-9f87-856788d57661", "ContactStatus": "ACTIVE", "Name": "Jungle Fever Coffee", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969898223+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0a84abef-efcd-404d-82cd-ae3563dcfa6b", "ContactStatus": "ACTIVE", "Name": "Hamza center", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969956073+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a7b57ee0-ec8c-4870-90ad-948856ce5747", "ContactStatus": "ACTIVE", "Name": "Em Sharif Cafe", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725969993153+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "280a5ccc-f209-4fb8-ad40-18d215057cf0", "ContactStatus": "ACTIVE", "Name": "Buffol wings", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1725970004573+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a22cfeb7-d0d9-4662-8806-e48085d7c304", "ContactStatus": "ACTIVE", "Name": "Calude AI", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1727438949670+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b81e7161-4263-41cf-b502-80a71122659a", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1727438959193+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2a714578-59bf-47e7-b084-ed741dd56979", "ContactStatus": "ACTIVE", "Name": "Noho Garden", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1730200443583+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a36c3d9d-5b3e-4bb0-aada-b995fc1138e6", "ContactStatus": "ACTIVE", "Name": "Amazi", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1730200666553+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cf67f964-6ffb-49cb-8a97-7e8d63c27e4e", "ContactStatus": "ACTIVE", "Name": "Burger King", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1730200676340+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b396ecd3-37eb-4adc-8cd6-8e5fad606b7d", "ContactStatus": "ACTIVE", "Name": "Cousins Supermarket", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1730200684533+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "633bec19-657a-4ad3-8733-97b5abc78c26", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b37b247e-6658-433d-9599-aaf24c1ad232", "CompanyNumber": "********", "ContactStatus": "ACTIVE", "Name": "UPOD MEDICAL LTD", "FirstName": "<PERSON>", "LastName": "Banks", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": true, "Website": "midas-ic.com", "Balances": {"AccountsReceivable": {"Outstanding": 177.09, "Overdue": 177.09}, "AccountsPayable": {"Outstanding": 0.0, "Overdue": 0.0}}, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "7a54d9b3-bf2c-4f9b-b5bd-beae98f85953", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "6fcd757c-765b-47c5-8659-d01ca6daa86c", "ContactStatus": "ACTIVE", "Name": "RJ", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ec9ac4ee-8031-466f-8b79-f218b80a2d01", "ContactStatus": "ACTIVE", "Name": "Shotgun", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1735911581567+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "447ed5b4-3a94-4e75-a28e-2c3a367624a9", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1735915540010+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3c8885a1-d5a3-4ffc-8787-fbcdc25ebc54", "ContactStatus": "ACTIVE", "Name": "Stackblitz", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1735917469503+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "399ba9f7-5d40-405d-a85a-bc1750e3204e", "ContactStatus": "ACTIVE", "Name": "Openrouter", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163589790+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "dc931b47-7dff-4728-815b-3b9029b5e1ed", "ContactStatus": "ACTIVE", "Name": "<PERSON> and the Juice", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163611743+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2c5112bb-bcab-436f-8741-4ad1c7e6de38", "ContactStatus": "ACTIVE", "Name": "Docker Inc.", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163645667+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "384f66f6-fe01-4ee9-99c4-714a8ffd2d6c", "ContactStatus": "ACTIVE", "Name": "A.station", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163682333+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4b6f6813-4d79-4ca3-94de-68ac94988e9a", "ContactStatus": "ACTIVE", "Name": "Pharma<PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163701903+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5301b2e5-9d1a-4811-9f47-7abcdb147ced", "ContactStatus": "ACTIVE", "Name": "Italy On The Med Sal", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1740163709747+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "86298238-726f-40b0-90f9-d285d55f3218", "ContactStatus": "ACTIVE", "Name": "Cyrano", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1741885595117+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "74f75f13-a138-4782-bbab-4e45cce03fdb", "ContactStatus": "ACTIVE", "Name": "Abacus.ai", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1744361648897+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "99d4005f-b421-43bf-b908-4bab672352a2", "ContactStatus": "ACTIVE", "Name": "Windsurf", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1744361676900+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "31135d5e-e3d6-4eea-95c2-7b95fc094bfc", "ContactStatus": "ACTIVE", "Name": "Raycast", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1753374935673+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ff5df8c4-8283-484d-95eb-96985a0d229f", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1753375043407+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "62586e15-a709-4fbe-b013-58a2561ef7d6", "ContactStatus": "ACTIVE", "Name": "Genspark", "Addresses": [{"AddressType": "STREET"}, {"AddressType": "POBOX"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1753375055530+0000)/", "IsSupplier": false, "IsCustomer": false, "HasAttachments": false, "HasValidationErrors": false}], "summary": {"count": 377, "first_record_date": null, "last_record_date": null}}
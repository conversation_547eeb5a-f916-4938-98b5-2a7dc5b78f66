{"metadata": {"dataType": "Chart of Accounts", "extractedDate": "2025-07-29T06:37:28.681727", "organization": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "recordCount": 107, "version": "2.0.0"}, "accounts": [{"AccountID": "87a6e267-fb49-47f3-becf-1ac0470b55a7", "Name": "WISE AED", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "*********", "BankAccountType": "BANK", "CurrencyCode": "AED", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "eb799da2-8d86-4be4-8563-1e3857b0069e", "Name": "Revolut EUR Main", "Status": "ARCHIVED", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "xIIKfliSIWTXXLfTsSwibkyiLe", "BankAccountType": "BANK", "CurrencyCode": "EUR", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c70c977e-653c-444b-8e8c-26a617164164", "Name": "Cash", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "Cash", "BankAccountType": "BANK", "CurrencyCode": "USD", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "2bc79874-9a8c-4722-a319-88702e96cf4f", "Name": "Barclays (old)", "Status": "ARCHIVED", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "123456*********00000", "BankAccountType": "BANK", "CurrencyCode": "GBP", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "115e2431-6dcb-42e6-826e-97db4a3ebc6d", "Name": "Revolut GBP Main", "Status": "ARCHIVED", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "**************", "BankAccountType": "BANK", "CurrencyCode": "GBP", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "ad8bd36a-dd31-49d9-9de1-b3488f5f66e4", "Name": "Revolut USD Main", "Status": "ARCHIVED", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "L5lXZXExPwliwR9sNbmoftGhYJ", "BankAccountType": "BANK", "CurrencyCode": "USD", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "64dca2b4-2605-431a-a8c2-cb9c6cbc68f4", "Name": "Bilal Itani - USD", "Status": "ARCHIVED", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "0000000", "BankAccountType": "BANK", "CurrencyCode": "GBP", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "a7a9c628-11d5-49d8-ae93-d676cff2939d", "Name": "GoCardless-GBP", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "*********", "BankAccountType": "BANK", "CurrencyCode": "GBP", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "********", "BankAccountType": "BANK", "CurrencyCode": "EUR", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "********", "BankAccountType": "BANK", "CurrencyCode": "GBP", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Name": "WISE USD", "Status": "ACTIVE", "Type": "BANK", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "BankAccountNumber": "**********", "BankAccountType": "BANK", "CurrencyCode": "USD", "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "6ef45790-b314-45f5-a111-4d4489f7b01b", "Code": "001", "Name": "Cash Reserve", "Status": "ACTIVE", "Type": "CURRENT", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f92d6be5-3b16-4f0d-93bc-cc99af5e6a18", "Code": "100", "Name": "Currency Exchange", "Status": "ACTIVE", "Type": "CURRENT", "TaxType": "NONE", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS", "ReportingCodeName": "Assets", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "a16ff26c-4fa3-4cf8-9865-8c29ec91e6f6", "Code": "200", "Name": "Sales", "Status": "ACTIVE", "Type": "REVENUE", "TaxType": "NONE", "Description": "Income from any normal business activity", "Class": "REVENUE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "REV.TUR.SAL", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c14b8e6e-0717-458d-a63a-d0848de35a84", "Code": "250", "Name": "Modeling Fees", "Status": "ACTIVE", "Type": "REVENUE", "TaxType": "NONE", "Class": "REVENUE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "REV", "ReportingCodeName": "Revenue", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "81f3d6f7-11bf-4c1a-a4bd-4017aa24e262", "Code": "260", "Name": "Other Revenue", "Status": "ACTIVE", "Type": "REVENUE", "TaxType": "NONE", "Description": "Any other income that does not relate to  normal business activity and is not recurring", "Class": "REVENUE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "REV.OTH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c77b0ec0-3b59-4fe9-bb5a-cd8d6b89e2b9", "Code": "270", "Name": "Interest Income", "Status": "ACTIVE", "Type": "REVENUE", "TaxType": "NONE", "Description": "Gross interest income", "Class": "REVENUE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "REV.INV.INT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "b76376a7-2e8e-485c-bd11-8ef218bd3411", "Code": "280", "Name": "Tax refund", "Status": "ACTIVE", "Type": "REVENUE", "TaxType": "NONE", "Class": "REVENUE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "REV", "ReportingCodeName": "Revenue", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f6b51703-06fa-4a13-ab7b-20e57b00e513", "Code": "310", "Name": "Cost of Goods Sold", "Status": "ACTIVE", "Type": "DIRECTCOSTS", "TaxType": "NONE", "Description": "Cost of goods sold by the business", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.COS", "ReportingCodeName": "Cost of Goods Sold", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "e01fe930-c08c-48c2-b5e1-6e1fba5b3b33", "Code": "320", "Name": "Direct Wages", "Status": "ACTIVE", "Type": "DIRECTCOSTS", "TaxType": "NONE", "Description": "Payment of wages/salary to an employee whose work can be directly linked to the product or service", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.WAG", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "5ed0c5a0-c176-4255-ad98-21492e345607", "Code": "325", "Name": "Direct Expenses", "Status": "ACTIVE", "Type": "DIRECTCOSTS", "TaxType": "NONE", "Description": "Expenses incurred that relate directly to earning revenue", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.COS", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "d109eb52-5dcc-43fb-8774-f64b3c73cae7", "Code": "400", "Name": "Advertising & Marketing", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred for advertising and marketing", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.ADV", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "914713ce-932d-44b4-a2a9-dc34f0f71c29", "Code": "401", "Name": "Audit & Accountancy fees", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred relating to accounting and audit fees", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FEE.AUD", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "535d5496-53b2-41a2-9401-9f4ab0a763c2", "Code": "402", "Name": "Market Research", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": true, "ReportingCode": "EXP", "ReportingCodeName": "Expense", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "0ee22836-f53f-4101-9503-0316a899c483", "Code": "404", "Name": "Bank Fees", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Fees charged by your bank for transactions regarding your bank account(s)", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FIN.BNK", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "3ddbf878-9a41-472e-96e0-f36caa6bc43e", "Code": "408", "Name": "Cleaning", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred for cleaning business property", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.EST.CLE", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "242d623d-e072-4ff3-bfa1-4d825492e40a", "Code": "412", "Name": "Consulting", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments made to consultants", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FEE", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "ce92157d-ab70-4401-acf3-bb9027e103c9", "Code": "416", "Name": "Depreciation Expense", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "The amount of the asset's cost (based on the useful life) that was consumed during the period", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.DEP.TAN", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "cb3460fe-5341-4d50-ae29-17285cdfe185", "Code": "418", "Name": "Charitable and Political Donations", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments made to charities or political organisations or events", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.DON", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "26010ec9-ea19-4cae-addf-3e56a521c309", "Code": "420", "Name": "Entertainment-100% business", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on entertainment by the business that for income tax purposes are fully deductable", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.ENT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "6c15125b-25f2-42b6-a3b1-cce5087bf69b", "Code": "424", "Name": "Entertainment - 0%", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on entertainment by the business that for income tax purposes are not fully deductable", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.ENT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "18d0025f-3187-4e7b-bd11-09c9346cf324", "Code": "425", "Name": "Postage, Freight & Courier", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred by the entity on postage, freight & courier costs", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.PRI", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "4059e3e6-2884-43ce-b103-bb19e687de75", "Code": "429", "Name": "General Expenses", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred that relate to the general running of the business", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "cf3444cb-8642-4c30-b1ea-be55758dc081", "Code": "433", "Name": "Insurance", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred for insuring the business' assets", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.INS", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "0fec4f60-f21f-4022-89a7-f3c1672d445f", "Code": "437", "Name": "Interest Paid", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Interest paid on a business bank account or credit card account", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.INT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "5db559c8-2a8a-4543-a188-fe458f571f6c", "Code": "441", "Name": "Legal Expenses", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on any legal matters", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FEE", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9d4274da-5d95-490c-b6bc-b9e0561d84af", "Code": "445", "Name": "Light, Power, Heating", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred for lighting, power or heating the business premises", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "8722fef2-4631-48b6-ba1b-10d328681eb7", "Code": "449", "Name": "Motor Vehicle Expenses", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on the running of business motor vehicles", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.VEH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "b689b296-6607-47fd-aec3-bde492068466", "Code": "457", "Name": "Operating Lease Payments", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on operating expenses such as office rental and vehicle leases (excludes hire purchase agreements)", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.HIR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "cca14498-cd04-4904-b1ab-2a464983a98d", "Code": "461", "Name": "Printing & Stationery", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on printing and stationery", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.PRI", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e", "Code": "463", "Name": "IT Software and Consumables", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred  on  software or computer consumables", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.SOF", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "06f64d86-832d-4e86-9718-0f777b928f4f", "Code": "464", "Name": "Website", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP", "ReportingCodeName": "Expense", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "85cd41fb-9392-449b-a230-fb013133491c", "Code": "465", "Name": "Rates", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments made to local council for rates", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.EST.RAT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "b66afb7a-2bb2-4ae8-81f6-809ed3c2f5c4", "Code": "466", "Name": "Hosting", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP", "ReportingCodeName": "Expense", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "849fe69b-961e-4372-87ec-214442461d2c", "Code": "469", "Name": "Rent", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments  made to lease a building or area", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.EST.REN", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "242fad6c-00c8-4578-99ab-76fc3142c231", "Code": "473", "Name": "Repairs & Maintenance", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred on a damaged or run down asset that will bring the asset back to its original condition", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.REP", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "324bc493-9109-424c-8f85-ba43c00d702b", "Code": "477", "Name": "Salaries", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payment to employees in exchange for their resources", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.WAG", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "46bda0c4-c804-44ee-ac50-343d171653a4", "Code": "478", "Name": "Directors' Remuneration", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments to company directors in exchange for their resources", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.DIR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "cbe30322-3e85-4be9-8045-47e7308928cb", "Code": "479", "Name": "Employers National Insurance", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payment made for National Insurance contributions - business contribution only", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.ENI", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "53ab320d-2fc7-4cd7-93ab-3a9f59b4f5ef", "Code": "480", "Name": "Staff Training", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred in relation to training staff", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.TRN", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "99f7691c-2c2a-4a54-95f1-85192d704acc", "Code": "482", "Name": "Pensions Costs", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments made to pension schemes", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.PEN", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "a8a1e1a5-6947-4c61-b0e3-79641ef06b49", "Code": "483", "Name": "Medical Insurance", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Payments made to medical insurance schemes", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.STF.BEN", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "84e7b626-191e-4f6f-be6e-100227e3cfe5", "Code": "485", "Name": "Subscriptions", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred by the business in relation to subscriptions, such as magazines and professional bodies", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.SUB", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f5eb3615-07e5-4f6c-82ee-c11afca<PERSON>dae", "Code": "489", "Name": "Telephone & Internet", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred from any business-related phone calls, phone lines, or internet connections", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.TEL", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "5655e317-5c41-4594-9c09-330c7f16d415", "Code": "493", "Name": "Travel - National", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred from any domestic business travel", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.TRA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "00b7acb8-b640-4bc4-930a-e0e977dbcf0a", "Code": "494", "Name": "Travel - International", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Expenses incurred from any international business travel", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.TRA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "776e66a9-71e0-42f2-87b5-3b2d080a5866", "Code": "497", "Name": "Bank Revaluations", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Description": "Bank account revaluations due for foreign exchange rate changes", "Class": "EXPENSE", "SystemAccount": "BANKCURRENCYGAIN", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FOR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "bb82c812-7331-432b-b62e-c7137e570410", "Code": "498", "Name": "Unrealised <PERSON><PERSON><PERSON><PERSON>", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Description": "Unrealised currency gains on outstanding items", "Class": "EXPENSE", "SystemAccount": "UNREALISEDCURRENCYGAIN", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FOR.UGL", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "74c03b03-4011-4792-accc-b7c78c095641", "Code": "499", "Name": "Realised <PERSON><PERSON><PERSON><PERSON>", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Description": "Gains or losses made due to currency exchange rate changes", "Class": "EXPENSE", "SystemAccount": "REALISEDCURRENCYGAIN", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.ADM.FOR.RGL", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f662353b-6072-492b-ac07-e6f5d87e9517", "Code": "500", "Name": "Corporation Tax", "Status": "ACTIVE", "Type": "OVERHEADS", "TaxType": "NONE", "Description": "Tax payable on business profits", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP.TAX.COR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "74d05c06-bea8-4d47-9090-d05a796db63b", "Code": "501", "Name": "Stripe Fees", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Description": "Stripe Fees", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP", "ReportingCodeName": "Expense", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "50dfd140-b6ed-40b3-95ac-fa1113f9844d", "Code": "502", "Name": "Stripe Fees 1", "Status": "ACTIVE", "Type": "EXPENSE", "TaxType": "NONE", "Description": "Stripe Fees 1", "Class": "EXPENSE", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EXP", "ReportingCodeName": "Expense", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "4f014aad-5813-4cb6-9219-f619c006b5d5", "Code": "610", "Name": "Accounts Receivable", "Status": "ACTIVE", "Type": "CURRENT", "TaxType": "NONE", "Description": "Invoices the business has issued but has not yet collected payment on", "Class": "ASSET", "SystemAccount": "DEBTORS", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.CUR.REC.TRA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "97ea7e25-c5c4-4c0d-9e8f-4b55f6b52978", "Code": "611", "Name": "Less Provision for Doubtful Debts", "Status": "ACTIVE", "Type": "CURRENT", "TaxType": "NONE", "Description": "A provision anticipating that a portion of accounts receivable will never be collected", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.CUR.REC.TRA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9c66066d-18af-4ff3-ad94-331d8cceac40", "Code": "620", "Name": "Prepayments", "Status": "ACTIVE", "Type": "CURRENT", "TaxType": "NONE", "Description": "An expenditure that has been paid for in advance", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.CUR.REC.PRE", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "89fd3438-8adb-46e0-b6e8-8ca5428fe1d2", "Code": "630", "Name": "Inventory", "Status": "ACTIVE", "Type": "INVENTORY", "TaxType": "NONE", "Description": "Value of tracked items for resale.", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.CUR.INY", "ReportingCodeName": "Inventory", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "e61788ab-d857-4325-8902-ead382fab4c6", "Code": "710", "Name": "Office Equipment", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Office equipment that is owned and controlled by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.OFF", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "6ff256e7-d93c-4e8a-b644-0a2d7261cc66", "Code": "711", "Name": "Less Accumulated Depreciation on Office Equipment", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of office equipment costs that has been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.OFF.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "fcb33a91-6881-474e-bc28-f29e5138d442", "Code": "720", "Name": "Computer Equipment", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Computer equipment that is owned and controlled by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.OFF", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "fee56f90-69ec-47c8-a736-afc486f8a6a7", "Code": "721", "Name": "Less Accumulated Depreciation on Computer Equipment", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of computer equipment costs that has been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.OFF.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "********-df77-418b-b4d2-d2e43d2ff4d1", "Code": "740", "Name": "Buildings", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Buildings that are owned and controlled by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.LAB", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "303dbb09-b55a-4b54-83a7-204ca198cbb1", "Code": "741", "Name": "Less Accumulated Depreciation on Buildings", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of buildings costs that have been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.LAB.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f3c27e88-afeb-49f1-9e61-1fd5d4adffd3", "Code": "750", "Name": "Leasehold Improvements", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The value added to the leased premises via improvements", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.LAB.LEA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "d0372d26-a5f5-4950-a0b3-000c7ab04a9f", "Code": "751", "Name": "Less Accumulated Depreciation on Leasehold Improvements", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of leasehold improvement costs that has been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.LAB.ACC.LEA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "73faca86-ea86-43e3-ada3-6fa0cbc909e7", "Code": "760", "Name": "Motor Vehicles", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Motor vehicles that are owned and controlled by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.VEH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9c166365-6a14-4103-a6eb-252385f942d9", "Code": "761", "Name": "Less Accumulated Depreciation on Motor Vehicles", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of motor vehicle costs that has been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.VEH.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "beb56d90-0fe3-46f4-9624-ba899d9c0781", "Code": "764", "Name": "Plant and Machinery", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Plant and machinery that are owned and controlled by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.PLA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "3d29e271-ddbd-4d16-9ae4-5a0b8b8991dd", "Code": "765", "Name": "Less Accumulated Depreciation on Plant and Machinery", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of plant and machinery cost that has been consumed by the business (based on the useful life)", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.FIX.PLA.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "8736c50a-be86-453d-8bbf-8b042da9a2f6", "Code": "770", "Name": "Intangibles", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "Assets with no physical presence e.g. goodwill or patents", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.INT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "62ec6fa1-e005-4ff5-bbd6-ee24f652db88", "Code": "771", "Name": "Less Accumulated Amortisation on Intangibles", "Status": "ACTIVE", "Type": "FIXED", "TaxType": "NONE", "Description": "The total amount of intangibles that have been consumed by the business", "Class": "ASSET", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "ASS.NCA.INT.AMO", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "6599447d-e409-4afe-b14a-3e2893741f27", "Code": "800", "Name": "Accounts Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Invoices the company has received from suppliers but have not made payment on", "Class": "LIABILITY", "SystemAccount": "CREDITORS", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TRA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "********-29e2-4228-8551-7e63c271ab9b", "Code": "801", "Name": "Unpaid Expense <PERSON>", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Expense claims typically made by employees/shareholder employees which the business has not made payment on", "Class": "LIABILITY", "SystemAccount": "UNPAIDEXPCLM", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.OTH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "a31ebcd9-c156-445b-94e8-99698f631b62", "Code": "803", "Name": "Wages Payable", "Status": "ARCHIVED", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Xero automatically updates this account for payroll entries created using Payroll and will store the payroll amount to be paid to the employee for the pay run. This account enables you to maintain separate accounts for employee Wages Payable amounts and Accounts Payable amounts", "Class": "LIABILITY", "SystemAccount": "WAGEPAYABLES", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.OTH", "ReportingCodeName": "Employee entitlements (wages, annual leave, etc)", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f197d9f4-9ace-4555-b49c-8faf0020ca22", "Code": "805", "Name": "Accruals", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Any services the business has received but have not yet been invoiced for e.g. Accountancy Fees", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "f33cfedf-e9b1-45e5-9c64-56531288da01", "Code": "810", "Name": "Income in Advance", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Any income the business has received but have not provided the goods or services for", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.ACC", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "ea22c7c9-2941-45ed-ae19-762b4a3589b6", "Code": "811", "Name": "Credit Card Control Account", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "The amount owing on the company's credit cards", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "63d76651-6718-4085-afc6-dbb775554e0b", "Code": "814", "Name": "Wages Payable - Payroll", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Where this account is set as the nominated Wages Payable account within Payroll Settings, Xero allocates the net wage amount of each pay run created using Payroll to this account", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.OTH", "ReportingCodeName": "Liabilities", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "d6eaf1a4-06b0-44c6-8929-dd243a9cc354", "Code": "815", "Name": "Employee contribution to benefits", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Payroll deductions for employee contributions towards payrolled benefits in kind", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.OTH", "ReportingCodeName": "Liabilities", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c5a5ad9e-a155-4f74-9034-7f664aa4f14f", "Code": "820", "Name": "VAT", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "The balance in this account represents VAT owing to or from the HMRC. At the end of the VAT period, it is this account that should be used to code against either the ‘refunds from’ or ‘payments to’ the HMRC that will appear on the bank statement. Xero has been designed to use only one VAT account to track VAT on income and expenses, so there is no need to add any new VAT accounts to Xero", "Class": "LIABILITY", "SystemAccount": "GST", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.VAT", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "62f0baa3-c5a3-4fa7-8d3c-1f739f075047", "Code": "825", "Name": "PAYE Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "The Amount of PAYE tax due to be paid to the HMRC", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.OTH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "92987c28-b17f-4aa0-9693-8a8224091c1a", "Code": "826", "Name": "NIC Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "The amount of a business' portion of National Insurance Contribution that is due to be paid to the HMRC", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.OTH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "4a7ac3ce-37be-45cc-84b5-e09414d09d47", "Code": "830", "Name": "Provision for Corporation Tax", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Corporation tax payable to the HMRC", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.COR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "86766e26-36fc-4264-b352-2949cfa25a30", "Code": "835", "Name": "Directors' Loan Account", "Status": "ACTIVE", "Type": "LIABILITY", "TaxType": "NONE", "Description": "Monies owed to or from company directors", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.NCL.DIR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9ca3cb26-cfb1-49e1-ad47-c4acc9aa8b09", "Code": "840", "Name": "Historical Adjustment", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "For any accounting and starting balance adjustments", "Class": "LIABILITY", "SystemAccount": "HISTORICAL", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "468e9e5c-02fc-473c-be9a-8e427149e03d", "Code": "850", "Name": "Suspense", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "A clearing account", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "ef3fe175-b5ce-480d-b575-7ea15cafc183", "Code": "858", "Name": "Pensions Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Payroll pension payable account", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.OTH", "ReportingCodeName": "Liabilities", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "64b3c7a9-6c16-4d82-ad0f-************", "Code": "860", "Name": "Rounding", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "An adjustment entry to allow for rounding", "Class": "LIABILITY", "SystemAccount": "ROUNDING", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "6ba10af1-268b-47b1-8aef-96960bcc5b6a", "Code": "868", "Name": "Earnings Orders Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Payroll earnings order account", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.OTH", "ReportingCodeName": "Liabilities", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "10404b25-9598-43df-86c8-93583b8a3a7d", "Code": "877", "Name": "Tracking Transfers", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Transfers between tracking categories", "Class": "LIABILITY", "SystemAccount": "TRACKINGTRANSFERS", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c7d6393e-b75b-4b0e-8fe4-a3190fb5a02d", "Code": "900", "Name": "Loan", "Status": "ACTIVE", "Type": "LIABILITY", "TaxType": "NONE", "Description": "Any money that has been borrowed from a creditor", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.NCL.OTH", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "3ad51c1b-92f6-469f-8f2d-429e8560a637", "Code": "910", "Name": "Hire Purchase Loan", "Status": "ACTIVE", "Type": "TERMLIAB", "TaxType": "NONE", "Description": "Any goods bought through hire purchase agreements", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.NCL.LOA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c4185751-b20f-4bab-8715-71042e3c601c", "Code": "920", "Name": "Deferred Tax", "Status": "ACTIVE", "Type": "TERMLIAB", "TaxType": "NONE", "Description": "Used if there is a timing difference between taxable profits and accounting profits", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.PRO.DEF", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "328075f3-9ba2-4ba0-9166-2cd2772f119f", "Code": "947", "Name": "Student Loan Deductions Payable", "Status": "ACTIVE", "Type": "CURRLIAB", "TaxType": "NONE", "Description": "Payroll student loan deductions payable account", "Class": "LIABILITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "LIA.CUR.TAX.OTH", "ReportingCodeName": "Liabilities", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9b4efce7-575b-4db5-9e21-585cbe18196d", "Code": "950", "Name": "Capital - x,xxx Ordinary Shares", "Status": "ACTIVE", "Type": "EQUITY", "TaxType": "NONE", "Description": "Paid up capital", "Class": "EQUITY", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EQU.SHA", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "9c3469fb-626a-491c-88a7-f245b8849ad5", "Code": "960", "Name": "Retained Earnings", "Status": "ACTIVE", "Type": "EQUITY", "TaxType": "NONE", "Description": "Do not Use", "Class": "EQUITY", "SystemAccount": "RETAINEDEARNINGS", "EnablePaymentsToAccount": false, "ShowInExpenseClaims": false, "ReportingCode": "EQU.RET", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "5a91d392-718b-44c4-bd09-23a420e5b964", "Code": "970", "Name": "Owner A Funds Introduced", "Status": "ACTIVE", "Type": "EQUITY", "TaxType": "NONE", "Description": "Funds contributed by the owner", "Class": "EQUITY", "EnablePaymentsToAccount": true, "ShowInExpenseClaims": false, "ReportingCode": "EQU.OWN.1", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}, {"AccountID": "c678d7c9-1f02-43e0-8a3e-b103d412aa13", "Code": "980", "Name": "Owner A Drawings", "Status": "ACTIVE", "Type": "EQUITY", "TaxType": "NONE", "Description": "Withdrawals by the owners", "Class": "EQUITY", "EnablePaymentsToAccount": true, "ShowInExpenseClaims": false, "ReportingCode": "EQU.DRA.1", "HasAttachments": false, "UpdatedDateUTC": "/Date(*************+0000)/", "AddToWatchlist": false}], "summary": {"count": 107, "first_record_date": null, "last_record_date": null}}
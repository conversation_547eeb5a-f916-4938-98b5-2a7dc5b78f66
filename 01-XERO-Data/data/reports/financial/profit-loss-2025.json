{"metadata": {"dataType": "Profit and Loss Report", "extractedDate": "2025-07-29T06:38:10.316345", "organization": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "recordCount": 1, "version": "2.0.0", "year": 2025, "dateRange": {"startDate": "2025-01-01", "endDate": "2025-12-31"}, "currency": "GBP"}, "report": {"ReportID": "ProfitAndLoss", "ReportName": "Profit and Loss", "ReportType": "ProfitAndLoss", "ReportTitles": ["Profit & Loss", "MCX3D LTD", "1 January 2025 to 31 December 2025"], "ReportDate": "29 July 2025", "UpdatedDateUTC": "/Date(*************)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": ""}, {"Value": "31 Dec 25"}]}, {"RowType": "Section", "Title": "Income", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Sales", "Attributes": [{"Value": "a16ff26c-4fa3-4cf8-9865-8c29ec91e6f6", "Id": "account"}]}, {"Value": "19644.24", "Attributes": [{"Value": "a16ff26c-4fa3-4cf8-9865-8c29ec91e6f6", "Id": "account"}]}]}, {"RowType": "SummaryRow", "Cells": [{"Value": "Total Income"}, {"Value": "19644.24"}]}]}, {"RowType": "Section", "Title": "", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Gross Profit"}, {"Value": "19644.24"}]}]}, {"RowType": "Section", "Title": "Less Operating Expenses", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Audit & Accountancy fees", "Attributes": [{"Value": "914713ce-932d-44b4-a2a9-dc34f0f71c29", "Id": "account"}]}, {"Value": "91.43", "Attributes": [{"Value": "914713ce-932d-44b4-a2a9-dc34f0f71c29", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Bank Fees", "Attributes": [{"Value": "0ee22836-f53f-4101-9503-0316a899c483", "Id": "account"}]}, {"Value": "262.90", "Attributes": [{"Value": "0ee22836-f53f-4101-9503-0316a899c483", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Entertainment-100% business", "Attributes": [{"Value": "26010ec9-ea19-4cae-addf-3e56a521c309", "Id": "account"}]}, {"Value": "67.86", "Attributes": [{"Value": "26010ec9-ea19-4cae-addf-3e56a521c309", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Foreign Currency Gains and Losses", "Attributes": [{"Value": "FXGROUPID", "Id": "account"}, {"Value": "FXGROUPID", "Id": "groupID"}]}, {"Value": "662.27", "Attributes": [{"Value": "FXGROUPID", "Id": "account"}, {"Value": "FXGROUPID", "Id": "groupID"}]}]}, {"RowType": "Row", "Cells": [{"Value": "General Expenses", "Attributes": [{"Value": "4059e3e6-2884-43ce-b103-bb19e687de75", "Id": "account"}]}, {"Value": "699.31", "Attributes": [{"Value": "4059e3e6-2884-43ce-b103-bb19e687de75", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "IT Software and Consumables", "Attributes": [{"Value": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e", "Id": "account"}]}, {"Value": "217.87", "Attributes": [{"Value": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Salaries", "Attributes": [{"Value": "324bc493-9109-424c-8f85-ba43c00d702b", "Id": "account"}]}, {"Value": "17634.57", "Attributes": [{"Value": "324bc493-9109-424c-8f85-ba43c00d702b", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Stripe Fees", "Attributes": [{"Value": "74d05c06-bea8-4d47-9090-d05a796db63b", "Id": "account"}]}, {"Value": "10.10", "Attributes": [{"Value": "74d05c06-bea8-4d47-9090-d05a796db63b", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Stripe Fees 1", "Attributes": [{"Value": "50dfd140-b6ed-40b3-95ac-fa1113f9844d", "Id": "account"}]}, {"Value": "47.70", "Attributes": [{"Value": "50dfd140-b6ed-40b3-95ac-fa1113f9844d", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Subscriptions", "Attributes": [{"Value": "84e7b626-191e-4f6f-be6e-100227e3cfe5", "Id": "account"}]}, {"Value": "941.69", "Attributes": [{"Value": "84e7b626-191e-4f6f-be6e-100227e3cfe5", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Travel - International", "Attributes": [{"Value": "00b7acb8-b640-4bc4-930a-e0e977dbcf0a", "Id": "account"}]}, {"Value": "19.47", "Attributes": [{"Value": "00b7acb8-b640-4bc4-930a-e0e977dbcf0a", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Travel - National", "Attributes": [{"Value": "5655e317-5c41-4594-9c09-330c7f16d415", "Id": "account"}]}, {"Value": "7.94", "Attributes": [{"Value": "5655e317-5c41-4594-9c09-330c7f16d415", "Id": "account"}]}]}, {"RowType": "Row", "Cells": [{"Value": "Website", "Attributes": [{"Value": "06f64d86-832d-4e86-9718-0f777b928f4f", "Id": "account"}]}, {"Value": "231.49", "Attributes": [{"Value": "06f64d86-832d-4e86-9718-0f777b928f4f", "Id": "account"}]}]}, {"RowType": "SummaryRow", "Cells": [{"Value": "Total Operating Expenses"}, {"Value": "20894.60"}]}]}, {"RowType": "Section", "Title": "", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Net Profit"}, {"Value": "-1250.36"}]}]}]}}
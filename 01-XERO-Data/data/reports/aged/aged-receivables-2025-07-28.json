{"metadata": {"reportType": "Aged Receivables by Contact", "reportDate": "2025-07-28", "extractedDate": "2025-07-28T23:17:33.505007", "currency": "GBP", "organisation": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "totalContacts": 377, "successfulExtracts": 20}, "receivables": [{"contactId": "2692d6a4-da65-4e65-b9b5-1fbbdb344251", "contactName": "<PERSON><PERSON><PERSON>", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "<PERSON><PERSON><PERSON>", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733831271)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "51e5453e-3f23-45ff-8b16-6b9b48068040", "contactName": "Bilal itani", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Bilal itani", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733831896)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "9b18c5d4-8f24-4683-b8b4-4a50f08ca9e1", "contactName": "Google", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Google", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733832457)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "bcc5f409-f280-472f-9ac0-91d72704dae3", "contactName": "Photoshop", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Photoshop", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733834442)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "222c17d4-ad8f-4e04-979c-205fd68ff8a4", "contactName": "Grammarly", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Grammarly", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733835573)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "a3f19542-8f68-4ecd-bb98-c961edd0927d", "contactName": "The Hoxton Mix", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "The Hoxton Mix", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733836231)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "8c5b91f3-c164-40d2-b238-262f3d08c79b", "contactName": "Webflow", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Webflow", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733839833)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "6876c84b-acdf-4a9c-8c8f-1e8ba4a18a0b", "contactName": "Siteground", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Siteground", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733841132)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "c6685a74-4cfe-4305-8d4a-aa991eb06475", "contactName": "Tailor Brands", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Tailor Brands", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733841705)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "1a31a469-7cf2-48a8-8914-8254494cb4cd", "contactName": "Vimeo", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Vimeo", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733845510)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "f81bb253-9522-45d9-a21e-48d5796fc7c2", "contactName": "Befunky", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Befunky", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733846395)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "36b01822-90af-47ce-bd06-fe0c2247639f", "contactName": "<PERSON><PERSON><PERSON>", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "<PERSON><PERSON><PERSON>", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733847644)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "c0609209-4bb7-4aa6-be20-681d6a226687", "contactName": "Facebook", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Facebook", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733848516)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "8ef61a23-a740-47fe-ae74-205ba9944fef", "contactName": "Microsoft", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Microsoft", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733849482)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "0a993520-d124-4ee7-bb8c-f3f1ad8df5ed", "contactName": "SBR", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "SBR", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733850066)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "75afbf7f-08da-4fdc-82ad-0779816d97d0", "contactName": "Zoom", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Zoom", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733850707)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "11762fee-6eea-403f-994d-328c142c76c7", "contactName": "Wix", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Wix", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733851350)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "bec95d29-7d2a-450c-8964-f6773eb19924", "contactName": "Facbook", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Facbook", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733852003)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "3ecaaf9a-cd0a-4f80-911c-529d2a890710", "contactName": "Prysm", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Prysm", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733853009)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}, {"contactId": "46472a72-70e8-4c3a-a581-edf5e4e63d71", "contactName": "Designer", "report": {"ReportID": "AgedReceivablesByContact", "ReportName": "Aged Receivables By Contact", "ReportType": "AgedReceivablesByContact", "ReportTitles": ["Invoices", "Designer", "To 31 July 2025", "Showing payments to 28 July 2025"], "ReportDate": "28 July 2025", "UpdatedDateUTC": "/Date(1753733853610)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Date"}, {"Value": "Number"}, {"Value": "Due Date"}, {"Value": ""}, {"Value": "Total"}, {"Value": ""}, {"Value": "Paid"}, {"Value": ""}, {"Value": "Credited"}, {"Value": ""}, {"Value": "Due"}, {"Value": ""}]}]}}]}
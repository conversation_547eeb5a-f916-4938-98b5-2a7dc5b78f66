{"metadata": {"reportType": "Bank Summary", "extractedDate": "2025-07-29T06:38:18.884497", "organisation": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea"}, "report": {"ReportID": "BankSummary", "ReportName": "Bank Summary", "ReportType": "BankSummary", "ReportTitles": ["Bank Summary", "MCX3D LTD", "From 1 July 2025 to 31 July 2025"], "ReportDate": "29 July 2025", "UpdatedDateUTC": "/Date(*************)/", "Fields": [], "Rows": [{"RowType": "Header", "Cells": [{"Value": "Bank Accounts"}, {"Value": "Opening Balance"}, {"Value": "Cash Received"}, {"Value": "Cash Spent"}, {"Value": "FX Gain"}, {"Value": "Closing Balance"}]}, {"RowType": "Section", "Title": "", "Rows": [{"RowType": "Row", "Cells": [{"Value": "Cash", "Attributes": [{"Value": "c70c977e-653c-444b-8e8c-26a617164164", "Id": "accountID"}]}, {"Value": "7128.96"}, {"Value": "0.00", "Attributes": [{"Value": "c70c977e-653c-444b-8e8c-26a617164164", "Id": "account"}]}, {"Value": "0.00", "Attributes": [{"Value": "c70c977e-653c-444b-8e8c-26a617164164", "Id": "account"}]}, {"Value": "199.83"}, {"Value": "7328.79"}]}, {"RowType": "Row", "Cells": [{"Value": "Wise EUR", "Attributes": [{"Value": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Id": "accountID"}]}, {"Value": "-25.74"}, {"Value": "0.00", "Attributes": [{"Value": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Id": "account"}]}, {"Value": "0.00", "Attributes": [{"Value": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Id": "account"}]}, {"Value": "-0.31"}, {"Value": "-26.05"}]}, {"RowType": "Row", "Cells": [{"Value": "WISE GBP", "Attributes": [{"Value": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Id": "accountID"}]}, {"Value": "13784.30"}, {"Value": "4.46", "Attributes": [{"Value": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Id": "account"}]}, {"Value": "4.46", "Attributes": [{"Value": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Id": "account"}]}, {"Value": "0.00"}, {"Value": "13784.30"}]}, {"RowType": "Row", "Cells": [{"Value": "WISE USD", "Attributes": [{"Value": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Id": "accountID"}]}, {"Value": "226.57"}, {"Value": "4222.89", "Attributes": [{"Value": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Id": "account"}]}, {"Value": "3782.71", "Attributes": [{"Value": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7", "Id": "account"}]}, {"Value": "4.85"}, {"Value": "671.60"}]}, {"RowType": "SummaryRow", "Cells": [{"Value": "Total"}, {"Value": "21114.09"}, {"Value": "4227.35"}, {"Value": "3787.17"}, {"Value": "204.37"}, {"Value": "21758.64"}]}]}]}}
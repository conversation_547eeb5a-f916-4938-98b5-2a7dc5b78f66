{"metadata": {"dataType": "Payments", "extractedDate": "2025-07-29T06:38:12.795007", "organization": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "recordCount": 103, "version": "2.0.0"}, "payments": [{"PaymentID": "0bfe9e16-9936-482b-a7a6-d491a6af4b4e", "Date": "/Date(*************+0000)/", "BankAmount": 800.0, "Amount": 800.0, "CurrencyRate": 1.20745, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "664a2475-c35f-4aca-a6b3-55bb7e927134", "InvoiceNumber": "INV-0014", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4fcb7eef-0d7d-4b4d-981b-d564a59cd0be", "Name": "High Tech XL Group B.V", "HasValidationErrors": false}, "CurrencyCode": "EUR"}, "HasValidationErrors": false}, {"PaymentID": "c4ffa8e7-1339-4a0b-b8f5-2cad1dfb9ef1", "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "DELETED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f0c6677e-7100-44fa-8a83-31bce6486f6b", "InvoiceNumber": "INV-0013", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-09d7-410c-9b05-09f6322ed265", "Date": "/Date(*************+0000)/", "BankAmount": 347.06, "Amount": 400.0, "Reference": "ch_3Ltv62S5gCcGK53K0X2Nb2bt", "CurrencyRate": 1.152538, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a381477d-6ccb-4046-8db1-25c82b169ada", "InvoiceNumber": "INV-0029", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "d2e3bdbe-cd8e-4ebd-bfb7-b128d313c727", "Name": "INCORP", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c14db7ba-c2e9-4dba-837d-8fb46bf25e21", "Date": "/Date(*************+0000)/", "BankAmount": 408.57, "Amount": 479.0, "Reference": "ch_3LzaMWS5gCcGK53K1HxwsTZn", "CurrencyRate": 1.172382, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "d967c303-b2cb-43ea-a4c2-eaff49c4223f", "InvoiceNumber": "INV-0036", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "5a331741-5d73-4c8e-9e19-971718fcd829", "Name": "ESC S.A.L", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "f2127656-625d-4e69-a1b9-d05018811f02", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "38571c15-20da-4091-96b6-582ce028c04c", "InvoiceNumber": "INV-000104", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-d0af-4e13-8952-c278e4e06d72", "Date": "/Date(*************+0000)/", "BankAmount": 596.46, "Amount": 799.26, "Reference": "#*********", "CurrencyRate": 1.34, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "95068ba5-92dc-49e5-bfbc-8e0ae7a6ea94", "InvoiceNumber": "INV-0004", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "487c27d8-2b80-4748-9a9f-0f4511c30479", "Date": "/Date(*************+0000)/", "BankAmount": 2880.0, "Amount": 2880.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "27221c70-8ba6-4d20-840c-dd41a3145101", "InvoiceNumber": "INV-0002", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "f85b73db-6eaa-48c2-9f92-28e13470fa84", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "fe0b8ce5-5a08-48be-8bae-ce110761b6b3", "InvoiceNumber": "INV-0005", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "0bcf9d8a-08c0-4e30-9393-a1fc5e846e3c", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "********-0697-44f0-8f04-44ef673e8f79", "InvoiceNumber": "INV-0008", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "a5639110-c986-4a69-a1a0-722b7d814498", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "1a98f499-e1df-4b07-bf93-b4c6a4cedb97", "InvoiceNumber": "INV-0006", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "5f05a8d9-567b-4603-84eb-66682d2ba574", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "fca65df2-2923-49cc-9e0f-b276ab430d98", "InvoiceNumber": "INV-0007", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "363d287d-cb1c-475c-a34d-883483af5134", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "2771ed5c-4b84-430a-9fee-d5034caf257e", "InvoiceNumber": "INV-0009", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "9eb01a1b-507e-4136-88b4-d6a38c5f9678", "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "66bc063d-0587-40b7-b53b-0bc66c86edab", "InvoiceNumber": "INV-0011", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-5bc5-44fb-9112-f2f0fc825e6a", "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "DateString": "2022-06-03T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f0c6677e-7100-44fa-8a83-31bce6486f6b", "InvoiceNumber": "INV-0013", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "da057d0a-8e26-4c57-8cb2-39cbf0d367d4", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b36d33ad-5f02-4c00-87f7-37a75a8f1e4b", "InvoiceNumber": "INV-0016", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "7f17692e-5d18-4d84-bd88-7c8a50b73b31", "Date": "/Date(*************+0000)/", "BankAmount": 1020.0, "Amount": 1020.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "4a0fd2f2-cd5d-440d-9b47-ae5039d87bf0", "InvoiceNumber": "INV-0010", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "8c6ad0d5-29d5-4dd8-8139-b34e41cf65d4", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "cc4af716-40f8-411c-b83b-d73689d9a4e7", "InvoiceNumber": "INV-0012", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "3c89168e-5c2e-47d4-8bf5-0a351b6c5b5d", "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "DateString": "2022-06-03T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 135.0, "Amount": 135.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ac441337-ea9f-4fe1-9c94-6fb2e2f1ab79", "InvoiceNumber": "INV-0017", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "36354d50-1b5d-4a84-b90d-b363410a1f8d", "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "DateString": "2022-06-28T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 105.0, "Amount": 105.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ac441337-ea9f-4fe1-9c94-6fb2e2f1ab79", "InvoiceNumber": "INV-0017", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "03a93acb-795f-4c39-99dd-9a19585a7718", "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "DateString": "2022-06-28T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "51d21772-0bc2-488a-93cf-7731a2b8d456", "InvoiceNumber": "INV-0021", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "b0eb35a7-e866-40eb-99e2-4f7505d28023", "Date": "/Date(*************+0000)/", "BankAmount": 703.0, "Amount": 703.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "13cd2b90-7827-485a-8dd9-d24e33cef822", "InvoiceNumber": "INV-0025", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "b7a4f71a-310b-411a-9d14-685d6a863c52", "BatchPaymentID": "85e645fd-e597-4916-ae02-985ecfd1cfaf", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "85e645fd-e597-4916-ae02-985ecfd1cfaf", "DateString": "2022-09-09T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 703.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 17.0, "Amount": 17.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "13cd2b90-7827-485a-8dd9-d24e33cef822", "InvoiceNumber": "INV-0025", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "f302380e-2a2c-48dc-92db-d4404c3f407d", "Date": "/Date(*************+0000)/", "BankAmount": 703.0, "Amount": 703.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "9d1f8929-dabf-45c7-b955-10f53dc02c6b", "InvoiceNumber": "INV-0031", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "1f383a5b-93a5-44c3-9f38-2dea65373af5", "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "DateString": "2022-09-09T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 400.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 17.0, "Amount": 17.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "9d1f8929-dabf-45c7-b955-10f53dc02c6b", "InvoiceNumber": "INV-0031", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "08f02270-79bb-4ade-ad46-667993dd8542", "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "DateString": "2022-06-28T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 30.0, "Amount": 30.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f08c7b4c-2c6d-40c9-877e-660f5101938c", "InvoiceNumber": "INV-0028", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "c88e847d-0703-46ea-8a1d-a1d1c7186eaa", "Date": "/Date(*************+0000)/", "BankAmount": 200.0, "Amount": 200.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f08c7b4c-2c6d-40c9-877e-660f5101938c", "InvoiceNumber": "INV-0028", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "348349ae-9bf2-4efb-bbe9-56c11a24a858", "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "DateString": "2022-09-09T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 400.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 143.0, "Amount": 143.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f08c7b4c-2c6d-40c9-877e-660f5101938c", "InvoiceNumber": "INV-0028", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "98a986ee-0a1a-41eb-81b6-8d2d6d79cb91", "BatchPaymentID": "85e645fd-e597-4916-ae02-985ecfd1cfaf", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "85e645fd-e597-4916-ae02-985ecfd1cfaf", "DateString": "2022-09-09T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 703.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 686.0, "Amount": 686.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b1ac1dbd-6c3f-426e-b29d-5025367f022d", "InvoiceNumber": "INV-0030", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "15e6fc3c-3593-4ade-8148-561886b9a8ed", "BatchPaymentID": "89067d01-7a36-4076-a98b-999e149861b1", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "89067d01-7a36-4076-a98b-999e149861b1", "DateString": "2022-10-06T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 754.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 34.0, "Amount": 34.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b1ac1dbd-6c3f-426e-b29d-5025367f022d", "InvoiceNumber": "INV-0030", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "3d6f5eb5-26d6-47af-a12b-d0ac94235c80", "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "457b93b6-ee3a-4e5d-9b2a-52f94ceae13a", "DateString": "2022-09-09T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 400.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "33d8776c-33da-47eb-b289-82cb02e2b8e1", "InvoiceNumber": "0031", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "9e6249ab-4197-41a6-b995-2d00b45f363d", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "0df9f732-59c1-4e58-b7d5-d6afa9fdead4", "InvoiceNumber": "INV-0027", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "837935d6-0f34-4f8b-961f-34e5b7107f2e", "BatchPaymentID": "89067d01-7a36-4076-a98b-999e149861b1", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "89067d01-7a36-4076-a98b-999e149861b1", "DateString": "2022-10-06T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 754.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "2c21d005-d750-47ee-869a-096e0e8a423a", "InvoiceNumber": "INV-0032", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "3f31f3ca-9f82-47ad-8b24-fd8485980818", "Date": "/Date(*************+0000)/", "BankAmount": 249.79, "Amount": 300.0, "Reference": "ch_3M42OMS5gCcGK53K1qPHpu2q", "CurrencyRate": 1.201009, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "98d54096-ae1f-42d4-98f0-1441c90504c0", "InvoiceNumber": "INV-0037", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c486595a-680f-4cf1-bdd6-d01ef8516ac5", "Date": "/Date(*************+0000)/", "BankAmount": 622.69, "Amount": 720.0, "Reference": "ch_3LpXHQS5gCcGK53K1N3TlRbm", "CurrencyRate": 1.156274, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "72dfd5d5-5927-4bc8-94ff-39da43dc53d5", "InvoiceNumber": "INV-0033", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "615711d1-fa13-429b-9e56-82c99b694c84", "BatchPaymentID": "ce18b1c5-cd2c-48c9-874c-7b8f4db01f2c", "BatchPayment": {"Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "BatchPaymentID": "ce18b1c5-cd2c-48c9-874c-7b8f4db01f2c", "DateString": "2022-12-06T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 700.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 350.0, "Amount": 300.0, "CurrencyRate": 1.16667, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "6d8f0d5a-d197-4505-912b-43ba1247914a", "InvoiceNumber": "INV-0035", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "568f5b90-1905-4e2f-8484-ac87529c9f03", "Name": "Lines and Arts s.a.r.l", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "5acb984f-dbf8-4c44-be84-785025426c9d", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "58c9d423-3f84-4960-bca8-7c5e62a5a697", "InvoiceNumber": "INV-0038", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "3d31fa3f-81bf-49db-9d7e-9792a8191e4c", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "3c413666-1cd0-49d9-a640-81e987784d90", "InvoiceNumber": "INV-0039", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "cbdfa68e-d303-4ace-95b1-cba45edc4a70", "BatchPaymentID": "ce18b1c5-cd2c-48c9-874c-7b8f4db01f2c", "BatchPayment": {"Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "BatchPaymentID": "ce18b1c5-cd2c-48c9-874c-7b8f4db01f2c", "DateString": "2022-12-06T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 700.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 350.0, "Amount": 300.0, "CurrencyRate": 1.16667, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "db1ff7b2-4fbc-411f-9147-6179eb1de29a", "InvoiceNumber": "INV-0041", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "568f5b90-1905-4e2f-8484-ac87529c9f03", "Name": "Lines and Arts s.a.r.l", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "c8c7b95a-83c6-436e-aab5-8d3da1b7216a", "Date": "/Date(*************+0000)/", "BankAmount": 236.97, "Amount": 300.0, "Reference": "ch_3MTI4MS5gCcGK53K0PeYFtJ6", "CurrencyRate": 1.265983, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "01e989f4-8e44-44c2-ae0b-8dee3bace209", "InvoiceNumber": "INV-0043", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "fa7e482d-3146-4625-9388-c16b07b5e65a", "Date": "/Date(*************+0000)/", "BankAmount": 600.0, "Amount": 600.0, "Reference": "ch_3MdXhHS5gCcGK53K1wCSMz2P", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "92a53f39-4e70-4960-901e-a22eb242dc62", "InvoiceNumber": "INV-0045", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "96ae6042-7d86-4405-9947-df32a932928f", "Name": "Lumi SAL", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "78609c89-c530-4f2b-ba4f-e80b23957fda", "Date": "/Date(*************+0000)/", "BankAmount": 239.82, "Amount": 300.0, "Reference": "ch_3MDKk2S5gCcGK53K18gmUMR7", "CurrencyRate": 1.250938, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "********-b2c6-4b02-9b1c-be4849d634af", "InvoiceNumber": "INV-0040", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "328da062-5252-4126-b329-ad3a464dae15", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "5391bbae-a369-42b8-9db8-f97b6e5a415e", "InvoiceNumber": "INV-0044", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "7bcd066f-4f0c-4fae-a638-210b902645c8", "Date": "/Date(*************+0000)/", "BankAmount": 248.4, "Amount": 300.0, "Reference": "ch_3MjN7rS5gCcGK53K1ovfVi3x", "CurrencyRate": 1.207729, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "1e25c740-9573-45cf-9354-21c7eacb9d72", "InvoiceNumber": "INV-0052", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "b93e5d1c-369f-4d9d-a492-034bdd6cc929", "Date": "/Date(*************+0000)/", "BankAmount": 238.53, "Amount": 300.0, "Reference": "ch_3MWhKuS5gCcGK53K063YrL4k", "CurrencyRate": 1.257703, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "3a56bba8-2b32-4347-8a98-76e5a2018f9f", "InvoiceNumber": "INV-0048", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c95d0ebc-0712-4e37-90de-97f1fc06fff3", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "d1b2fdd8-10a0-4296-8ced-42902660af55", "InvoiceNumber": "INV-0049", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "70bf7e70-fc0a-4374-a95d-61a3da2704fa", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "8f5ad4fe-76c8-402d-91f1-c37fea20dd17", "InvoiceNumber": "INV-0053", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "7072061a-5b7e-4abc-b2d1-e7513b64ad35", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "bf047d9f-8115-4255-bdc5-e370268677b5", "InvoiceNumber": "INV-0058", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "56e235d5-06f4-4fc6-bec7-2fee4e288a52", "Date": "/Date(*************+0000)/", "BankAmount": 236.66, "Amount": 300.0, "Reference": "ch_3MxT9eS5gCcGK53K0QstwNLS", "CurrencyRate": 1.267641, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "e3392dd0-63ce-4369-a6ea-623994fcf1fe", "InvoiceNumber": "INV-0057", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "886b05d2-7fc5-4895-bcfa-fbd997f4b368", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "46a851d4-8d54-4e39-a205-42295a6a5e07", "InvoiceNumber": "INV-0061", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "35522bc0-ff3d-4386-8286-6873b9cbb036", "Date": "/Date(*************+0000)/", "BankAmount": 235.9, "Amount": 300.0, "Reference": "ch_3NAbPMS5gCcGK53K0f1thocG", "CurrencyRate": 1.271725, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "843cac6a-886f-4188-a076-961bb3cb2afa", "InvoiceNumber": "INV-0060", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "386303b3-4753-4e9c-aa1f-3a4a439bf29d", "Date": "/Date(*************+0000)/", "BankAmount": 8340.88, "Amount": 7200.0, "CurrencyRate": 1.15846, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "4b539c58-fd2d-40d7-8602-693a01ecf3ea", "InvoiceNumber": "INV-0063", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "5a331741-5d73-4c8e-9e19-971718fcd829", "Name": "ESC S.A.L", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "6850d2d0-5665-4f81-b2ad-96bcac572bec", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "c22d5fc4-ed3b-40ca-81f2-18c6a3900074", "InvoiceNumber": "INV-0065", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "ef9d9aeb-6cae-4547-a22a-653470c8e3b1", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "3fc62472-4280-4536-aac9-f2e8322e7187", "InvoiceNumber": "INV-0067", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "4f60f64c-8165-4097-8dbb-e16d5ba362b5", "Date": "/Date(*************+0000)/", "BankAmount": 2423.46, "Amount": 3000.0, "CurrencyRate": 1.2379, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "0a39b48b-43da-4137-905b-ace74e4db345", "InvoiceNumber": "INV-0047", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "1c36201b-810c-476e-b474-33e4e01455cb", "Date": "/Date(*************+0000)/", "BankAmount": 9437.88, "Amount": 7204.49, "CurrencyRate": 1.31, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ef95c550-51b5-4d80-bdf9-2ce1bc8688e2", "InvoiceNumber": "INV-0068", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "247e319a-9919-4f82-ab3c-1b4c43ba4a50", "Date": "/Date(*************+0000)/", "BankAmount": 4500.0, "Amount": 4500.0, "Reference": "ch_3Na2FMS5gCcGK53K0maNja6t", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "0d54d1b6-cb68-4082-9220-a3e856218d27", "InvoiceNumber": "INV-0062", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "********-c47f-467e-8c08-0e76a67c6cfa", "Name": "B<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "75287d39-6670-47d5-859d-ad4da5b124c4", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "4aead057-bbd3-4c3f-8dd5-1d2e709581dc", "InvoiceNumber": "INV-0070", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "0001918b-0dc2-4008-b1d4-1c883442db4e", "Date": "/Date(*************+0000)/", "BankAmount": 420.0, "Amount": 420.0, "Reference": "ch_3NgQJmS5gCcGK53K1cMMfH83", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "99213b7b-eeef-4710-bcff-11cf2c49ecb7", "InvoiceNumber": "INV-0072", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "dcd3d599-f5df-4c89-825a-5a579964bcff", "Name": "Face Junkie Ltd", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "cb488103-5f30-4812-9797-874c689b2fd3", "Date": "/Date(*************+0000)/", "BankAmount": 4699.46, "Amount": 3700.0, "CurrencyRate": 1.27012, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ef95c550-51b5-4d80-bdf9-2ce1bc8688e2", "InvoiceNumber": "INV-0068", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "5cab19ec-e389-4fc9-9d4f-7e7840304a11", "BatchPaymentID": "c9d5c3a2-7502-42cd-a915-9898655be43d", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "c9d5c3a2-7502-42cd-a915-9898655be43d", "DateString": "2023-09-07T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 4502.66, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 3136.27, "Amount": 2514.53, "CurrencyRate": 1.24726, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "543de132-f95f-458d-8706-2343e905272c", "InvoiceNumber": "INV-0073", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-0a08-41e2-86d5-96d72be6c815", "BatchPaymentID": "c9d5c3a2-7502-42cd-a915-9898655be43d", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "c9d5c3a2-7502-42cd-a915-9898655be43d", "DateString": "2023-09-07T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 4502.66, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 1366.39, "Amount": 1095.51, "CurrencyRate": 1.24726, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ef95c550-51b5-4d80-bdf9-2ce1bc8688e2", "InvoiceNumber": "INV-0068", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "4789f7f6-2f80-40d3-a1f9-1baa85ab4f0f", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "2b7306f9-9c59-4d20-a106-c74bf3167d45", "InvoiceNumber": "INV-0077", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "ffca65d1-4246-4dd9-ba54-23048b8d5e49", "BatchPaymentID": "f3da6c00-2e89-4f9b-91db-17fc731a2d56", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "f3da6c00-2e89-4f9b-91db-17fc731a2d56", "DateString": "2023-09-21T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 4579.18, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 1458.13, "Amount": 1185.47, "CurrencyRate": 1.23, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "543de132-f95f-458d-8706-2343e905272c", "InvoiceNumber": "INV-0073", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "b6b82de5-22e2-41ce-9ee2-16f7f9097643", "BatchPaymentID": "f3da6c00-2e89-4f9b-91db-17fc731a2d56", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "f3da6c00-2e89-4f9b-91db-17fc731a2d56", "DateString": "2023-09-21T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 4579.18, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 3121.05, "Amount": 2537.44, "CurrencyRate": 1.23, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "42ed8421-bf60-4a4e-9a79-5b1ad41bb1ef", "InvoiceNumber": "INV-0079", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "5b24986d-928a-44f4-9ef4-f3de5661253e", "Date": "/Date(*************+0000)/", "BankAmount": 1500.0, "Amount": 1500.0, "Reference": "ch_3NxYdgS5gCcGK53K0hFG1spp", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "13bdd8f6-7230-496d-a75f-f66a35948d52", "InvoiceNumber": "INV-0080", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "24db7fe0-2a19-4bb0-af43-aaacce2fff94", "Name": "<PERSON> - Sin Dental", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "f8aab87d-149a-4ea2-999f-0b738333beb7", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b690a496-8db5-45bd-b141-cac7a312bcc5", "InvoiceNumber": "INV-0081", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "87639db8-3c0b-40ec-8498-6f0b713ac8c6", "Date": "/Date(*************+0000)/", "BankAmount": 620.0, "Amount": 620.0, "CurrencyRate": 1.21976, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "5ffb4d14-90d7-4af2-8ab5-be7c6a4af29c", "InvoiceNumber": "INV-0078", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c353fc0e-9f29-4f7a-a4e5-c33a236168db", "Name": "E-MOOD SAL", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "959ee1aa-ac31-4fa0-b0c5-0edd3c25eb27", "Date": "/Date(*************+0000)/", "BankAmount": 30.0, "Amount": 30.0, "CurrencyRate": 1.22024, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "5ffb4d14-90d7-4af2-8ab5-be7c6a4af29c", "InvoiceNumber": "INV-0078", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c353fc0e-9f29-4f7a-a4e5-c33a236168db", "Name": "E-MOOD SAL", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "b0d59df8-23f4-48f2-95f3-f248fdd46ccb", "Date": "/Date(*************+0000)/", "BankAmount": 238.78, "Amount": 300.0, "Reference": "ch_3O0QL9S5gCcGK53K0dWQzqkQ", "CurrencyRate": 1.256387, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "954dd6af-0bb1-47cf-aae0-fa3954828775", "InvoiceNumber": "INV-0069", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "21d1ff44-2fa6-4512-8680-f4c619655fee", "Date": "/Date(*************+0000)/", "BankAmount": 3796.0, "Amount": 3796.0, "CurrencyRate": 1.2747, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "c70c977e-653c-444b-8e8c-26a617164164"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "841cb362-5395-426a-99c5-eff4fcbd89d5", "InvoiceNumber": "INV-0071", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3a3cfd91-15ee-4639-af07-d05033c5aebf", "Name": "W. <PERSON> & Sons.", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "5c0f7688-22c0-4a84-a16e-b8fa29e377fa", "Date": "/Date(*************+0000)/", "BankAmount": 1500.0, "Amount": 1500.0, "Reference": "ch_3O80ocS5gCcGK53K1DXvcs0f", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "8543566c-6416-40e7-8f14-4a32adabf68c", "InvoiceNumber": "INV-0082", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "5256b41f-fcd7-404b-aff3-7621ff5ab761", "Name": "<PERSON><PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "1b5a40b9-687e-4a52-aeb6-83fb29fb91ed", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "81e1545f-9a74-49c0-8cd2-4ba288722b62", "InvoiceNumber": "INV-0086", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "2a6a80cf-575b-4ff4-9d76-80768aee5a89", "Date": "/Date(*************+0000)/", "BankAmount": 240.4, "Amount": 300.0, "Reference": "ch_3OBtKqS5gCcGK53K1nBmy7c4", "CurrencyRate": 1.24792, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "be946fb8-9994-40ad-a159-d2e20ce24e24", "InvoiceNumber": "INV-0087", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "b179eccf-2315-441c-b92a-43b8002b27e2", "BatchPaymentID": "c3be7132-baf5-4da2-b38d-f082fac35bea", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "c3be7132-baf5-4da2-b38d-f082fac35bea", "DateString": "2023-11-10T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 3685.17, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 1421.04, "Amount": 1162.56, "CurrencyRate": 1.22234, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "42ed8421-bf60-4a4e-9a79-5b1ad41bb1ef", "InvoiceNumber": "INV-0079", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "379307af-324c-4457-8bdc-ac056fbdbe34", "BatchPaymentID": "c3be7132-baf5-4da2-b38d-f082fac35bea", "BatchPayment": {"Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "BatchPaymentID": "c3be7132-baf5-4da2-b38d-f082fac35bea", "DateString": "2023-11-10T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 3685.17, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 2264.13, "Amount": 1852.3, "CurrencyRate": 1.22233, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a48e5d35-1e3a-4314-8ce0-d5edde562e9c", "InvoiceNumber": "INV-0084", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "c91c88ea-e6a2-4220-b1c2-07354281c2ac", "Date": "/Date(*************+0000)/", "BankAmount": 2800.0, "Amount": 2800.0, "Reference": "ch_3OJQanS5gCcGK53K0l4PgceY", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "8510269c-9bf3-417e-ae92-97e007fe6a6a", "InvoiceNumber": "INV-0085", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "24db7fe0-2a19-4bb0-af43-aaacce2fff94", "Name": "<PERSON> - Sin Dental", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "d99ca525-44bb-4dbc-a2eb-adfb39c8dab6", "Date": "/Date(*************+0000)/", "BankAmount": 233.85, "Amount": 300.0, "Reference": "ch_3OKgjOS5gCcGK53K1aU4C4qa", "CurrencyRate": 1.282874, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ac2d458a-807d-49c3-b9e9-03daebb5baf3", "InvoiceNumber": "INV-0088", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "f36893a4-f427-4028-a166-4123a2a7342a", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "8eedea68-84a6-49ae-a174-a21f69dea014", "InvoiceNumber": "INV-0089", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "1e1b74f2-9f26-439c-89a1-8dacf43a6255", "Date": "/Date(*************+0000)/", "BankAmount": 1700.0, "Amount": 1700.0, "Reference": "ch_3OVHkKS5gCcGK53K1HhwHMFs", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "005da76c-714c-484e-8be2-6db85fbb0540", "InvoiceNumber": "INV-0093", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "9e73f3a8-b490-47fe-9418-478bb0f4d48b", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "be0d2b94-87f5-4328-a07a-fa56b399edee", "Date": "/Date(*************+0000)/", "BankAmount": 231.5, "Amount": 300.0, "Reference": "ch_3OWIN2S5gCcGK53K0MtetPj5", "CurrencyRate": 1.295896, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "82bd4665-33d0-46bf-b11f-47e3b3d05577", "InvoiceNumber": "INV-0091", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "bcc2c68b-738b-4223-bde2-05027419bd42", "Date": "/Date(*************+0000)/", "BankAmount": 3000.0, "Amount": 3000.0, "Reference": "ch_3OWzVaS5gCcGK53K0xL9sNf8", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "af5dc329-f289-4fcb-bede-ce33b6e6db4d", "InvoiceNumber": "INV-0092", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4f5a326c-98af-41c7-9ffa-af60382396cb", "Name": "TOMO BOTTLE LLC", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "eb3ddfb8-dc45-47c7-8b0a-a5fdf6ce0df2", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "ch_3OXPJbS5gCcGK53K0H86KZjg", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "00545fac-bc1a-4c06-8231-99dbdd8eaf99", "InvoiceNumber": "INV-0090", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "a5dfeb4f-b9e8-4205-bdf7-f5ad0bab8171", "Date": "/Date(*************+0000)/", "BankAmount": 233.85, "Amount": 300.0, "Reference": "ch_3OgVKvS5gCcGK53K08S6R5D7", "CurrencyRate": 1.282874, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "7bef7f72-3a49-49ca-97c8-172555587ed5", "InvoiceNumber": "INV-0096", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "871abd1c-cd12-4d7e-966e-fe3ecb7f01fb", "Date": "/Date(*************+0000)/", "BankAmount": 1500.0, "Amount": 1500.0, "Reference": "ch_3Oixl1S5gCcGK53K1kibB1Vo", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "c83157b0-e472-4a82-93ed-e97ad01b1deb", "InvoiceNumber": "INV-0094", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "5256b41f-fcd7-404b-aff3-7621ff5ab761", "Name": "<PERSON><PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "9830cc3e-19f4-456b-a35f-c74ebe29d59f", "Date": "/Date(*************+0000)/", "BankAmount": 100.0, "Amount": 100.0, "Reference": "ch_3OkvCwS5gCcGK53K1P9nfiq5", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b2e3b3e8-afe8-4039-a9aa-4ad165079d4e", "InvoiceNumber": "INV-0097", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "9e73f3a8-b490-47fe-9418-478bb0f4d48b", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "a903f1a7-6975-4a4c-afd4-8242793afead", "Date": "/Date(*************+0000)/", "BankAmount": 300.0, "Amount": 300.0, "Reference": "ch_3Oor4oS5gCcGK53K19CPVeFG", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a9c613f9-1270-456d-b74e-f7c8e8460b53", "InvoiceNumber": "INV-0098", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "24db7fe0-2a19-4bb0-af43-aaacce2fff94", "Name": "<PERSON> - Sin Dental", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "e04abb5d-febb-4610-8548-d13f395932a3", "Date": "/Date(*************+0000)/", "BankAmount": 1147.7, "Amount": 1147.7, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a48e5d35-1e3a-4314-8ce0-d5edde562e9c", "InvoiceNumber": "INV-0084", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "ecb78406-f792-49ed-a9ef-b1dc63910256", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "d6405e5e-1618-4f2d-a7ad-f45e097577e0", "Date": "/Date(*************+0000)/", "BankAmount": 234.59, "Amount": 300.0, "Reference": "ch_3OqwAvS5gCcGK53K144JOSya", "CurrencyRate": 1.278827, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "3ad588d2-66f0-4014-9df7-c4dc895f4a63", "InvoiceNumber": "INV-0103", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "f7f1c37a-c5d6-464f-83b6-4d27cd2d2ed6", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "Reference: PAYMENT INVS 95 PLUS 99 SERVICES OF  JAN AND FEB 2024 JEWELRY", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "413b52fb-535e-4e8f-9f99-b0133243c027", "InvoiceNumber": "INV-0099", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "405a7370-f574-42b3-b6ed-8f1747ade11c", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "Reference: PAYMENT INVS 95 PLUS 99 SERVICES OF  JAN AND FEB 2024 JEWELRY", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "0ebbb9ad-b360-4672-ab95-3535c3b7b86a", "InvoiceNumber": "INV-0095", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "bdcfb606-4cfb-4d08-9879-fdd0fcbf0ed5", "Date": "/Date(*************+0000)/", "BankAmount": 600.0, "Amount": 600.0, "Reference": "ch_3OwQ3PS5gCcGK53K1wmFtUT6", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f5b5f70d-20a9-4fc9-b080-9cccd9c55d3f", "InvoiceNumber": "INV-0101", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "9e73f3a8-b490-47fe-9418-478bb0f4d48b", "Name": "<PERSON>", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "839f3ce8-4870-42d9-986b-b084213cdef7", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "Reference": "ch_3P5jJxS5gCcGK53K05DGA5Dm", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ba43ac40-b620-47c6-b23d-f27d49b4c9a4", "InvoiceNumber": "INV-0106", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "2efb9d90-af51-4058-877c-3d2bc3b541b2", "Date": "/Date(*************+0000)/", "BankAmount": 234.68, "Amount": 300.0, "Reference": "ch_3PFwPQS5gCcGK53K0mm11WeE", "CurrencyRate": 1.278336, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "9e1bc63a-b711-4fe7-848d-47da661e3e67", "InvoiceNumber": "INV-0107", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "97b00792-ebb6-4158-a86b-c288d83635c3", "Date": "/Date(*************+0000)/", "BankAmount": 231.81, "Amount": 300.0, "Reference": "ch_3PVYnWS5gCcGK53K0ucQfWD9", "CurrencyRate": 1.294163, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "6a18428e-60b4-4820-a278-3bf1e982fc2f", "InvoiceNumber": "INV-0110", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c0f82a4d-c6c0-4cbb-a602-643e1f7a956f", "Date": "/Date(*************+0000)/", "BankAmount": 230.66, "Amount": 300.0, "Reference": "ch_3Pj3LxS5gCcGK53K1yrE1eUX", "CurrencyRate": 1.300616, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "fbe8aef3-26b5-4b7e-a7c5-e5256f9f6822", "InvoiceNumber": "INV-0112", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "26f29189-efaf-46d8-b41c-b91f7cb2a079", "Date": "/Date(*************+0000)/", "BankAmount": 6000.0, "Amount": 6000.0, "Reference": "ch_3QLZptS5gCcGK53K0Z6AXuNz", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "6a8ce91c-be67-4623-a115-096b9f66339d", "InvoiceNumber": "INV-0116", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b37b247e-6658-433d-9599-aaf24c1ad232", "Name": "UPOD MEDICAL LTD", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "1b615ed3-4ac9-4802-988e-************", "Date": "/Date(*************+0000)/", "BankAmount": 455.69, "Amount": 600.0, "Reference": "ch_3QIUHqS5gCcGK53K1gmyjJ1P", "CurrencyRate": 1.316685, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "26e40368-0661-41ef-a233-8c8017d01f0f", "InvoiceNumber": "INV-0114", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "467a1233-79cb-4f15-af61-cf5fd1528e66", "Name": "Packegha", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "3c7462cd-925a-4a3e-8658-cce6a39d5dc4", "Date": "/Date(*************+0000)/", "BankAmount": 3980.0, "Amount": 3980.0, "CurrencyRate": 1.26511, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "5145c65e-9456-4b8d-91c9-faafd3e1dfe7"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "815b2dbb-fd57-46b4-9dc5-590239800c9c", "InvoiceNumber": "INV-0104", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4189c0a3-29dd-49af-909b-eb5b117733f4", "Name": "Hosny Homany Industrial Company SAL", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "521fbd46-4616-4c11-88fc-da86dcd3d692", "Date": "/Date(*************+0000)/", "BankAmount": 4215.0, "Amount": 4215.0, "Reference": "Cash payment $3,000 on 27/09/2024 and $1,215 on 25/10/2024", "CurrencyRate": 1.26051, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "c70c977e-653c-444b-8e8c-26a617164164"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "815b2dbb-fd57-46b4-9dc5-590239800c9c", "InvoiceNumber": "INV-0104", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4189c0a3-29dd-49af-909b-eb5b117733f4", "Name": "Hosny Homany Industrial Company SAL", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "25b40eed-ab84-4c5d-85c8-f12a4a2d7c3c", "Date": "/Date(*************+0000)/", "BankAmount": 1745.0, "Amount": 1745.0, "CurrencyRate": 1.26051, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "c70c977e-653c-444b-8e8c-26a617164164"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "338dcb6d-bd8b-49ca-b2d8-f14795f69b19", "InvoiceNumber": "INV-0120", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4189c0a3-29dd-49af-909b-eb5b117733f4", "Name": "Hosny Homany Industrial Company SAL", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "********-909f-4c13-acce-5404c5c3f774", "Date": "/Date(*************+0000)/", "BankAmount": 2500.0, "Amount": 2500.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "76a96146-8bb8-41a5-bd5c-d610ecf9707c", "InvoiceNumber": "INV-0118", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b37b247e-6658-433d-9599-aaf24c1ad232", "Name": "UPOD MEDICAL LTD", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "e62482f3-92c2-40ce-9dde-d64cb3bd9547", "Date": "/Date(*************+0000)/", "BankAmount": 3322.91, "Amount": 3322.91, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "76a96146-8bb8-41a5-bd5c-d610ecf9707c", "InvoiceNumber": "INV-0118", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b37b247e-6658-433d-9599-aaf24c1ad232", "Name": "UPOD MEDICAL LTD", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "ed0c77f5-8591-478c-ad39-de99a9565927", "Date": "/Date(*************+0000)/", "BankAmount": 2500.0, "Amount": 2500.0, "Reference": "ch_3RWDdOS5gCcGK53K09I0cZhy", "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": false, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "092d9b80-078f-4504-aaca-311a4186b33c", "InvoiceNumber": "INV-0121", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b37b247e-6658-433d-9599-aaf24c1ad232", "Name": "UPOD MEDICAL LTD", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}], "summary": {"totalPayments": 103, "byStatus": {"AUTHORISED": 102, "DELETED": 1}, "byType": {"ACCREC": 103}, "byYear": {}, "totalAmounts": {"totalPaid": 117960.***********, "currency": "GBP"}}}
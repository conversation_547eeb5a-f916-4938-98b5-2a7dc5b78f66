{"metadata": {"dataType": "Items", "extractedDate": "2025-07-29T06:38:14.850316", "organization": "MCX3D LTD", "tenantId": "22f8acc9-3fea-4bae-9b97-91f90b532eea", "recordCount": 14, "version": "2.0.0"}, "data": [{"ItemID": "df05ca32-6f65-47ce-9414-7633dfee47bc", "Code": "001", "PurchaseDescription": "FANTECH MOISE", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {"UnitPrice": 6.36, "AccountCode": "720", "TaxType": "INPUT2"}, "SalesDetails": {}, "Name": "FANTECH MOISE", "IsTrackedAsInventory": false, "IsSold": false, "IsPurchased": true}, {"ItemID": "715f9e7a-79e4-42f0-8aeb-8e16a380ae1b", "Code": "002", "PurchaseDescription": "FANTECH FAMING KEYBOARD K511 HUNTER PRO", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {"UnitPrice": 24.02, "AccountCode": "720", "TaxType": "INPUT2"}, "SalesDetails": {}, "Name": "FANTECH FAMING KEYBOARD K511 HUNTER PRO", "IsTrackedAsInventory": false, "IsSold": false, "IsPurchased": true}, {"ItemID": "c6fc2fdd-907f-4c8a-861d-2d5edcaabe19", "Code": "003", "PurchaseDescription": "FANTECH GAMING MOUSE X7 BLAST", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {"UnitPrice": 9.18, "AccountCode": "720", "TaxType": "INPUT2"}, "SalesDetails": {}, "Name": "FANTECH GAMING MOUSE X7 BLAST", "IsTrackedAsInventory": false, "IsSold": false, "IsPurchased": true}, {"ItemID": "6b400b10-47ff-4086-bd5c-de1493fc1ada", "Code": "004", "PurchaseDescription": "LOGITECH MOUSE WIRLESS M220", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {"UnitPrice": 11.3, "AccountCode": "720", "TaxType": "INPUT2"}, "SalesDetails": {}, "Name": "LOGITECH MOUSE WIRLESS M220", "IsTrackedAsInventory": false, "IsSold": false, "IsPurchased": true}, {"ItemID": "35f01d31-617f-48d1-88b5-622695f30c9e", "Code": "100", "Description": "L'atelier Nawbar’s 3D Configurator with less than 10,000 monthly views", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 60.0, "AccountCode": "200", "TaxType": "OUTPUT2"}, "Name": "3D Configurator", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "fb970b98-dd1e-428d-925d-c968a3543d65", "Code": "101", "Description": "L'atelier Nawbar’s 3D Vitrine", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 250.0, "AccountCode": "200", "TaxType": "OUTPUT2"}, "Name": "3D Vitrine", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "48bc9e29-3fec-4c32-b3b4-a683d3741301", "Code": "102", "Description": "3D Modeling - Men’s Shoe", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 400.0, "AccountCode": "250", "TaxType": "OUTPUT2"}, "Name": "3D Modeling - Men’s Shoe", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "fbdca24f-20e3-4e41-8fe8-3a6da0398e0d", "Code": "103", "Description": "3D Modeling", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 200.0, "AccountCode": "250", "TaxType": "OUTPUT2"}, "Name": "3D Modeling Women Shoes", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "4f33a5f2-32f4-425e-ae70-************", "Code": "104", "Description": "Modeling Indoor Slippers", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 150.0, "AccountCode": "250", "TaxType": "OUTPUT2"}, "Name": "Modeling Indoor Slippers", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "a8e38172-0f6e-4a7c-b159-da8a214aaabf", "Code": "105", "Description": "Textures", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 0.0, "AccountCode": "250", "TaxType": "OUTPUT2"}, "Name": "Textures", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "4966aef4-2e1e-46ec-b56e-e540dac524a7", "Code": "110", "Description": "3D Modeling", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 0.0, "AccountCode": "250", "TaxType": "OUTPUT2"}, "Name": "3D Modeling", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "03b0a864-2bb4-41a9-a7f9-f27c35f29d22", "Code": "200", "Description": "Bilal Itani Monthly Allowance Phase 1", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 800.0, "AccountCode": "260", "TaxType": "NONE"}, "Name": "Bilal Itani Monthly Allowance Phase 1", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "f9a3a4dd-2ffd-463a-9c78-0ea3918caef5", "Code": "301", "Description": "<PERSON>up <PERSON>", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {"UnitPrice": 0.0, "AccountCode": "200", "TaxType": "OUTPUT2"}, "Name": "<PERSON>up <PERSON><PERSON>", "IsTrackedAsInventory": false, "IsSold": true, "IsPurchased": false}, {"ItemID": "d720c342-5902-4727-8703-08439fad1b04", "Code": "Website", "UpdatedDateUTC": "/Date(*************+0000)/", "PurchaseDetails": {}, "SalesDetails": {}, "IsTrackedAsInventory": false, "IsSold": false, "IsPurchased": true}], "summary": {"count": 14, "first_record_date": null, "last_record_date": null}}
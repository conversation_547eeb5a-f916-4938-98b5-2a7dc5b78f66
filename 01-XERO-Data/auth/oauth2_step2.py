#!/usr/bin/env python3
"""
Enhanced OAuth2 Step 2: Process Callback and Exchange Tokens
Improved version with better error handling and token management
"""

import os
import json
import requests
from datetime import datetime, timedelta
from pathlib import Path
from urllib.parse import urlparse, parse_qs
from dotenv import load_dotenv

def load_environment():
    """Load environment variables from config directory"""
    # Try config directory first
    config_dir = Path(__file__).parent.parent / "config"
    env_path = config_dir / '.env'
    
    if not env_path.exists():
        # Fallback to parent directory
        env_path = Path(__file__).parent.parent / '.env'
    
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        return True
    else:
        print("❌ No .env file found. Please create one with your Xero credentials.")
        return False

def load_session_data():
    """Load session data from step 1"""
    # Check config directory first
    config_dir = Path(__file__).parent.parent / "config"
    session_file = config_dir / "xero_session.json"
    
    if not session_file.exists():
        # Fallback to parent directory
        session_file = Path(__file__).parent.parent / "xero_session.json"
    
    if not session_file.exists():
        print("❌ No session file found. Please run auth/oauth2_step1.py first.")
        return None
    
    try:
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        # Check if session expired (10 minutes)
        timestamp = datetime.fromisoformat(session_data["timestamp"])
        expires_at = timestamp + timedelta(minutes=session_data.get("expires_in_minutes", 10))
        
        if datetime.now() > expires_at:
            print("❌ Session expired. Please run auth/oauth2_step1.py again.")
            return None
        
        print("✅ Session data loaded successfully")
        return session_data
        
    except Exception as e:
        print(f"❌ Error loading session data: {e}")
        return None

def parse_callback_url(callback_url):
    """Parse authorization code and state from callback URL"""
    try:
        parsed_url = urlparse(callback_url)
        query_params = parse_qs(parsed_url.query)
        
        code = query_params.get('code', [None])[0]
        state = query_params.get('state', [None])[0]
        error = query_params.get('error', [None])[0]
        
        if error:
            print(f"❌ Authorization error: {error}")
            error_description = query_params.get('error_description', [None])[0]
            if error_description:
                print(f"   Description: {error_description}")
            return None, None, error
        
        if not code or not state:
            print("❌ Missing authorization code or state parameter in callback URL")
            return None, None, "missing_parameters"
        
        print("✅ Callback URL parsed successfully")
        print(f"   Code: {code[:10]}...")
        print(f"   State: {state[:10]}...")
        
        return code, state, None
        
    except Exception as e:
        print(f"❌ Error parsing callback URL: {e}")
        return None, None, str(e)

def exchange_code_for_tokens(code, code_verifier):
    """Exchange authorization code for access tokens"""
    # Load environment
    if not load_environment():
        return None
    
    CLIENT_ID = os.getenv("XERO_CLIENT_ID")
    CLIENT_SECRET = os.getenv("XERO_CLIENT_SECRET") 
    REDIRECT_URI = os.getenv("XERO_REDIRECT_URI", "http://localhost:8000/api/auth/xero/callback")
    
    print("🔄 Exchanging authorization code for tokens...")
    
    # Prepare token request
    token_url = "https://identity.xero.com/connect/token"
    
    data = {
        'grant_type': 'authorization_code',
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET,
        'code': code,
        'redirect_uri': REDIRECT_URI,
        'code_verifier': code_verifier
    }
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        response = requests.post(token_url, data=data, headers=headers)
        
        if response.status_code == 200:
            tokens = response.json()
            print("✅ Tokens received successfully!")
            
            # Add expiration timestamp
            expires_in = tokens.get('expires_in', 1800)  # Default 30 minutes
            expires_at = datetime.now() + timedelta(seconds=expires_in)
            tokens['expires_at'] = expires_at.isoformat()
            
            print(f"   Access Token: {tokens['access_token'][:20]}...")
            print(f"   Refresh Token: {tokens.get('refresh_token', 'N/A')[:20]}...")
            print(f"   Expires In: {expires_in} seconds ({expires_in//60} minutes)")
            print(f"   Scopes: {tokens.get('scope', 'N/A')}")
            
            return tokens
        else:
            print(f"❌ Token exchange failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error during token exchange: {e}")
        return None

def save_tokens(tokens):
    """Save tokens to file"""
    # Save to config directory if it exists, otherwise parent
    config_dir = Path(__file__).parent.parent / "config"
    if config_dir.exists():
        token_file = config_dir / "xero_token.json"
    else:
        token_file = Path(__file__).parent.parent / "xero_token.json"
    
    try:
        with open(token_file, 'w') as f:
            json.dump(tokens, f, indent=2)
        
        print(f"✅ Tokens saved successfully: {token_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving tokens: {e}")
        return False

def cleanup_session():
    """Clean up session file after successful completion"""
    session_files = [
        Path(__file__).parent.parent / "config" / "xero_session.json",
        Path(__file__).parent.parent / "xero_session.json"
    ]
    
    for session_file in session_files:
        if session_file.exists():
            try:
                session_file.unlink()
                print(f"🧹 Cleaned up session file: {session_file}")
            except Exception as e:
                print(f"⚠️ Could not clean up session file: {e}")

def process_callback_and_get_tokens(callback_url=None):
    """
    Process callback URL and exchange for tokens
    
    Args:
        callback_url: The callback URL from Xero (if None, will prompt user)
        
    Returns:
        dict: Token data or None if failed
    """
    print("🔐 MCX3D Financials - Xero OAuth2 Step 2")
    print("=" * 50)
    
    # Load session data from step 1
    session_data = load_session_data()
    if not session_data:
        return None
    
    # Get callback URL if not provided
    if not callback_url:
        print("\n📋 Please paste the complete callback URL from your browser:")
        print("(This is the URL you were redirected to, even if the page showed an error)")
        callback_url = input("Callback URL: ").strip()
    
    if not callback_url:
        print("❌ No callback URL provided.")
        return None
    
    # Parse callback URL
    code, state, error = parse_callback_url(callback_url)
    if error:
        return None
    
    # Verify state parameter
    if state != session_data["state"]:
        print("❌ State parameter mismatch. Possible security issue or expired session.")
        return None
    
    print("✅ State parameter verified")
    
    # Exchange code for tokens
    tokens = exchange_code_for_tokens(code, session_data["code_verifier"])
    if not tokens:
        return None
    
    # Save tokens
    if not save_tokens(tokens):
        return None
    
    # Cleanup session data
    cleanup_session()
    
    print("\n🎉 OAuth2 authentication completed successfully!")
    print("You can now use the Xero API with the saved tokens.")
    print("\n📝 Next Steps:")
    print("1. Run data extraction scripts: python extractors/extract_all.py")
    print("2. Or run specific extractors from the extractors/ directory")
    
    return tokens

def main():
    """Main execution function"""
    tokens = process_callback_and_get_tokens()
    
    if tokens:
        print("\n🎉 Step 2 completed successfully!")
        print("Authentication is complete. You can now extract data from Xero.")
    else:
        print("\n❌ Step 2 failed. Please check the errors above and try again.")
        print("You may need to run auth/oauth2_step1.py again if the session expired.")
        exit(1)

if __name__ == "__main__":
    main()
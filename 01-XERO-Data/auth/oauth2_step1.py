#!/usr/bin/env python3
"""
Enhanced OAuth2 Step 1: Generate Authorization URL with PKCE
Improved version with better error handling and user guidance
"""

import os
import json
import secrets
import base64
import hashlib
import webbrowser
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

def load_environment():
    """Load environment variables from config directory"""
    # Try config directory first
    config_dir = Path(__file__).parent.parent / "config"
    env_path = config_dir / '.env'
    
    if not env_path.exists():
        # Fallback to parent directory
        env_path = Path(__file__).parent.parent / '.env'
    
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        return True
    else:
        print("❌ No .env file found. Please create one with your Xero credentials.")
        return False

def generate_pkce_params():
    """Generate PKCE parameters for enhanced security"""
    # Generate code verifier (43-128 characters)
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    # Generate code challenge
    code_challenge = base64.urlsafe_b64encode(
        hashlib.sha256(code_verifier.encode('utf-8')).digest()
    ).decode('utf-8').rstrip('=')
    
    return code_verifier, code_challenge

def save_session_data(code_verifier, state):
    """Save session data for step 2"""
    session_data = {
        "code_verifier": code_verifier,
        "state": state,
        "timestamp": datetime.now().isoformat(),
        "expires_in_minutes": 10  # PKCE codes expire quickly
    }
    
    # Save to config directory if it exists, otherwise parent
    config_dir = Path(__file__).parent.parent / "config"
    if config_dir.exists():
        session_file = config_dir / "xero_session.json"
    else:
        session_file = Path(__file__).parent.parent / "xero_session.json"
    
    try:
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2)
        print(f"✅ Session data saved: {session_file}")
        return True
    except Exception as e:
        print(f"❌ Error saving session data: {e}")
        return False

def generate_authorization_url(auto_open=True):
    """
    Generate Xero authorization URL with PKCE
    
    Args:
        auto_open: Whether to automatically open the URL in browser
        
    Returns:
        tuple: (authorization_url, success)
    """
    print("🔐 MCX3D Financials - Xero OAuth2 Step 1")
    print("=" * 50)
    
    # Load environment
    if not load_environment():
        return None, False
    
    # Get required environment variables
    CLIENT_ID = os.getenv("XERO_CLIENT_ID")
    CLIENT_SECRET = os.getenv("XERO_CLIENT_SECRET")
    REDIRECT_URI = os.getenv("XERO_REDIRECT_URI", "http://localhost:8000/api/auth/xero/callback")
    SCOPES = os.getenv("XERO_SCOPES", "accounting.transactions accounting.contacts accounting.reports.read accounting.settings offline_access")
    
    # Validate required credentials
    if not all([CLIENT_ID, CLIENT_SECRET]):
        print("❌ Missing required environment variables:")
        if not CLIENT_ID:
            print("   - XERO_CLIENT_ID")
        if not CLIENT_SECRET:
            print("   - XERO_CLIENT_SECRET")
        print("\nPlease check your .env file configuration.")
        return None, False
    
    print("✅ Environment configuration loaded")
    print(f"   Client ID: {CLIENT_ID[:8]}...")
    print(f"   Redirect URI: {REDIRECT_URI}")
    print(f"   Scopes: {SCOPES}")
    
    try:
        # Generate PKCE parameters
        print("\n🔒 Generating PKCE security parameters...")
        code_verifier, code_challenge = generate_pkce_params()
        
        # Generate state parameter for security
        state = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        
        # Save session data for step 2
        if not save_session_data(code_verifier, state):
            return None, False
        
        # Build authorization URL
        auth_url = (
            "https://login.xero.com/identity/connect/authorize?"
            f"response_type=code&"
            f"client_id={CLIENT_ID}&"
            f"redirect_uri={REDIRECT_URI}&"
            f"scope={SCOPES}&"
            f"state={state}&"
            f"code_challenge={code_challenge}&"
            f"code_challenge_method=S256"
        )
        
        print("\n🎉 Authorization URL generated successfully!")
        print(f"📋 URL: {auth_url}")
        
        # Open browser automatically if requested
        if auto_open:
            print("\n🌐 Opening authorization URL in your default browser...")
            try:
                webbrowser.open(auth_url)
                print("✅ Browser opened successfully")
            except Exception as e:
                print(f"⚠️ Could not open browser automatically: {e}")
                print("Please copy and paste the URL above into your browser manually.")
        
        print("\n📝 Next Steps:")
        print("1. Complete the authorization in your browser")
        print("2. Copy the callback URL from your browser (even if it shows an error page)")
        print("3. Run: python auth/oauth2_step2.py")
        print("4. Paste the callback URL when prompted")
        
        print("\n⏰ Note: PKCE codes expire in 10 minutes. Complete step 2 quickly!")
        
        return auth_url, True
        
    except Exception as e:
        print(f"❌ Error generating authorization URL: {e}")
        return None, False

def main():
    """Main execution function"""
    url, success = generate_authorization_url()
    
    if success:
        print("\n🎉 Step 1 completed successfully!")
        print("Proceed to Step 2 after completing browser authorization.")
    else:
        print("\n❌ Step 1 failed. Please check your configuration and try again.")
        exit(1)

if __name__ == "__main__":
    main()
"""
Growth Accounting and Revenue Waterfall Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots


class GrowthAccountingVisualizer:
    """Create publication-quality growth accounting visualizations"""
    
    def __init__(self, metrics_calculator):
        """Initialize with a SaaSMetricsCalculator instance"""
        self.metrics = metrics_calculator
        self.growth_data = metrics_calculator.calculate_growth_accounting()
        
    def create_revenue_waterfall(self, save_path=None):
        """Create revenue waterfall chart showing growth components"""
        
        # Prepare waterfall data
        years = self.growth_data['year'].tolist()[1:]  # Skip first year
        
        fig = go.Figure()
        
        for idx, year in enumerate(years):
            row = self.growth_data[self.growth_data['year'] == year].iloc[0]
            prior_row = self.growth_data[self.growth_data['year'] == year-1].iloc[0]
            
            # Starting revenue
            if idx == 0:
                fig.add_trace(go.Waterfall(
                    x=[f"{year-1} Revenue"],
                    y=[prior_row['total_revenue']],
                    measure=["absolute"],
                    text=[f"£{prior_row['total_revenue']:,.0f}"],
                    textposition="outside",
                    connector={"line": {"color": "rgb(63, 63, 63)"}},
                    name="Starting"
                ))
            
            # Components
            components = [
                (f"{year} New", row['new_revenue'], 'relative', 'green'),
                (f"{year} Expansion", row['expansion_revenue'], 'relative', 'darkgreen'),
                (f"{year} Contraction", row['contraction_revenue'], 'relative', 'orange'),
                (f"{year} Churn", row['churn_revenue'], 'relative', 'red'),
                (f"{year} Total", row['total_revenue'], 'total', 'blue')
            ]
            
            for label, value, measure, color in components:
                fig.add_trace(go.Waterfall(
                    x=[label],
                    y=[value],
                    measure=[measure],
                    text=[f"£{abs(value):,.0f}"],
                    textposition="outside",
                    increasing={"marker": {"color": color if measure == 'relative' and value > 0 else None}},
                    decreasing={"marker": {"color": color if measure == 'relative' and value < 0 else None}},
                    totals={"marker": {"color": color if measure == 'total' else None}},
                    connector={"line": {"color": "rgb(63, 63, 63)"}},
                    showlegend=False
                ))
        
        # Update layout
        fig.update_layout(
            title="MCX3D Revenue Growth Waterfall (2020-2024)",
            title_font_size=20,
            xaxis_title="Components",
            yaxis_title="Revenue (£)",
            height=600,
            showlegend=False,
            yaxis=dict(tickformat=",.0f"),
            template="plotly_white"
        )
        
        fig.update_xaxes(tickangle=-45)
        
        if save_path:
            fig.write_html(save_path)
            # Image export requires kaleido package
            try:
                fig.write_image(save_path.replace('.html', '.png'))
            except Exception:
                pass  # Skip image export if kaleido not installed
        
        return fig
    
    def create_growth_decomposition(self, save_path=None):
        """Create growth rate decomposition chart"""
        
        # Calculate growth contributions
        self.growth_data['yoy_growth'] = self.growth_data['total_revenue'].pct_change() * 100
        self.growth_data['new_contribution'] = (
            self.growth_data['new_revenue'] / self.growth_data['total_revenue'].shift(1) * 100
        )
        self.growth_data['expansion_contribution'] = (
            self.growth_data['expansion_revenue'] / self.growth_data['total_revenue'].shift(1) * 100
        )
        self.growth_data['churn_impact'] = (
            self.growth_data['churn_revenue'] / self.growth_data['total_revenue'].shift(1) * 100
        )
        
        # Create visualization
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('YoY Revenue Growth', 'Growth Components',
                           'Customer Dynamics', 'Revenue per Customer'),
            specs=[[{'type': 'scatter'}, {'type': 'bar'}],
                   [{'type': 'bar'}, {'type': 'scatter'}]]
        )
        
        years = self.growth_data['year'].tolist()[1:]
        
        # YoY Growth
        fig.add_trace(
            go.Scatter(
                x=years,
                y=self.growth_data['yoy_growth'][1:].tolist(),
                mode='lines+markers+text',
                text=[f"{g:.0f}%" for g in self.growth_data['yoy_growth'][1:].tolist()],
                textposition='top center',
                line=dict(color='darkblue', width=3),
                marker=dict(size=10)
            ),
            row=1, col=1
        )
        
        # Growth components
        fig.add_trace(
            go.Bar(x=years, y=self.growth_data['new_contribution'][1:].tolist(),
                   name='New Customers', marker_color='lightblue'),
            row=1, col=2
        )
        fig.add_trace(
            go.Bar(x=years, y=self.growth_data['expansion_contribution'][1:].tolist(),
                   name='Expansion', marker_color='darkgreen'),
            row=1, col=2
        )
        fig.add_trace(
            go.Bar(x=years, y=self.growth_data['churn_impact'][1:].tolist(),
                   name='Churn', marker_color='red'),
            row=1, col=2
        )
        
        # Customer dynamics
        fig.add_trace(
            go.Bar(x=self.growth_data['year'], y=self.growth_data['new_customers'],
                   name='New', marker_color='green'),
            row=2, col=1
        )
        fig.add_trace(
            go.Bar(x=self.growth_data['year'], y=-self.growth_data['churned_customers'],
                   name='Churned', marker_color='red'),
            row=2, col=1
        )
        
        # Revenue per customer
        revenue_per_customer = (
            self.growth_data['total_revenue'] / self.growth_data['customer_count']
        )
        fig.add_trace(
            go.Scatter(
                x=self.growth_data['year'],
                y=revenue_per_customer,
                mode='lines+markers+text',
                text=[f"£{r:,.0f}" for r in revenue_per_customer],
                textposition='top center',
                line=dict(color='purple', width=3),
                marker=dict(size=10)
            ),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            height=800,
            title_text="MCX3D Growth Dynamics Analysis",
            title_font_size=20,
            showlegend=True,
            template="plotly_white"
        )
        
        # Update axes
        fig.update_yaxes(title_text="Growth Rate (%)", row=1, col=1)
        fig.update_yaxes(title_text="Contribution (%)", row=1, col=2)
        fig.update_yaxes(title_text="Customer Count", row=2, col=1)
        fig.update_yaxes(title_text="Revenue per Customer (£)", row=2, col=2)
        
        if save_path:
            fig.write_html(save_path)
            # Image export requires kaleido package
            try:
                fig.write_image(save_path.replace('.html', '.png'))
            except Exception:
                pass  # Skip image export if kaleido not installed
        
        return fig
    
    def create_cohort_retention_chart(self, save_path=None):
        """Create cohort retention analysis visualization"""
        
        # Calculate cohort retention
        cohort_data = []
        for cohort_year in range(2020, 2024):
            for measure_year in range(cohort_year, 2025):
                ndr = self.metrics.calculate_ndr(cohort_year, measure_year)
                if ndr['cohort_size'] > 0:
                    cohort_data.append({
                        'cohort': cohort_year,
                        'year': measure_year,
                        'period': measure_year - cohort_year,
                        'ndr': ndr['ndr'],
                        'revenue': ndr['ending_revenue'],
                        'customers': ndr['cohort_size']
                    })
        
        cohort_df = pd.DataFrame(cohort_data)
        
        # Create visualization
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Cohort Net Dollar Retention', 'Revenue Evolution by Cohort')
        )
        
        # NDR curves
        for cohort in cohort_df['cohort'].unique():
            data = cohort_df[cohort_df['cohort'] == cohort]
            fig.add_trace(
                go.Scatter(
                    x=data['year'],
                    y=data['ndr'],
                    mode='lines+markers',
                    name=f'{cohort} Cohort',
                    line=dict(width=3)
                ),
                row=1, col=1
            )
        
        # Add reference lines
        fig.add_hline(y=100, line_dash="dash", line_color="gray",
                      annotation_text="100%", row=1, col=1)
        fig.add_hline(y=110, line_dash="dash", line_color="green",
                      annotation_text="Best-in-class", row=1, col=1)
        
        # Revenue evolution
        for cohort in cohort_df['cohort'].unique():
            data = cohort_df[cohort_df['cohort'] == cohort]
            fig.add_trace(
                go.Scatter(
                    x=data['year'],
                    y=data['revenue'],
                    mode='lines+markers',
                    name=f'{cohort} Cohort',
                    showlegend=False,
                    line=dict(width=3)
                ),
                row=1, col=2
            )
        
        # Update layout
        fig.update_layout(
            height=500,
            title_text="Customer Cohort Analysis",
            title_font_size=20,
            template="plotly_white"
        )
        
        fig.update_yaxes(title_text="NDR (%)", row=1, col=1)
        fig.update_yaxes(title_text="Revenue (£)", row=1, col=2)
        fig.update_xaxes(title_text="Year", row=1, col=1)
        fig.update_xaxes(title_text="Year", row=1, col=2)
        
        if save_path:
            fig.write_html(save_path)
            # Image export requires kaleido package
            try:
                fig.write_image(save_path.replace('.html', '.png'))
            except Exception:
                pass  # Skip image export if kaleido not installed
        
        return fig
    
    def create_executive_dashboard(self, save_path=None):
        """Create executive summary dashboard"""
        
        # Get all metrics
        summary = self.metrics.generate_valuation_metrics_summary()
        concentration = self.metrics.calculate_revenue_concentration()
        ltv_cac = self.metrics.calculate_ltv_cac_implied()
        rule_40 = self.metrics.calculate_rule_of_40()
        quality = self.metrics.calculate_revenue_quality_score()
        
        # Create figure
        fig = make_subplots(
            rows=3, cols=3,
            subplot_titles=('2024 ARR', 'Customer Growth', 'Net Dollar Retention',
                           'LTV:CAC Ratio', 'Rule of 40', 'Revenue Quality',
                           'Customer Concentration', 'Valuation Range', 'Path to £1M'),
            specs=[[{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],
                   [{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],
                   [{'type': 'indicator'}, {'type': 'bar'}, {'type': 'scatter'}]],
            row_heights=[0.3, 0.3, 0.4]
        )
        
        # ARR indicator
        fig.add_trace(
            go.Indicator(
                mode="number+delta",
                value=summary['revenue_metrics']['arr_2024'],
                delta={'reference': 11937, 'relative': True, 'valueformat': '.0%'},
                number={'prefix': "£", 'valueformat': ",.0f"},
                title={'text': "Annual Recurring Revenue"}
            ),
            row=1, col=1
        )
        
        # Customer count
        fig.add_trace(
            go.Indicator(
                mode="number+delta",
                value=summary['revenue_metrics']['customer_count'],
                delta={'reference': 2, 'valueformat': '+0'},
                title={'text': "Active Customers"}
            ),
            row=1, col=2
        )
        
        # NDR gauge
        latest_ndr = self.metrics.calculate_ndr(2023, 2024)
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=latest_ndr['ndr'],
                gauge={'axis': {'range': [0, 500]},
                       'bar': {'color': "darkblue"},
                       'steps': [
                           {'range': [0, 100], 'color': "lightgray"},
                           {'range': [100, 110], 'color': "gray"},
                           {'range': [110, 500], 'color': "lightgreen"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                    'thickness': 0.75, 'value': 110}},
                title={'text': "Net Dollar Retention %"}
            ),
            row=1, col=3
        )
        
        # LTV:CAC
        fig.add_trace(
            go.Indicator(
                mode="number",
                value=ltv_cac['ltv_cac_ratio'],
                number={'suffix': ":1", 'valueformat': '.1f'},
                title={'text': "LTV:CAC Ratio"}
            ),
            row=2, col=1
        )
        
        # Rule of 40
        fig.add_trace(
            go.Indicator(
                mode="number",
                value=rule_40['rule_of_40_score'],
                number={'suffix': "%", 'valueformat': '.0f'},
                title={'text': "Rule of 40 Score"}
            ),
            row=2, col=2
        )
        
        # Revenue quality
        fig.add_trace(
            go.Indicator(
                mode="number",
                value=quality['revenue_quality_score'],
                number={'suffix': "/100", 'valueformat': '.0f'},
                title={'text': "Revenue Quality"}
            ),
            row=2, col=3
        )
        
        # HHI
        fig.add_trace(
            go.Indicator(
                mode="number",
                value=concentration['hhi'],
                number={'valueformat': '.3f'},
                title={'text': "HHI Concentration"}
            ),
            row=3, col=1
        )
        
        # Valuation range
        val_data = summary['valuation_indicators']
        fig.add_trace(
            go.Bar(
                x=['Conservative\n(5x)', 'Mid-Range\n(6.5x)', 'Optimistic\n(8x)'],
                y=[val_data['implied_valuation_low'],
                   val_data['implied_valuation_mid'],
                   val_data['implied_valuation_high']],
                text=[f"£{v/1e6:.1f}M" for v in [val_data['implied_valuation_low'],
                                                  val_data['implied_valuation_mid'],
                                                  val_data['implied_valuation_high']]],
                textposition='outside',
                marker_color=['lightcoral', 'lightblue', 'lightgreen']
            ),
            row=3, col=2
        )
        
        # Path to £1M
        current_arr = summary['revenue_metrics']['arr_2024']
        years = [2024, 2025, 2026, 2027]
        revenues = [current_arr]
        for i in range(3):
            revenues.append(revenues[-1] * 1.5)  # 50% growth
        
        fig.add_trace(
            go.Scatter(
                x=years,
                y=revenues,
                mode='lines+markers+text',
                text=[f"£{r/1000:.0f}K" for r in revenues],
                textposition='top center',
                line=dict(color='green', width=3)
            ),
            row=3, col=3
        )
        # Add £1M target line manually as a scatter trace
        fig.add_trace(
            go.Scatter(
                x=years,
                y=[1000000] * len(years),
                mode='lines',
                line=dict(color='red', width=2, dash='dash'),
                name='£1M Target',
                showlegend=False
            ),
            row=3, col=3
        )
        
        # Update layout
        fig.update_layout(
            height=1000,
            title_text="MCX3D Valuation Metrics Executive Dashboard",
            title_font_size=24,
            showlegend=False,
            template="plotly_white"
        )
        
        if save_path:
            fig.write_html(save_path)
            # Image export requires kaleido package
            try:
                fig.write_image(save_path.replace('.html', '.png'))
            except Exception:
                pass  # Skip image export if kaleido not installed
        
        return fig


if __name__ == "__main__":
    # Test the visualizations
    import os
    import sys
    sys.path.append(os.path.dirname(__file__))
    from valuation_metrics import SaaSMetricsCalculator
    
    base_path = "../../03-Financial-Foundation/data"
    transactions_path = f"{base_path}/all_transactions_2020_onwards.json"
    customer_path = f"{base_path}/revenue/customer_revenue_analysis.json"
    
    # Initialize
    metrics = SaaSMetricsCalculator(transactions_path, customer_path)
    visualizer = GrowthAccountingVisualizer(metrics)
    
    # Create output directory
    output_dir = "../data/derived_metrics/visualizations"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate all visualizations
    print("Generating growth accounting visualizations...")
    
    fig1 = visualizer.create_revenue_waterfall(f"{output_dir}/revenue_waterfall.html")
    print("✓ Revenue waterfall created")
    
    fig2 = visualizer.create_growth_decomposition(f"{output_dir}/growth_decomposition.html")
    print("✓ Growth decomposition created")
    
    fig3 = visualizer.create_cohort_retention_chart(f"{output_dir}/cohort_retention.html")
    print("✓ Cohort retention analysis created")
    
    fig4 = visualizer.create_executive_dashboard(f"{output_dir}/executive_dashboard.html")
    print("✓ Executive dashboard created")
    
    print("\nAll visualizations saved to:", output_dir)
"""
MCX3D Valuation Metrics Calculator
Core module for calculating institutional-grade SaaS metrics for valuation analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json


class SaaSMetricsCalculator:
    """Calculate key SaaS metrics for valuation analysis"""
    
    def __init__(self, transactions_path: str, customer_analysis_path: str):
        """
        Initialize with financial data paths
        
        Args:
            transactions_path: Path to all_transactions JSON file
            customer_analysis_path: Path to customer revenue analysis JSON
        """
        self.transactions = self._load_transactions(transactions_path)
        self.customer_data = self._load_customer_data(customer_analysis_path)
        
    def _load_transactions(self, path: str) -> pd.DataFrame:
        """Load and prepare transaction data"""
        with open(path, 'r') as f:
            data = json.load(f)
        
        df = pd.DataFrame(data['transactions'])
        
        # Filter for revenue transactions only (invoices)
        df = df[df['type'] == 'invoice'].copy()
        
        df['date'] = pd.to_datetime(df['date'])
        df['year'] = df['date'].dt.year
        df['quarter'] = df['date'].dt.to_period('Q')
        df['month'] = df['date'].dt.to_period('M')
        
        return df
    
    def _load_customer_data(self, path: str) -> Dict:
        """Load customer revenue analysis data"""
        with open(path, 'r') as f:
            return json.load(f)
    
    def calculate_ndr(self, cohort_year: int, measure_year: int) -> Dict[str, float]:
        """
        Calculate Net Dollar Retention for a specific cohort
        
        NDR = (Starting MRR + Expansion - Contraction - Churn) / Starting MRR
        
        Args:
            cohort_year: Year when customers first purchased
            measure_year: Year to measure retention against
            
        Returns:
            Dictionary with NDR metrics
        """
        # Get cohort customers
        cohort_customers = []
        for customer, data in self.customer_data['customers'].items():
            first_year = pd.to_datetime(data['first_transaction']).year
            if first_year == cohort_year:
                cohort_customers.append(customer)
        
        if not cohort_customers:
            return {'ndr': 0, 'cohort_size': 0}
        
        # Calculate starting and ending revenue
        starting_revenue = 0
        ending_revenue = 0
        expansion_revenue = 0
        
        for customer in cohort_customers:
            customer_data = self.customer_data['customers'][customer]
            yearly_revenue = customer_data.get('revenue_by_year', {})
            
            start_rev = float(yearly_revenue.get(str(cohort_year), 0))
            end_rev = float(yearly_revenue.get(str(measure_year), 0))
            
            starting_revenue += start_rev
            ending_revenue += end_rev
            
            # Track expansion
            if end_rev > start_rev:
                expansion_revenue += (end_rev - start_rev)
        
        # Calculate NDR
        if starting_revenue > 0:
            ndr = (ending_revenue / starting_revenue) * 100
            gross_retention = min(ending_revenue / starting_revenue, 1.0) * 100
            net_expansion = max(0, (ending_revenue - starting_revenue) / starting_revenue) * 100
        else:
            ndr = 0
            gross_retention = 0
            net_expansion = 0
        
        return {
            'ndr': round(ndr, 1),
            'gross_retention': round(gross_retention, 1),
            'net_expansion': round(net_expansion, 1),
            'cohort_size': len(cohort_customers),
            'starting_revenue': starting_revenue,
            'ending_revenue': ending_revenue,
            'expansion_revenue': expansion_revenue
        }
    
    def calculate_revenue_concentration(self) -> Dict[str, float]:
        """
        Calculate customer concentration metrics (Herfindahl-Hirschman Index)
        
        HHI ranges:
        - < 0.15: Unconcentrated
        - 0.15-0.25: Moderate concentration
        - > 0.25: High concentration
        """
        # Calculate total revenue per customer
        customer_revenues = {}
        total_revenue = 0
        
        for customer, data in self.customer_data['customers'].items():
            revenue = data['total_revenue']
            customer_revenues[customer] = revenue
            total_revenue += revenue
        
        # Calculate HHI
        hhi = 0
        revenue_shares = []
        
        for customer, revenue in customer_revenues.items():
            share = revenue / total_revenue
            revenue_shares.append((customer, share))
            hhi += share ** 2
        
        # Sort by revenue share
        revenue_shares.sort(key=lambda x: x[1], reverse=True)
        
        # Calculate top customer concentrations
        top_1_share = revenue_shares[0][1] if revenue_shares else 0
        top_5_share = sum(share for _, share in revenue_shares[:5])
        top_10_share = sum(share for _, share in revenue_shares[:10])
        
        return {
            'hhi': round(hhi, 4),
            'hhi_interpretation': self._interpret_hhi(hhi),
            'top_1_customer': revenue_shares[0][0] if revenue_shares else None,
            'top_1_share': round(top_1_share * 100, 1),
            'top_5_share': round(top_5_share * 100, 1),
            'top_10_share': round(top_10_share * 100, 1),
            'customer_count': len(customer_revenues),
            'revenue_distribution': revenue_shares[:10]  # Top 10
        }
    
    def _interpret_hhi(self, hhi: float) -> str:
        """Interpret HHI score"""
        if hhi < 0.15:
            return "Low concentration - Healthy diversification"
        elif hhi < 0.25:
            return "Moderate concentration - Monitor largest customers"
        else:
            return "High concentration - Significant customer risk"
    
    def calculate_growth_accounting(self) -> pd.DataFrame:
        """
        Decompose revenue growth into components:
        - New customer acquisition
        - Expansion revenue
        - Contraction
        - Churn
        """
        # Group transactions by year
        yearly_data = []
        
        for year in range(2020, 2025):
            year_revenue = 0
            new_revenue = 0
            expansion_revenue = 0
            contraction_revenue = 0
            
            # Track customers by year
            prior_customers = set()
            current_customers = set()
            
            # Get prior year customers
            if year > 2020:
                for customer, data in self.customer_data['customers'].items():
                    if str(year-1) in data.get('revenue_by_year', {}):
                        prior_customers.add(customer)
            
            # Analyze current year
            for customer, data in self.customer_data['customers'].items():
                yearly_rev = data.get('revenue_by_year', {})
                
                if str(year) in yearly_rev:
                    current_revenue = float(yearly_rev[str(year)])
                    year_revenue += current_revenue
                    current_customers.add(customer)
                    
                    if customer not in prior_customers:
                        # New customer
                        new_revenue += current_revenue
                    else:
                        # Existing customer
                        prior_revenue = float(yearly_rev.get(str(year-1), 0))
                        if current_revenue > prior_revenue:
                            expansion_revenue += (current_revenue - prior_revenue)
                        elif current_revenue < prior_revenue:
                            contraction_revenue += (prior_revenue - current_revenue)
            
            # Calculate churn (customers who had revenue last year but not this year)
            churned_customers = prior_customers - current_customers
            churn_revenue = 0
            for customer in churned_customers:
                prior_rev = self.customer_data['customers'][customer].get('revenue_by_year', {})
                churn_revenue += float(prior_rev.get(str(year-1), 0))
            
            yearly_data.append({
                'year': year,
                'total_revenue': year_revenue,
                'new_revenue': new_revenue,
                'expansion_revenue': expansion_revenue,
                'contraction_revenue': -contraction_revenue,
                'churn_revenue': -churn_revenue,
                'net_revenue_change': new_revenue + expansion_revenue - contraction_revenue - churn_revenue,
                'customer_count': len(current_customers),
                'new_customers': len(current_customers - prior_customers),
                'churned_customers': len(churned_customers)
            })
        
        return pd.DataFrame(yearly_data)
    
    def calculate_ltv_cac_implied(self, 
                                  assumed_gross_margin: float = 0.75,
                                  assumed_cac_months: int = 3) -> Dict[str, float]:
        """
        Calculate implied LTV:CAC ratio using available data
        
        Args:
            assumed_gross_margin: Assumed gross margin (default 75% for SaaS)
            assumed_cac_months: Assumed CAC as months of revenue (default 3)
        
        Returns:
            Dictionary with LTV:CAC metrics
        """
        # Calculate average customer lifetime
        customer_lifetimes = []
        
        for customer, data in self.customer_data['customers'].items():
            first_date = pd.to_datetime(data['first_transaction'])
            last_date = pd.to_datetime(data['last_transaction'])
            lifetime_months = (last_date - first_date).days / 30.44
            
            if lifetime_months > 0:
                customer_lifetimes.append(lifetime_months)
        
        avg_lifetime_months = np.mean(customer_lifetimes) if customer_lifetimes else 12
        
        # Calculate average revenue per customer
        total_revenue = sum(c['total_revenue'] for c in self.customer_data['customers'].values())
        customer_count = self.customer_data['customer_count']
        avg_revenue_per_customer = total_revenue / customer_count if customer_count > 0 else 0
        
        # Calculate monthly revenue
        avg_monthly_revenue = avg_revenue_per_customer / avg_lifetime_months if avg_lifetime_months > 0 else 0
        
        # Calculate LTV
        ltv = avg_monthly_revenue * avg_lifetime_months * assumed_gross_margin
        
        # Calculate implied CAC
        implied_cac = avg_monthly_revenue * assumed_cac_months
        
        # Calculate ratio
        ltv_cac_ratio = ltv / implied_cac if implied_cac > 0 else 0
        
        return {
            'ltv': round(ltv, 2),
            'implied_cac': round(implied_cac, 2),
            'ltv_cac_ratio': round(ltv_cac_ratio, 2),
            'avg_lifetime_months': round(avg_lifetime_months, 1),
            'avg_revenue_per_customer': round(avg_revenue_per_customer, 2),
            'avg_monthly_revenue': round(avg_monthly_revenue, 2),
            'assumed_gross_margin': assumed_gross_margin,
            'assumed_cac_months': assumed_cac_months
        }
    
    def calculate_revenue_quality_score(self) -> Dict[str, float]:
        """
        Calculate revenue quality metrics
        - Recurring vs one-time split
        - Revenue predictability (coefficient of variation)
        - Customer retention patterns
        """
        # Analyze revenue types from customer data
        revenue_by_type = {}
        total_revenue = 0
        
        for customer, data in self.customer_data['customers'].items():
            for rev_type, amount in data.get('revenue_by_type', {}).items():
                revenue_by_type[rev_type] = revenue_by_type.get(rev_type, 0) + amount
                total_revenue += amount
        
        # Calculate recurring percentage
        recurring_revenue = revenue_by_type.get('Sales', 0)  # 'Sales' is recurring/SaaS revenue
        one_time_revenue = revenue_by_type.get('Modeling Fees', 0) + revenue_by_type.get('Other Revenue', 0)
        recurring_percentage = (recurring_revenue / total_revenue * 100) if total_revenue > 0 else 0
        
        # Calculate monthly revenue variability
        monthly_revenue = self.transactions.groupby('month')['amount'].sum()
        revenue_cv = monthly_revenue.std() / monthly_revenue.mean() if len(monthly_revenue) > 1 else 0
        
        # Revenue predictability score (inverse of CV, capped at 100)
        predictability_score = min(100, (1 / (1 + revenue_cv)) * 100) if revenue_cv >= 0 else 100
        
        # Customer repeat rate
        repeat_customers = 0
        for customer, data in self.customer_data['customers'].items():
            if data['transaction_count'] > 1:
                repeat_customers += 1
        
        repeat_rate = (repeat_customers / self.customer_data['customer_count'] * 100) \
                     if self.customer_data['customer_count'] > 0 else 0
        
        # Overall quality score (weighted average)
        quality_score = (
            recurring_percentage * 0.4 +  # 40% weight on recurring revenue
            predictability_score * 0.3 +  # 30% weight on predictability
            repeat_rate * 0.3            # 30% weight on customer retention
        )
        
        return {
            'revenue_quality_score': round(quality_score, 1),
            'recurring_percentage': round(recurring_percentage, 1),
            'one_time_percentage': round((one_time_revenue / total_revenue * 100) if total_revenue > 0 else 0, 1),
            'predictability_score': round(predictability_score, 1),
            'revenue_cv': round(revenue_cv, 3),
            'repeat_customer_rate': round(repeat_rate, 1),
            'total_revenue': round(total_revenue, 2),
            'recurring_revenue': round(recurring_revenue, 2),
            'one_time_revenue': round(one_time_revenue, 2)
        }
    
    def calculate_rule_of_40(self, 
                            year: int = 2024,
                            estimated_profit_margin: float = 0.0) -> Dict[str, float]:
        """
        Calculate Rule of 40 (Growth Rate + Profit Margin >= 40%)
        
        Args:
            year: Year to calculate for
            estimated_profit_margin: Estimated EBITDA margin (default 0)
        
        Returns:
            Dictionary with Rule of 40 metrics
        """
        # Calculate YoY growth rate
        growth_df = self.calculate_growth_accounting()
        
        if year > 2020 and year <= 2024:
            current_year = growth_df[growth_df['year'] == year].iloc[0]
            prior_year = growth_df[growth_df['year'] == year - 1].iloc[0]
            
            growth_rate = ((current_year['total_revenue'] - prior_year['total_revenue']) / 
                          prior_year['total_revenue'] * 100) if prior_year['total_revenue'] > 0 else 0
        else:
            growth_rate = 0
        
        # Calculate Rule of 40
        rule_of_40_score = growth_rate + estimated_profit_margin
        
        # Interpretation
        if rule_of_40_score >= 40:
            interpretation = "Excellent - Meets Rule of 40 benchmark"
        elif rule_of_40_score >= 30:
            interpretation = "Good - Approaching Rule of 40"
        elif rule_of_40_score >= 20:
            interpretation = "Fair - Focus on growth or profitability"
        else:
            interpretation = "Below expectations - Significant improvement needed"
        
        return {
            'year': year,
            'revenue_growth_rate': round(growth_rate, 1),
            'profit_margin': round(estimated_profit_margin, 1),
            'rule_of_40_score': round(rule_of_40_score, 1),
            'interpretation': interpretation,
            'growth_component': round(growth_rate, 1),
            'profit_component': round(estimated_profit_margin, 1)
        }
    
    def generate_valuation_metrics_summary(self) -> Dict:
        """Generate comprehensive summary of all valuation metrics"""
        
        # Calculate all metrics
        ndr_2023 = self.calculate_ndr(2023, 2024)
        concentration = self.calculate_revenue_concentration()
        growth_accounting = self.calculate_growth_accounting()
        ltv_cac = self.calculate_ltv_cac_implied()
        revenue_quality = self.calculate_revenue_quality_score()
        rule_of_40 = self.calculate_rule_of_40()
        
        # Latest year metrics
        latest_year = growth_accounting[growth_accounting['year'] == 2024].iloc[0]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'revenue_metrics': {
                'arr_2024': latest_year['total_revenue'],
                'revenue_growth_2020_2024': round(
                    (latest_year['total_revenue'] / growth_accounting[growth_accounting['year'] == 2020].iloc[0]['total_revenue'] - 1) * 100, 1
                ),
                'customer_count': latest_year['customer_count'],
                'avg_revenue_per_customer': round(latest_year['total_revenue'] / latest_year['customer_count'], 2)
            },
            'saas_health_metrics': {
                'ndr': ndr_2023['ndr'],
                'gross_retention': ndr_2023['gross_retention'],
                'ltv_cac_ratio': ltv_cac['ltv_cac_ratio'],
                'rule_of_40_score': rule_of_40['rule_of_40_score'],
                'revenue_quality_score': revenue_quality['revenue_quality_score']
            },
            'risk_metrics': {
                'customer_concentration_hhi': concentration['hhi'],
                'top_5_customer_share': concentration['top_5_share'],
                'recurring_revenue_percentage': revenue_quality['recurring_percentage']
            },
            'valuation_indicators': {
                'revenue_multiple_range': '5x - 8x ARR',
                'implied_valuation_low': round(latest_year['total_revenue'] * 5, 0),
                'implied_valuation_mid': round(latest_year['total_revenue'] * 6.5, 0),
                'implied_valuation_high': round(latest_year['total_revenue'] * 8, 0)
            }
        }


if __name__ == "__main__":
    # Example usage
    import os
    
    base_path = "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2"
    transactions_path = os.path.join(base_path, "03-Financial-Foundation/data/all_transactions_2020_onwards.json")
    customer_path = os.path.join(base_path, "03-Financial-Foundation/data/revenue/customer_revenue_analysis.json")
    
    calculator = SaaSMetricsCalculator(transactions_path, customer_path)
    
    # Generate summary
    summary = calculator.generate_valuation_metrics_summary()
    print(json.dumps(summary, indent=2))
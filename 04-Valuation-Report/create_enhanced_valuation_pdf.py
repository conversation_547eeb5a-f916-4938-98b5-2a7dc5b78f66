#!/usr/bin/env python3
"""
Enhanced Professional PDF Generator for MCX3D Institutional Valuation Report
Creates a visually stunning, well-structured investment document
"""

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.platypus import Image, KeepTogether, CondPageBreak, Flowable, Frame, PageTemplate
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT, TA_RIGHT
from reportlab.pdfgen import canvas
from reportlab.platypus.doctemplate import BaseDocTemplate
from reportlab.graphics.shapes import Drawing, Rect, Line, Circle
from reportlab.graphics import renderPDF
import re
from datetime import datetime
import os

# Define brand colors
BRAND_COLORS = {
    'primary': colors.HexColor('#1e3a8a'),      # Deep blue
    'secondary': colors.HexColor('#3b82f6'),    # Bright blue
    'accent': colors.HexColor('#10b981'),       # Emerald green
    'warning': colors.HexColor('#f59e0b'),      # Amber
    'text': colors.HexColor('#1f2937'),         # Dark gray
    'light_gray': colors.HexColor('#f3f4f6'),   # Light gray
    'medium_gray': colors.HexColor('#9ca3af'),  # Medium gray
}

class NumberedCanvas(canvas.Canvas):
    """Enhanced canvas with professional headers and footers"""
    def __init__(self, *args, **kwargs):
        canvas.Canvas.__init__(self, *args, **kwargs)
        self._saved_page_states = []
        self.page_num = 0

    def showPage(self):
        self._saved_page_states.append(dict(self.__dict__))
        self._startPage()

    def save(self):
        """Add professional headers and footers to all pages"""
        num_pages = len(self._saved_page_states)
        for state in self._saved_page_states:
            self.__dict__.update(state)
            self.page_num += 1
            if self.page_num > 1:  # Skip header/footer on cover page
                self.draw_header_footer(num_pages)
            canvas.Canvas.showPage(self)
        canvas.Canvas.save(self)

    def draw_header_footer(self, page_count):
        """Draw professional header and footer"""
        # Header
        self.saveState()
        self.setFillColor(BRAND_COLORS['primary'])
        self.setFont("Helvetica", 9)
        
        # Header line
        self.setStrokeColor(BRAND_COLORS['primary'])
        self.setLineWidth(2)
        self.line(inch, letter[1] - 0.6*inch, letter[0] - inch, letter[1] - 0.6*inch)
        
        # Header text
        self.drawString(inch, letter[1] - 0.5*inch, "MCX3D Ltd. - Institutional Valuation Report")
        self.drawRightString(letter[0] - inch, letter[1] - 0.5*inch, "Confidential")
        
        # Footer
        self.setLineWidth(0.5)
        self.setStrokeColor(BRAND_COLORS['medium_gray'])
        self.line(inch, 0.7*inch, letter[0] - inch, 0.7*inch)
        
        # Footer text
        self.setFillColor(BRAND_COLORS['text'])
        self.setFont("Helvetica", 8)
        self.drawString(inch, 0.5*inch, "Series A Investment Round - July 2025")
        
        # Page number with total
        self.drawRightString(letter[0] - inch, 0.5*inch, f"Page {self.page_num} of {page_count}")
        
        # Confidentiality notice
        self.setFont("Helvetica-Oblique", 7)
        self.setFillColor(BRAND_COLORS['medium_gray'])
        confidential_text = "This document contains confidential and proprietary information"
        text_width = self.stringWidth(confidential_text, "Helvetica-Oblique", 7)
        self.drawString((letter[0] - text_width) / 2, 0.35*inch, confidential_text)
        
        self.restoreState()

class SectionDivider(Flowable):
    """Custom flowable for section dividers"""
    def __init__(self, height=0.5*inch):
        Flowable.__init__(self)
        self.height = height
        
    def draw(self):
        self.canv.setStrokeColor(BRAND_COLORS['primary'])
        self.canv.setLineWidth(3)
        self.canv.line(0, self.height/2, 2*inch, self.height/2)

def create_enhanced_cover_page(styles):
    """Create stunning cover page with visual elements"""
    elements = []
    
    # Large spacing at top
    elements.append(Spacer(1, 1.5*inch))
    
    # Company name with custom style
    title_style = ParagraphStyle(
        'CoverTitle',
        fontName='Helvetica-Bold',
        fontSize=42,
        textColor=BRAND_COLORS['primary'],
        alignment=TA_CENTER,
        spaceAfter=20
    )
    
    elements.append(Paragraph("MCX3D Ltd.", title_style))
    
    # Subtitle
    subtitle_style = ParagraphStyle(
        'CoverSubtitle',
        fontName='Helvetica',
        fontSize=24,
        textColor=BRAND_COLORS['secondary'],
        alignment=TA_CENTER,
        spaceAfter=10
    )
    
    elements.append(Paragraph("The AI-Powered 3D Commerce Platform", subtitle_style))
    
    # Visual divider
    elements.append(Spacer(1, 0.5*inch))
    elements.append(SectionDivider())
    elements.append(Spacer(1, 0.5*inch))
    
    # Document type
    doc_type_style = ParagraphStyle(
        'DocType',
        fontName='Helvetica-Bold',
        fontSize=20,
        textColor=BRAND_COLORS['text'],
        alignment=TA_CENTER,
        spaceAfter=80
    )
    
    elements.append(Paragraph("Institutional Valuation Report", doc_type_style))
    
    # Key information in a professional box
    key_info_data = [
        ['', ''],
        ['Series A Investment Round', ''],
        ['', ''],
        ['Seeking Investment:', '£1 Million'],
        ['Pre-Money Valuation:', '$15-30 Million'],
        ['Use of Funds:', 'Product Development & Market Expansion'],
        ['', ''],
    ]
    
    key_info_table = Table(key_info_data, colWidths=[3*inch, 2.5*inch])
    key_info_table.setStyle(TableStyle([
        # Background
        ('BACKGROUND', (0, 0), (-1, -1), BRAND_COLORS['light_gray']),
        
        # Title row
        ('SPAN', (0, 1), (1, 1)),
        ('ALIGN', (0, 1), (1, 1), 'CENTER'),
        ('FONTNAME', (0, 1), (1, 1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 1), (1, 1), 16),
        ('TEXTCOLOR', (0, 1), (1, 1), BRAND_COLORS['primary']),
        
        # Data rows
        ('FONTNAME', (0, 3), (0, 5), 'Helvetica'),
        ('FONTNAME', (1, 3), (1, 5), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 3), (-1, 5), 14),
        ('ALIGN', (0, 3), (0, 5), 'RIGHT'),
        ('ALIGN', (1, 3), (1, 5), 'LEFT'),
        ('TEXTCOLOR', (0, 3), (0, 5), BRAND_COLORS['text']),
        ('TEXTCOLOR', (1, 3), (1, 5), BRAND_COLORS['primary']),
        
        # Padding
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('LEFTPADDING', (0, 0), (-1, -1), 20),
        ('RIGHTPADDING', (0, 0), (-1, -1), 20),
        
        # Border
        ('BOX', (0, 0), (-1, -1), 2, BRAND_COLORS['primary']),
        ('LINEBELOW', (0, 2), (1, 2), 1, BRAND_COLORS['primary']),
    ]))
    
    elements.append(key_info_table)
    
    # Date and confidentiality
    elements.append(Spacer(1, 1*inch))
    
    date_style = ParagraphStyle(
        'Date',
        fontName='Helvetica',
        fontSize=12,
        textColor=BRAND_COLORS['medium_gray'],
        alignment=TA_CENTER,
        spaceAfter=10
    )
    
    elements.append(Paragraph("July 2025", date_style))
    elements.append(Paragraph("Confidential & Proprietary", date_style))
    
    elements.append(PageBreak())
    
    return elements

def create_executive_summary_dashboard(styles):
    """Create visual executive summary dashboard"""
    elements = []
    
    # Title
    exec_title = ParagraphStyle(
        'ExecTitle',
        parent=styles['Heading1'],
        fontSize=28,
        textColor=BRAND_COLORS['primary'],
        spaceAfter=30,
        alignment=TA_LEFT
    )
    
    elements.append(Paragraph("Executive Summary", exec_title))
    
    # Investment thesis in a highlighted box
    thesis_text = """MCX3D represents a compelling Series A investment opportunity at the intersection 
    of AI and 3D commerce. With validated product-market fit, world-class retention metrics, 
    and $2M+ in contracted enterprise revenue, the company is positioned for explosive growth."""
    
    thesis_style = ParagraphStyle(
        'Thesis',
        fontName='Helvetica',
        fontSize=13,
        leading=18,
        textColor=BRAND_COLORS['text'],
        alignment=TA_JUSTIFY
    )
    
    # Create thesis box
    thesis_data = [[Paragraph(thesis_text, thesis_style)]]
    thesis_table = Table(thesis_data, colWidths=[6.5*inch])
    thesis_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), BRAND_COLORS['light_gray']),
        ('BOX', (0, 0), (-1, -1), 2, BRAND_COLORS['secondary']),
        ('TOPPADDING', (0, 0), (-1, -1), 15),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
        ('LEFTPADDING', (0, 0), (-1, -1), 15),
        ('RIGHTPADDING', (0, 0), (-1, -1), 15),
    ]))
    
    elements.append(thesis_table)
    elements.append(Spacer(1, 0.3*inch))
    
    # Key metrics dashboard - 2x2 grid
    metrics_style = ParagraphStyle(
        'MetricValue',
        fontName='Helvetica-Bold',
        fontSize=24,
        textColor=BRAND_COLORS['primary'],
        alignment=TA_CENTER
    )
    
    label_style = ParagraphStyle(
        'MetricLabel',
        fontName='Helvetica',
        fontSize=11,
        textColor=BRAND_COLORS['text'],
        alignment=TA_CENTER
    )
    
    # Create metric cards
    metric_cards = [
        ['2,098%', 'Revenue Growth\n(2020-2024)', '492%', 'Net Dollar\nRetention'],
        ['$2M+', 'Q4 2025\nPipeline', '288', 'Rule of 40\nScore']
    ]
    
    # Build metrics table
    metrics_data = []
    for row in metric_cards:
        row_data = []
        for i in range(0, len(row), 2):
            value = Paragraph(row[i], metrics_style)
            label = Paragraph(row[i+1], label_style)
            row_data.append([value, label])
            if i+2 < len(row):
                row_data.append('')  # Spacer column
                value2 = Paragraph(row[i+2], metrics_style)
                label2 = Paragraph(row[i+3], label_style)
                row_data.append([value2, label2])
        metrics_data.append(row_data)
    
    # Flatten for table
    flat_data = []
    for row in metrics_data:
        values = []
        labels = []
        for cell in row:
            if cell:
                values.append(cell[0])
                labels.append(cell[1])
            else:
                values.append('')
                labels.append('')
        flat_data.append(values)
        flat_data.append(labels)
    
    metrics_table = Table(flat_data, colWidths=[3*inch, 0.5*inch, 3*inch])
    metrics_table.setStyle(TableStyle([
        # Card backgrounds
        ('BACKGROUND', (0, 0), (0, 1), colors.white),
        ('BACKGROUND', (2, 0), (2, 1), colors.white),
        ('BACKGROUND', (0, 2), (0, 3), colors.white),
        ('BACKGROUND', (2, 2), (2, 3), colors.white),
        
        # Borders for cards
        ('BOX', (0, 0), (0, 1), 1, BRAND_COLORS['secondary']),
        ('BOX', (2, 0), (2, 1), 1, BRAND_COLORS['secondary']),
        ('BOX', (0, 2), (0, 3), 1, BRAND_COLORS['secondary']),
        ('BOX', (2, 2), (2, 3), 1, BRAND_COLORS['secondary']),
        
        # Padding
        ('TOPPADDING', (0, 0), (-1, -1), 15),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
        
        # Row heights
        ('ROWHEIGHTS', (0, 0), (-1, -1), 0.5*inch),
    ]))
    
    elements.append(metrics_table)
    elements.append(Spacer(1, 0.3*inch))
    
    # Key highlights
    highlight_title = ParagraphStyle(
        'HighlightTitle',
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=BRAND_COLORS['primary'],
        spaceAfter=10
    )
    
    elements.append(Paragraph("Investment Highlights", highlight_title))
    
    # Bullet points with custom bullets
    bullet_style = ParagraphStyle(
        'BulletPoint',
        fontName='Helvetica',
        fontSize=11,
        leftIndent=20,
        bulletIndent=10,
        textColor=BRAND_COLORS['text'],
        leading=14
    )
    
    highlights = [
        "Proven product-market fit with 22 enterprise clients across 15 industries",
        "World-class SaaS metrics exceeding industry benchmarks",
        "$2M+ contracted pipeline provides immediate growth visibility",
        "Dual revenue model: High-value enterprise + Scalable SaaS platform",
        "Massive $90B addressable market in 3D/AR commerce",
        "Experienced team with deep AI and 3D technology expertise"
    ]
    
    for highlight in highlights:
        elements.append(Paragraph(f"• {highlight}", bullet_style))
    
    elements.append(PageBreak())
    
    return elements

def create_section_header(title, number=None):
    """Create visually appealing section headers"""
    elements = []
    
    # Section divider
    elements.append(SectionDivider())
    elements.append(Spacer(1, 0.2*inch))
    
    # Section title
    section_style = ParagraphStyle(
        'SectionHeader',
        fontName='Helvetica-Bold',
        fontSize=24,
        textColor=BRAND_COLORS['primary'],
        spaceAfter=20
    )
    
    if number:
        title_text = f"{number}. {title}"
    else:
        title_text = title
    
    elements.append(Paragraph(title_text, section_style))
    elements.append(Spacer(1, 0.1*inch))
    
    return elements

def format_content_section(content, styles):
    """Format content with improved typography and spacing"""
    elements = []
    
    # Enhanced paragraph style
    para_style = ParagraphStyle(
        'EnhancedBody',
        fontName='Helvetica',
        fontSize=11,
        leading=16,
        textColor=BRAND_COLORS['text'],
        alignment=TA_JUSTIFY,
        spaceAfter=12
    )
    
    # Process content line by line
    lines = content.split('\n')
    in_table = False
    table_data = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            elements.append(Spacer(1, 0.1*inch))
            continue
        
        # Headers
        if line.startswith('###'):
            sub_style = ParagraphStyle(
                'SubHeader',
                fontName='Helvetica-Bold',
                fontSize=14,
                textColor=BRAND_COLORS['secondary'],
                spaceBefore=10,
                spaceAfter=8
            )
            elements.append(Paragraph(line[3:].strip(), sub_style))
            
        # Bold lines
        elif line.startswith('**') and line.endswith('**'):
            bold_style = ParagraphStyle(
                'BoldText',
                parent=para_style,
                fontName='Helvetica-Bold',
                textColor=BRAND_COLORS['primary']
            )
            elements.append(Paragraph(line[2:-2], bold_style))
            
        # Bullet points
        elif line.startswith('- ') or line.startswith('• '):
            bullet_style = ParagraphStyle(
                'EnhancedBullet',
                parent=para_style,
                leftIndent=25,
                bulletIndent=15,
                bulletFontName='Symbol',
                bulletFontSize=10,
                bulletColor=BRAND_COLORS['accent']
            )
            elements.append(Paragraph(line[2:], bullet_style, bulletText='•'))
            
        # Tables
        elif '|' in line and line.count('|') > 2:
            if not in_table:
                in_table = True
                table_data = []
            
            cells = [cell.strip() for cell in line.split('|') if cell.strip()]
            if not all(c == '-' or c.startswith('-') for c in cells):
                table_data.append(cells)
        
        else:
            # End table if we were in one
            if in_table and table_data:
                table = create_styled_table(table_data)
                elements.append(table)
                elements.append(Spacer(1, 0.2*inch))
                table_data = []
                in_table = False
            
            # Regular paragraph
            elements.append(Paragraph(line, para_style))
    
    # Handle any remaining table
    if in_table and table_data:
        table = create_styled_table(table_data)
        elements.append(table)
        elements.append(Spacer(1, 0.2*inch))
    
    return elements

def create_styled_table(data):
    """Create professionally styled tables"""
    # Calculate column widths
    num_cols = len(data[0]) if data else 0
    col_width = 6.5*inch / num_cols if num_cols > 0 else 6.5*inch
    
    table = Table(data, colWidths=[col_width] * num_cols)
    
    # Professional table styling
    table.setStyle(TableStyle([
        # Header row
        ('BACKGROUND', (0, 0), (-1, 0), BRAND_COLORS['primary']),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 11),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        
        # Data rows
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
        ('TEXTCOLOR', (0, 1), (-1, -1), BRAND_COLORS['text']),
        
        # Alternating row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, BRAND_COLORS['light_gray']]),
        
        # Grid
        ('GRID', (0, 0), (-1, -1), 0.5, BRAND_COLORS['medium_gray']),
        
        # Padding
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 10),
        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
    ]))
    
    return table

def create_enhanced_toc(styles):
    """Create enhanced table of contents"""
    elements = []
    
    # TOC header
    toc_header = ParagraphStyle(
        'TOCHeader',
        fontName='Helvetica-Bold',
        fontSize=24,
        textColor=BRAND_COLORS['primary'],
        spaceAfter=30
    )
    
    elements.append(Paragraph("Table of Contents", toc_header))
    
    # TOC entries with better styling
    toc_entries = [
        ("Executive Summary", "3"),
        ("1. Company Story & Evolution", "5"),
        ("2. Historic Performance Analysis", "10"),
        ("3. Financial Deep Dive", "15"),
        ("4. Market Analysis & Positioning", "20"),
        ("5. Future Growth Strategy", "25"),
        ("6. Valuation Analysis", "30"),
        ("7. Investment Highlights", "35"),
        ("8. Risk Analysis & Mitigation", "37"),
        ("Appendices", "40")
    ]
    
    toc_data = []
    for title, page in toc_entries:
        # Create dotted line effect
        dots = "." * (70 - len(title) - len(page))
        toc_data.append([title, dots, page])
    
    toc_table = Table(toc_data, colWidths=[4*inch, 2*inch, 0.5*inch])
    toc_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('TEXTCOLOR', (0, 0), (0, -1), BRAND_COLORS['text']),
        ('TEXTCOLOR', (1, 0), (1, -1), BRAND_COLORS['medium_gray']),
        ('TEXTCOLOR', (2, 0), (2, -1), BRAND_COLORS['text']),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'CENTER'),
        ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
    ]))
    
    elements.append(toc_table)
    elements.append(PageBreak())
    
    return elements

def integrate_charts_with_captions(section_name, styles):
    """Integrate charts with professional captions"""
    elements = []
    
    chart_configs = {
        'valuation': {
            'file': 'analysis/valuation_scenarios.png',
            'caption': 'Figure 1: MCX3D Valuation Scenarios - Conservative to Aggressive',
            'width': 5.5*inch,
            'height': 3.5*inch
        },
        'comparable': {
            'file': 'analysis/comparable_companies.png', 
            'caption': 'Figure 2: Comparable Company Analysis - 3D/AR Commerce Platforms',
            'width': 6*inch,
            'height': 3.5*inch
        },
        'revenue': {
            'file': 'analysis/revenue_growth_to_exit.png',
            'caption': 'Figure 3: Revenue Growth Trajectory to Exit (2024-2029)',
            'width': 6*inch,
            'height': 3.5*inch
        },
        'returns': {
            'file': 'analysis/investment_return_analysis.png',
            'caption': 'Figure 4: Investment Return Analysis - Historical & Projected',
            'width': 5.5*inch,
            'height': 4.5*inch
        }
    }
    
    # Determine which chart to show
    chart_key = None
    if 'valuation' in section_name.lower():
        chart_key = 'valuation'
    elif 'comparable' in section_name.lower() or 'market' in section_name.lower():
        chart_key = 'comparable'
    elif 'growth' in section_name.lower() or 'future' in section_name.lower():
        chart_key = 'revenue'
    elif 'investment' in section_name.lower() or 'highlight' in section_name.lower():
        chart_key = 'returns'
    
    if chart_key and chart_key in chart_configs:
        config = chart_configs[chart_key]
        if os.path.exists(config['file']):
            # Add some spacing
            elements.append(Spacer(1, 0.2*inch))
            
            # Add the image
            img = Image(config['file'], width=config['width'], height=config['height'])
            elements.append(img)
            
            # Add caption
            caption_style = ParagraphStyle(
                'Caption',
                fontName='Helvetica-Oblique',
                fontSize=10,
                textColor=BRAND_COLORS['medium_gray'],
                alignment=TA_CENTER,
                spaceBefore=5,
                spaceAfter=15
            )
            elements.append(Paragraph(config['caption'], caption_style))
            elements.append(Spacer(1, 0.2*inch))
    
    return elements

def create_enhanced_pdf():
    """Main function to create the enhanced PDF"""
    
    # Setup document
    pdf_filename = "MCX3D_Institutional_Valuation_Report_Enhanced.pdf"
    doc = SimpleDocTemplate(
        pdf_filename,
        pagesize=letter,
        rightMargin=0.75*inch,
        leftMargin=0.75*inch,
        topMargin=1*inch,
        bottomMargin=1*inch
    )
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Container for all elements
    elements = []
    
    # 1. Cover Page
    elements.extend(create_enhanced_cover_page(styles))
    
    # 2. Table of Contents
    elements.extend(create_enhanced_toc(styles))
    
    # 3. Executive Summary Dashboard
    elements.extend(create_executive_summary_dashboard(styles))
    
    # 4. Read and process main content
    md_file = "/Users/<USER>/Documents/GitHub/mcx3d_financials/mcx3d_financials_v2/04-Valuation-Report/MCX3D_Institutional_Valuation_Report.md"
    
    with open(md_file, 'r') as f:
        content = f.read()
    
    # Split into major sections
    sections = re.split(r'^#{1,2}\s+', content, flags=re.MULTILINE)
    
    # Process each section
    section_configs = [
        ("Company Story & Evolution", 1),
        ("Historic Performance Analysis", 2),
        ("Financial Deep Dive", 3),
        ("Market Analysis & Positioning", 4),
        ("Future Growth Strategy", 5),
        ("Valuation Analysis", 6),
        ("Investment Highlights", 7),
        ("Risk Analysis & Mitigation", 8)
    ]
    
    section_counter = 0
    for i, section in enumerate(sections[4:]):  # Skip title and exec summary
        if section.strip() and section_counter < len(section_configs):
            title, number = section_configs[section_counter]
            
            # Add section header
            elements.extend(create_section_header(title, number))
            
            # Add section content
            elements.extend(format_content_section(section, styles))
            
            # Add relevant charts
            elements.extend(integrate_charts_with_captions(title, styles))
            
            # Page break after major sections
            if section_counter in [1, 3, 5, 6]:
                elements.append(PageBreak())
            
            section_counter += 1
    
    # Build PDF with custom canvas
    doc.build(elements, canvasmaker=NumberedCanvas)
    
    print(f"✅ Enhanced PDF created: {pdf_filename}")
    print(f"📊 Professional formatting applied with:")
    print("   - Visual executive summary dashboard")
    print("   - Color-coded sections and headers")
    print("   - Professional tables and charts")
    print("   - Consistent branding throughout")
    
    return pdf_filename

if __name__ == "__main__":
    create_enhanced_pdf()
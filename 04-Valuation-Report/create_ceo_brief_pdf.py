#!/usr/bin/env python3
"""
Enhanced PDF Generator for MCX3D CEO Strategic Brief
"""

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT
import re
import os

# Define brand colors
BRAND_COLORS = {
    'primary': colors.HexColor('#1e3a8a'),
    'secondary': colors.HexColor('#3b82f6'),
    'text': colors.HexColor('#1f2937'),
    'light_gray': colors.HexColor('#f3f4f6'),
    'medium_gray': colors.HexColor('#9ca3af'),
}

def create_styled_table(data):
    """Create professionally styled tables"""
    if not data or not data[0]:
        return None
    
    num_cols = len(data[0])
    col_widths = [6.5 * inch / num_cols] * num_cols
    
    table = Table(data, colWidths=col_widths)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), BRAND_COLORS['primary']),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, BRAND_COLORS['light_gray']]),
        ('GRID', (0, 0), (-1, -1), 1, BRAND_COLORS['medium_gray']),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
    ]))
    return table

def create_pdf_from_markdown(md_file, pdf_file):
    """
    Converts a markdown file to a well-formatted PDF with embedded images and tables.
    """
    doc = SimpleDocTemplate(pdf_file, pagesize=letter, topMargin=0.5*inch, bottomMargin=0.5*inch, leftMargin=0.75*inch, rightMargin=0.75*inch)
    story = []
    styles = getSampleStyleSheet()

    # Custom styles
    title_style = ParagraphStyle('TitleStyle', fontName='Helvetica-Bold', fontSize=24, textColor=BRAND_COLORS['primary'], spaceAfter=18, alignment=TA_LEFT)
    h2_style = ParagraphStyle('H2Style', fontName='Helvetica-Bold', fontSize=18, textColor=BRAND_COLORS['primary'], spaceBefore=12, spaceAfter=12)
    h3_style = ParagraphStyle('H3Style', fontName='Helvetica-Bold', fontSize=14, textColor=BRAND_COLORS['secondary'], spaceBefore=10, spaceAfter=10)
    body_style = ParagraphStyle('BodyStyle', fontName='Helvetica', fontSize=10, leading=14, alignment=TA_JUSTIFY)
    bullet_style = ParagraphStyle('BulletStyle', parent=body_style, leftIndent=20, bulletIndent=10)
    code_style = ParagraphStyle('CodeStyle', fontName='Courier', fontSize=9, textColor=colors.darkred, backColor=BRAND_COLORS['light_gray'], padding=5, borderPadding=5, borderRadius=2)
    caption_style = ParagraphStyle('CaptionStyle', fontName='Helvetica-Oblique', fontSize=9, textColor=BRAND_COLORS['medium_gray'], alignment=TA_CENTER, spaceBefore=6)

    with open(md_file, 'r') as f:
        content = f.read()

    lines = content.split('\n')
    in_table = False
    table_data = []
    in_code_block = False
    code_block = ""

    for line in lines:
        stripped_line = line.strip()

        if stripped_line.startswith('```'):
            if in_code_block:
                story.append(Paragraph(code_block.strip(), code_style))
                story.append(Spacer(1, 0.1*inch))
                in_code_block = False
                code_block = ""
            else:
                in_code_block = True
            continue

        if in_code_block:
            code_block += line + '\n'
            continue

        if stripped_line.startswith('|') and stripped_line.endswith('|'):
            if '---' not in stripped_line:
                in_table = True
                cells = [cell.strip() for cell in stripped_line.split('|')][1:-1]
                table_data.append(cells)
            continue
        
        if in_table:
            table = create_styled_table(table_data)
            if table:
                story.append(table)
                story.append(Spacer(1, 0.2*inch))
            in_table = False
            table_data = []

        if stripped_line.startswith('# '):
            story.append(Paragraph(stripped_line[2:], title_style))
        elif stripped_line.startswith('## '):
            story.append(Paragraph(stripped_line[3:], h2_style))
        elif stripped_line.startswith('### '):
            story.append(Paragraph(stripped_line[4:], h3_style))
        elif stripped_line.startswith('* '):
            story.append(Paragraph(stripped_line[2:], bullet_style, bulletText='•'))
        elif stripped_line.startswith('✅ '):
            story.append(Paragraph(stripped_line, bullet_style, bulletText='✅'))
        elif stripped_line.startswith('---'):
            story.append(Spacer(1, 0.25*inch))
        elif stripped_line.startswith('![') and '](' in stripped_line:
            img_path_match = re.search(r'\((.*?)\)', stripped_line)
            caption_match = re.search(r'\[(.*?)\]', stripped_line)
            if img_path_match:
                img_path = os.path.join(os.path.dirname(md_file), img_path_match.group(1))
                if os.path.exists(img_path):
                    img = Image(img_path, width=5*inch, height=3*inch)
                    img.hAlign = 'CENTER'
                    story.append(img)
                    if caption_match:
                        story.append(Paragraph(caption_match.group(1), caption_style))
                    story.append(Spacer(1, 0.2*inch))
                else:
                    story.append(Paragraph(f"[Image not found: {img_path}]", body_style))
        elif stripped_line:
            story.append(Paragraph(stripped_line, body_style))

    doc.build(story)
    print(f"Enhanced PDF created: {pdf_file}")

if __name__ == "__main__":
    md_input = "04-Valuation-Report/MCX3D_CEO_Strategic_Brief.md"
    pdf_output = "04-Valuation-Report/MCX3D_CEO_Strategic_Brief.pdf"
    create_pdf_from_markdown(md_input, pdf_output)

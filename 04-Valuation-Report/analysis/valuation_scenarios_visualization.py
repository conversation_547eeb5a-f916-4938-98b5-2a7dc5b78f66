#!/usr/bin/env python3
"""
MCX3D Valuation Scenarios Visualization
Creates charts to support the institutional valuation report
"""

import json
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
import pandas as pd
import seaborn as sns
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Set style
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

def create_valuation_scenarios():
    """Create valuation scenario comparison chart"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    scenarios = ['Conservative\n($15M)', 'Base Case\n($22M)', 'Aggressive\n($30M)']
    valuations = [15, 22, 30]
    multiples = [7, 10, 13.5]
    
    x = np.arange(len(scenarios))
    width = 0.6
    
    # Create bars with gradient effect
    bars = ax.bar(x, valuations, width, alpha=0.8)
    
    # Color bars differently
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    for bar, color in zip(bars, colors):
        bar.set_color(color)
    
    # Add value labels on bars
    for i, (val, mult) in enumerate(zip(valuations, multiples)):
        ax.text(i, val + 0.5, f'${val}M', ha='center', va='bottom', fontsize=14, fontweight='bold')
        ax.text(i, val/2, f'{mult}x\nARR', ha='center', va='center', fontsize=11, color='white', fontweight='bold')
    
    # Styling
    ax.set_ylabel('Valuation ($ Millions)', fontsize=12, fontweight='bold')
    ax.set_title('MCX3D Valuation Scenarios\nBased on Forward ARR Multiple Analysis', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(scenarios, fontsize=12)
    ax.set_ylim(0, 35)
    
    # Add grid
    ax.grid(True, axis='y', alpha=0.3)
    ax.set_axisbelow(True)
    
    # Add reference line for current valuation
    current_val = 2.1 * 1.25  # £2.1M in USD
    ax.axhline(y=current_val, color='red', linestyle='--', alpha=0.5, label=f'Current Valuation (~${current_val:.1f}M)')
    
    ax.legend(loc='upper left')
    
    plt.tight_layout()
    plt.savefig('valuation_scenarios.png', dpi=300, bbox_inches='tight')
    plt.savefig('valuation_scenarios.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ Created valuation scenarios chart")

def create_comparable_companies_analysis():
    """Create comparable companies valuation chart"""
    fig, ax = plt.subplots(figsize=(14, 8))
    
    companies = ['ThreeKit', 'Avataar', 'VNTANA', 'Cylindo', 'MCX3D\n(Base Case)']
    valuations = [150, 228, 60, 40, 22]
    multiples = [15, 20, 10, 8, 10]
    growth_rates = ['3x', 'High', '2x', 'Stable', '8.4x']
    
    x = np.arange(len(companies))
    width = 0.7
    
    # Create bars
    bars = ax.bar(x, valuations, width, alpha=0.8)
    
    # Color MCX3D differently
    colors = ['#95A5A6'] * 4 + ['#3498DB']
    for bar, color in zip(bars, colors):
        bar.set_color(color)
    
    # Add labels
    for i, (val, mult, growth) in enumerate(zip(valuations, multiples, growth_rates)):
        ax.text(i, val + 3, f'${val}M', ha='center', va='bottom', fontsize=12, fontweight='bold')
        ax.text(i, val/2, f'{mult}x\nMultiple', ha='center', va='center', fontsize=10, color='white', fontweight='bold')
        ax.text(i, -8, f'Growth: {growth}', ha='center', va='top', fontsize=9, style='italic')
    
    # Styling
    ax.set_ylabel('Valuation ($ Millions)', fontsize=12, fontweight='bold')
    ax.set_title('Comparable Company Valuations\n3D/AR Commerce Platform Companies', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(companies, fontsize=11)
    ax.set_ylim(-15, 250)
    
    # Add average line
    avg_val = np.mean(valuations[:-1])
    ax.axhline(y=avg_val, color='green', linestyle='--', alpha=0.5, label=f'Peer Average: ${avg_val:.0f}M')
    
    ax.legend()
    ax.grid(True, axis='y', alpha=0.3)
    ax.set_axisbelow(True)
    
    plt.tight_layout()
    plt.savefig('comparable_companies.png', dpi=300, bbox_inches='tight')
    plt.savefig('comparable_companies.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ Created comparable companies analysis")

def create_revenue_growth_to_exit():
    """Create revenue growth trajectory to exit chart"""
    fig, ax = plt.subplots(figsize=(14, 8))
    
    years = ['2024\n(Current)', '2025\n(Pipeline)', '2026\n(Target)', '2027', '2028', '2029\n(Exit)']
    revenue = [0.328, 2.2, 4.5, 9.0, 18.0, 36.0]  # In millions
    valuation = [2.1, 22, 45, 90, 180, 360]  # Assuming 10x multiple
    
    x = np.arange(len(years))
    
    # Create figure with secondary y-axis
    fig, ax1 = plt.subplots(figsize=(14, 8))
    ax2 = ax1.twinx()
    
    # Plot revenue bars
    bars = ax1.bar(x - 0.2, revenue, 0.4, label='ARR ($M)', color='#2ECC71', alpha=0.8)
    
    # Plot valuation line
    line = ax2.plot(x, valuation, 'o-', color='#E74C3C', linewidth=3, markersize=10, label='Valuation ($M)')
    
    # Add value labels
    for i, (rev, val) in enumerate(zip(revenue, valuation)):
        ax1.text(i - 0.2, rev + 0.5, f'${rev:.1f}M', ha='center', va='bottom', fontsize=10)
        ax2.text(i + 0.05, val + 5, f'${val:.0f}M', ha='left', va='bottom', fontsize=10, color='#E74C3C')
    
    # Styling
    ax1.set_xlabel('Year', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Annual Recurring Revenue ($M)', fontsize=12, fontweight='bold', color='#2ECC71')
    ax2.set_ylabel('Valuation at 10x Multiple ($M)', fontsize=12, fontweight='bold', color='#E74C3C')
    ax1.set_title('MCX3D Growth Trajectory to Exit\nRevenue and Valuation Projections', fontsize=16, fontweight='bold', pad=20)
    
    ax1.set_xticks(x)
    ax1.set_xticklabels(years)
    ax1.tick_params(axis='y', labelcolor='#2ECC71')
    ax2.tick_params(axis='y', labelcolor='#E74C3C')
    
    # Add grid
    ax1.grid(True, alpha=0.3)
    ax1.set_axisbelow(True)
    
    # Combine legends
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.tight_layout()
    plt.savefig('revenue_growth_to_exit.png', dpi=300, bbox_inches='tight')
    plt.savefig('revenue_growth_to_exit.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ Created revenue growth to exit chart")

def create_investment_return_analysis():
    """Create investment return analysis chart"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Chart 1: Historical Investment Efficiency
    years = ['2020', '2021', '2022', '2023', '2024']
    investment = [18.5, 76.2, 191.5, 47.2, 5.5]
    revenue = [11.9, 8.9, 15.9, 67.6, 262.5]
    cumulative_inv = [16.3, 92.6, 272.1, 312.5, 308.2]
    cumulative_rev = [11.9, 20.8, 36.8, 104.4, 366.9]
    
    x = np.arange(len(years))
    width = 0.35
    
    # Plot investment and revenue bars
    bars1 = ax1.bar(x - width/2, investment, width, label='Annual Investment', color='#E74C3C', alpha=0.8)
    bars2 = ax1.bar(x + width/2, revenue, width, label='Annual Revenue', color='#2ECC71', alpha=0.8)
    
    # Add cumulative line
    ax1_twin = ax1.twinx()
    ax1_twin.plot(x, np.array(cumulative_rev)/np.array(cumulative_inv) * 100, 'o-', color='#3498DB', linewidth=3, markersize=8, label='Revenue/Investment %')
    
    ax1.set_xlabel('Year', fontsize=11)
    ax1.set_ylabel('Amount (£000s)', fontsize=11)
    ax1.set_title('Historical Investment Efficiency\n£341K Investment → £367K Revenue (108% Return)', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(years)
    ax1.legend(loc='upper left')
    ax1_twin.set_ylabel('Revenue/Investment Ratio (%)', fontsize=11)
    ax1_twin.legend(loc='upper right')
    
    # Chart 2: Return Scenarios
    scenarios = ['Conservative\n(5x in 5yr)', 'Base Case\n(10x in 5yr)', 'Aggressive\n(20x in 5yr)']
    returns = [5, 10, 20]
    irr = [38, 58, 82]  # Approximate IRRs
    
    x2 = np.arange(len(scenarios))
    bars3 = ax2.bar(x2, returns, 0.6, alpha=0.8)
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    for bar, color, ret, i in zip(bars3, colors, returns, irr):
        bar.set_color(color)
        ax2.text(bar.get_x() + bar.get_width()/2, ret + 0.5, f'{ret}x', ha='center', va='bottom', fontsize=12, fontweight='bold')
        ax2.text(bar.get_x() + bar.get_width()/2, ret/2, f'~{i}%\nIRR', ha='center', va='center', fontsize=10, color='white', fontweight='bold')
    
    ax2.set_ylabel('Return Multiple', fontsize=11)
    ax2.set_title('Projected Investor Returns\nSeries A Investment at $22M Valuation', fontsize=14, fontweight='bold')
    ax2.set_xticks(x2)
    ax2.set_xticklabels(scenarios)
    ax2.set_ylim(0, 25)
    ax2.grid(True, axis='y', alpha=0.3)
    ax2.set_axisbelow(True)
    
    plt.tight_layout()
    plt.savefig('investment_return_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('investment_return_analysis.pdf', bbox_inches='tight')
    plt.close()
    
    print("✅ Created investment return analysis")

def create_interactive_valuation_dashboard():
    """Create interactive Plotly dashboard for valuation analysis"""
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Valuation Methods Comparison', 'Revenue Growth Trajectory',
                       'Comparable Companies', 'Risk-Adjusted Returns'),
        specs=[[{"type": "bar"}, {"type": "scatter"}],
               [{"type": "bar"}, {"type": "pie"}]]
    )
    
    # 1. Valuation Methods
    methods = ['Revenue<br>Multiple', 'DCF', 'VC Method', 'Berkus']
    values = [24, 22.4, 22.5, 9.5]
    weights = [40, 30, 20, 10]
    
    fig.add_trace(
        go.Bar(x=methods, y=values, name='Valuation ($M)',
               marker_color=['#3498DB', '#2ECC71', '#E74C3C', '#F39C12'],
               text=[f'${v}M<br>{w}% weight' for v, w in zip(values, weights)],
               textposition='auto'),
        row=1, col=1
    )
    
    # 2. Revenue Growth
    years = [2024, 2025, 2026, 2027, 2028]
    revenue = [0.328, 2.2, 4.5, 9.0, 18.0]
    
    fig.add_trace(
        go.Scatter(x=years, y=revenue, mode='lines+markers',
                  name='ARR ($M)', line=dict(color='#2ECC71', width=3),
                  marker=dict(size=10)),
        row=1, col=2
    )
    
    # 3. Comparable Companies
    companies = ['ThreeKit', 'Avataar', 'VNTANA', 'MCX3D']
    comp_vals = [150, 228, 60, 22]
    
    fig.add_trace(
        go.Bar(x=companies, y=comp_vals, name='Valuation',
               marker_color=['gray', 'gray', 'gray', '#3498DB']),
        row=2, col=1
    )
    
    # 4. Risk-Adjusted Returns
    labels = ['Massive Success<br>(25x)', 'Strong Success<br>(10x)', 
              'Moderate<br>(2.5x)', 'Survival<br>(1x)', 'Failure<br>(0x)']
    sizes = [20, 40, 25, 10, 5]
    
    fig.add_trace(
        go.Pie(labels=labels, values=sizes, hole=.3,
               marker_colors=['#2ECC71', '#3498DB', '#F39C12', '#95A5A6', '#E74C3C']),
        row=2, col=2
    )
    
    # Update layout
    fig.update_layout(
        title_text="MCX3D Valuation Analysis Dashboard",
        title_font_size=20,
        showlegend=False,
        height=800
    )
    
    # Update axes
    fig.update_yaxes(title_text="Valuation ($M)", row=1, col=1)
    fig.update_yaxes(title_text="ARR ($M)", row=1, col=2)
    fig.update_yaxes(title_text="Valuation ($M)", row=2, col=1)
    
    # Save
    fig.write_html('valuation_dashboard.html')
    print("✅ Created interactive valuation dashboard")

def main():
    """Generate all valuation visualizations"""
    print("Generating MCX3D valuation visualizations...")
    
    # Create all charts
    create_valuation_scenarios()
    create_comparable_companies_analysis()
    create_revenue_growth_to_exit()
    create_investment_return_analysis()
    create_interactive_valuation_dashboard()
    
    print("\n✨ All valuation visualizations created successfully!")
    print("Files generated:")
    print("  - valuation_scenarios.png/pdf")
    print("  - comparable_companies.png/pdf")
    print("  - revenue_growth_to_exit.png/pdf")
    print("  - investment_return_analysis.png/pdf")
    print("  - valuation_dashboard.html")

if __name__ == "__main__":
    main()
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MCX3D Historic Growth Analysis\n", "## Forensic Financial Analysis for Valuation Report\n", "\n", "This notebook provides CFO-level analysis of MCX3D's historic performance from 2020-2025."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import json\n", "import sys\n", "import os\n", "from datetime import datetime\n", "\n", "# Add src to path\n", "sys.path.append('../src')\n", "from valuation_metrics import SaaSMetricsCalculator\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', lambda x: '%.2f' % x)\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data paths\n", "base_path = \"../../03-Financial-Foundation/data\"\n", "transactions_path = os.path.join(base_path, \"all_transactions_2020_onwards.json\")\n", "customer_path = os.path.join(base_path, \"revenue/customer_revenue_analysis.json\")\n", "\n", "# Initialize calculator\n", "metrics = SaaSMetricsCalculator(transactions_path, customer_path)\n", "\n", "# Generate comprehensive metrics summary\n", "valuation_summary = metrics.generate_valuation_metrics_summary()\n", "\n", "print(\"MCX3D Valuation Metrics Summary\")\n", "print(\"=\" * 50)\n", "print(json.dumps(valuation_summary, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Revenue Growth Decomposition\n", "\n", "Breaking down revenue growth into its core components is crucial for understanding the quality and sustainability of growth."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get growth accounting data\n", "growth_accounting = metrics.calculate_growth_accounting()\n", "\n", "# Display growth accounting table\n", "print(\"Revenue Growth Accounting (2020-2024)\")\n", "print(\"=\" * 80)\n", "display(growth_accounting[['year', 'total_revenue', 'new_revenue', 'expansion_revenue', \n", "                          'contraction_revenue', 'churn_revenue', 'net_revenue_change']])\n", "\n", "# Calculate growth rates\n", "growth_accounting['yoy_growth'] = growth_accounting['total_revenue'].pct_change() * 100\n", "growth_accounting['new_customer_contribution'] = (growth_accounting['new_revenue'] / \n", "                                                  growth_accounting['total_revenue'].shift(1) * 100)\n", "growth_accounting['expansion_contribution'] = (growth_accounting['expansion_revenue'] / \n", "                                              growth_accounting['total_revenue'].shift(1) * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create revenue waterfall chart\n", "fig = make_subplots(rows=2, cols=2,\n", "                    subplot_titles=('Revenue Growth Waterfall', 'Growth Rate Decomposition',\n", "                                   'Customer Count Evolution', 'Revenue per Customer Trend'),\n", "                    specs=[[{'type': 'bar'}, {'type': 'bar'}],\n", "                           [{'type': 'scatter'}, {'type': 'scatter'}]])\n", "\n", "# Waterfall chart data\n", "years = growth_accounting['year'].tolist()[1:]  # Skip first year\n", "categories = []\n", "values = []\n", "\n", "for idx, year in enumerate(years):\n", "    row = growth_accounting[growth_accounting['year'] == year].iloc[0]\n", "    categories.extend([f'{year} Start', f'{year} New', f'{year} Expansion', \n", "                      f'{year} Contraction', f'{year} Churn'])\n", "    \n", "    prior_revenue = growth_accounting[growth_accounting['year'] == year-1].iloc[0]['total_revenue']\n", "    values.extend([prior_revenue, row['new_revenue'], row['expansion_revenue'],\n", "                  row['contraction_revenue'], row['churn_revenue']])\n", "\n", "# Add waterfall\n", "fig.add_trace(\n", "    go.Waterfall(\n", "        x=categories,\n", "        y=values,\n", "        connector={\"line\": {\"color\": \"rgb(63, 63, 63)\"}},\n", "        increasing={\"marker\": {\"color\": \"green\"}},\n", "        decreasing={\"marker\": {\"color\": \"red\"}},\n", "        totals={\"marker\": {\"color\": \"blue\"}}\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Growth rate decomposition\n", "fig.add_trace(\n", "    go.Bar(x=years, y=growth_accounting['new_customer_contribution'][1:].tolist(),\n", "           name='New Customers', marker_color='lightblue'),\n", "    row=1, col=2\n", ")\n", "fig.add_trace(\n", "    go.Bar(x=years, y=growth_accounting['expansion_contribution'][1:].tolist(),\n", "           name='Expansion', marker_color='darkblue'),\n", "    row=1, col=2\n", ")\n", "\n", "# Customer count evolution\n", "fig.add_trace(\n", "    go.<PERSON>er(x=growth_accounting['year'], y=growth_accounting['customer_count'],\n", "               mode='lines+markers', name='Total Customers',\n", "               line=dict(color='purple', width=3)),\n", "    row=2, col=1\n", ")\n", "\n", "# Revenue per customer\n", "revenue_per_customer = growth_accounting['total_revenue'] / growth_accounting['customer_count']\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=growth_accounting['year'], y=revenue_per_customer,\n", "               mode='lines+markers', name='Revenue per Customer',\n", "               line=dict(color='green', width=3)),\n", "    row=2, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=800, showlegend=True,\n", "                  title_text=\"MCX3D Revenue Growth Analysis (2020-2024)\",\n", "                  title_font_size=20)\n", "fig.update_xaxes(tickangle=-45)\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Net Dollar Retention (NDR) Analysis\n", "\n", "NDR is the most critical SaaS metric for valuation. Best-in-class B2B SaaS companies achieve 110%+ NDR."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate NDR for each cohort\n", "cohort_analysis = []\n", "\n", "for cohort_year in range(2020, 2024):\n", "    for measure_year in range(cohort_year + 1, 2025):\n", "        ndr_data = metrics.calculate_ndr(cohort_year, measure_year)\n", "        ndr_data['cohort_year'] = cohort_year\n", "        ndr_data['measure_year'] = measure_year\n", "        ndr_data['years_since_cohort'] = measure_year - cohort_year\n", "        cohort_analysis.append(ndr_data)\n", "\n", "cohort_df = pd.DataFrame(cohort_analysis)\n", "\n", "# Display NDR summary\n", "print(\"Net Dollar Retention by Co<PERSON><PERSON>\")\n", "print(\"=\" * 60)\n", "ndr_pivot = cohort_df.pivot(index='cohort_year', columns='measure_year', values='ndr')\n", "display(ndr_pivot.style.background_gradient(cmap='RdYlGn', vmin=80, vmax=120))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create NDR visualization\n", "fig = make_subplots(rows=1, cols=2,\n", "                    subplot_titles=('NDR Trends by Cohort', 'NDR Components Analysis'))\n", "\n", "# NDR trends by cohort\n", "for cohort in cohort_df['cohort_year'].unique():\n", "    cohort_data = cohort_df[cohort_df['cohort_year'] == cohort]\n", "    fig.add_trace(\n", "        go.<PERSON>er(x=cohort_data['measure_year'], y=cohort_data['ndr'],\n", "                   mode='lines+markers', name=f'{cohort} Cohort',\n", "                   line=dict(width=3)),\n", "        row=1, col=1\n", "    )\n", "\n", "# Add 100% reference line\n", "fig.add_hline(y=100, line_dash=\"dash\", line_color=\"gray\", \n", "              annotation_text=\"100% Retention\", row=1, col=1)\n", "fig.add_hline(y=110, line_dash=\"dash\", line_color=\"green\", \n", "              annotation_text=\"Best-in-class (110%)\", row=1, col=1)\n", "\n", "# NDR components for latest cohort\n", "latest_cohort = cohort_df[cohort_df['cohort_year'] == 2023].iloc[-1]\n", "components = ['Gross Retention', 'Net Expansion']\n", "values = [latest_cohort['gross_retention'], latest_cohort['net_expansion']]\n", "\n", "fig.add_trace(\n", "    go.Bar(x=components, y=values, text=values,\n", "           texttemplate='%{text:.1f}%', textposition='outside',\n", "           marker_color=['lightcoral', 'lightgreen']),\n", "    row=1, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=400, title_text=\"Net Dollar Retention Analysis\",\n", "                  title_font_size=18)\n", "fig.update_yaxes(title_text=\"NDR %\", row=1, col=1)\n", "fig.update_yaxes(title_text=\"Percentage\", row=1, col=2)\n", "\n", "fig.show()\n", "\n", "# Print interpretation\n", "print(f\"\\n📊 NDR Insights:\")\n", "print(f\"- Latest NDR (2023 cohort): {latest_cohort['ndr']:.1f}%\")\n", "print(f\"- Gross Retention: {latest_cohort['gross_retention']:.1f}%\")\n", "print(f\"- Net Expansion: {latest_cohort['net_expansion']:.1f}%\")\n", "print(f\"\\n💡 Interpretation: \", end=\"\")\n", "if latest_cohort['ndr'] >= 110:\n", "    print(\"Excellent - Best-in-class retention with strong expansion\")\n", "elif latest_cohort['ndr'] >= 100:\n", "    print(\"Good - Positive net retention, approaching best-in-class\")\n", "else:\n", "    print(\"Needs improvement - Focus on reducing churn and driving expansion\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Customer Concentration Risk Analysis\n", "\n", "Understanding customer concentration is critical for risk assessment and valuation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate concentration metrics\n", "concentration = metrics.calculate_revenue_concentration()\n", "\n", "print(\"Customer Concentration Analysis\")\n", "print(\"=\" * 50)\n", "print(f\"HHI Score: {concentration['hhi']:.4f}\")\n", "print(f\"Interpretation: {concentration['hhi_interpretation']}\")\n", "print(f\"\\nTop Customer: {concentration['top_1_customer']} ({concentration['top_1_share']:.1f}% of revenue)\")\n", "print(f\"Top 5 Customers: {concentration['top_5_share']:.1f}% of revenue\")\n", "print(f\"Top 10 Customers: {concentration['top_10_share']:.1f}% of revenue\")\n", "\n", "# Visualize concentration\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))\n", "\n", "# Pareto chart\n", "revenue_dist = concentration['revenue_distribution']\n", "customers = [item[0] for item in revenue_dist]\n", "shares = [item[1] * 100 for item in revenue_dist]\n", "cumulative = np.cumsum(shares)\n", "\n", "ax1.bar(range(len(customers)), shares, color='skyblue')\n", "ax1_twin = ax1.twinx()\n", "ax1_twin.plot(range(len(customers)), cumulative, color='red', marker='o', linewidth=2)\n", "ax1.set_xlabel('Customer Rank')\n", "ax1.set_ylabel('Revenue Share (%)', color='blue')\n", "ax1_twin.set_ylabel('Cumulative Share (%)', color='red')\n", "ax1.set_title('Customer Revenue Concentration (Pareto Chart)')\n", "ax1.set_xticks(range(len(customers)))\n", "ax1.set_xticklabels([f'#{i+1}' for i in range(len(customers))], rotation=45)\n", "\n", "# HHI visualization\n", "hhi_ranges = [0, 0.15, 0.25, 1.0]\n", "hhi_labels = ['Low Risk\\n(Well Diversified)', 'Moderate Risk\\n(Monitor)', 'High Risk\\n(Concentrated)']\n", "hhi_colors = ['green', 'yellow', 'red']\n", "\n", "for i in range(len(hhi_ranges)-1):\n", "    ax2.axhspan(hhi_ranges[i], hhi_ranges[i+1], alpha=0.3, color=hhi_colors[i])\n", "    ax2.text(0.5, (hhi_ranges[i] + hhi_ranges[i+1])/2, hhi_labels[i], \n", "             ha='center', va='center', fontsize=10, weight='bold')\n", "\n", "ax2.axhline(concentration['hhi'], color='black', linewidth=3, label=f\"MCX3D HHI: {concentration['hhi']:.3f}\")\n", "ax2.set_ylim(0, 0.4)\n", "ax2.set_xlim(0, 1)\n", "ax2.set_ylabel('HHI Score')\n", "ax2.set_title('Customer Concentration Risk Assessment')\n", "ax2.legend()\n", "ax2.set_xticks([])\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Revenue Quality & Predictability Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "# Calculate revenue quality metrics\nrevenue_quality = metrics.calculate_revenue_quality_score()\n\nprint(\"Revenue Quality Assessment\")\nprint(\"=\" * 50)\nprint(f\"Overall Revenue Quality Score: {revenue_quality['revenue_quality_score']:.1f}/100\")\nprint(f\"\\nComponents:\")\nprint(f\"- Recurring Revenue: {revenue_quality['recurring_percentage']:.1f}%\")\nprint(f\"- Predictability Score: {revenue_quality['predictability_score']:.1f}/100\")\nprint(f\"- Repeat Customer Rate: {revenue_quality['repeat_customer_rate']:.1f}%\")\n\n# Create revenue quality dashboard\nfig = make_subplots(\n    rows=2, cols=2,\n    subplot_titles=('Revenue Type Split', 'Monthly Revenue Volatility',\n                   'Revenue Quality Score', 'Business Model Evolution'),\n    specs=[[{'type': 'pie'}, {'type': 'scatter'}],\n           [{'type': 'indicator'}, {'type': 'bar'}]]\n)\n\n# Revenue type split\nfig.add_trace(\n    go.Pie(labels=['Recurring (SaaS)', 'One-Time'],\n           values=[revenue_quality['recurring_revenue'], revenue_quality['one_time_revenue']],\n           marker_colors=['lightgreen', 'lightcoral']),\n    row=1, col=1\n)\n\n# Monthly revenue trend\nmonthly_revenue = metrics.transactions.groupby('month')['amount'].sum()\nfig.add_trace(\n    go.Scatter(x=monthly_revenue.index.astype(str), y=monthly_revenue.values,\n               mode='lines', line=dict(color='blue', width=2)),\n    row=1, col=2\n)\n\n# Revenue quality gauge\nfig.add_trace(\n    go.Indicator(\n        mode=\"gauge+number\",\n        value=revenue_quality['revenue_quality_score'],\n        title={'text': \"Revenue Quality Score\"},\n        gauge={'axis': {'range': [0, 100]},\n               'bar': {'color': \"darkblue\"},\n               'steps': [\n                   {'range': [0, 50], 'color': \"lightgray\"},\n                   {'range': [50, 75], 'color': \"gray\"},\n                   {'range': [75, 100], 'color': \"lightgreen\"}],\n               'threshold': {'line': {'color': \"red\", 'width': 4},\n                            'thickness': 0.75, 'value': 80}}),\n    row=2, col=1\n)\n\n# Business model evolution - fixed to use 'revenue_type' instead of 'transaction_type'\n# First check if column exists and what it's called\nif 'revenue_type' in metrics.transactions.columns:\n    type_column = 'revenue_type'\nelif 'category' in metrics.transactions.columns:\n    type_column = 'category'\nelse:\n    # Fallback - create a simple type column\n    type_column = 'type'\n    metrics.transactions['type'] = 'Revenue'\n\nyearly_type = metrics.transactions.groupby(['year', type_column])['amount'].sum().unstack(fill_value=0)\nyearly_type_pct = yearly_type.div(yearly_type.sum(axis=1), axis=0) * 100\n\nfor col in yearly_type_pct.columns:\n    fig.add_trace(\n        go.Bar(x=yearly_type_pct.index, y=yearly_type_pct[col],\n               name=col),\n        row=2, col=2\n    )\n\n# Update layout\nfig.update_layout(height=800, showlegend=True,\n                  title_text=\"Revenue Quality & Predictability Analysis\",\n                  title_font_size=20)\nfig.update_xaxes(title_text=\"Month\", row=1, col=2)\nfig.update_yaxes(title_text=\"Revenue (£)\", row=1, col=2)\nfig.update_xaxes(title_text=\"Year\", row=2, col=2)\nfig.update_yaxes(title_text=\"Percentage\", row=2, col=2)\n\nfig.show()"}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. LTV:CAC Analysis (Implied Metrics)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate LTV:CAC with different assumptions\n", "ltv_scenarios = []\n", "\n", "for gross_margin in [0.70, 0.75, 0.80]:\n", "    for cac_months in [2, 3, 4]:\n", "        ltv_cac = metrics.calculate_ltv_cac_implied(\n", "            assumed_gross_margin=gross_margin,\n", "            assumed_cac_months=cac_months\n", "        )\n", "        ltv_cac['scenario'] = f\"{int(gross_margin*100)}% GM, {cac_months}mo CAC\"\n", "        ltv_scenarios.append(ltv_cac)\n", "\n", "ltv_df = pd.DataFrame(ltv_scenarios)\n", "\n", "# Display LTV:CAC scenarios\n", "print(\"LTV:CAC Ratio Scenarios\")\n", "print(\"=\" * 60)\n", "display(ltv_df[['scenario', 'ltv', 'implied_cac', 'ltv_cac_ratio']].style.background_gradient(\n", "    subset=['ltv_cac_ratio'], cmap='RdYlGn', vmin=1, vmax=5\n", "))\n", "\n", "# Visualize LTV:CAC\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# Create heatmap\n", "pivot_data = ltv_df.pivot_table(\n", "    values='ltv_cac_ratio',\n", "    index='assumed_gross_margin',\n", "    columns='assumed_cac_months'\n", ")\n", "\n", "sns.heatmap(pivot_data, annot=True, fmt='.2f', cmap='RdYlGn', \n", "            vmin=1, vmax=5, center=3, ax=ax,\n", "            cbar_kws={'label': 'LTV:CAC Ratio'})\n", "\n", "ax.set_title('LTV:CAC Ratio Sensitivity Analysis', fontsize=16, pad=20)\n", "ax.set_xlabel('Assumed CAC (Months of Revenue)', fontsize=12)\n", "ax.set_ylabel('Assumed Gross Margin', fontsize=12)\n", "\n", "# Add reference lines\n", "ax.axhline(y=1.5, color='black', linestyle='--', linewidth=2)\n", "ax.axvline(x=1.5, color='black', linestyle='--', linewidth=2)\n", "ax.text(1.5, 1.6, 'Target: 3:1 <PERSON>io', ha='center', fontsize=10, weight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Best estimate\n", "best_estimate = metrics.calculate_ltv_cac_implied()\n", "print(f\"\\n📊 Best Estimate (75% GM, 3mo CAC):\")\n", "print(f\"- LTV: £{best_estimate['ltv']:,.0f}\")\n", "print(f\"- Implied CAC: £{best_estimate['implied_cac']:,.0f}\")\n", "print(f\"- LTV:CAC Ratio: {best_estimate['ltv_cac_ratio']:.1f}:1\")\n", "print(f\"\\n💡 Target: 3:1 ratio for healthy SaaS businesses\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Rule of 40 Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate Rule of 40 for different scenarios\n", "rule_of_40_scenarios = []\n", "\n", "# Historical years with different profit margin assumptions\n", "for year in range(2021, 2025):\n", "    for profit_margin in [-20, -10, 0, 10, 20]:\n", "        rule_40 = metrics.calculate_rule_of_40(year=year, estimated_profit_margin=profit_margin)\n", "        rule_of_40_scenarios.append(rule_40)\n", "\n", "rule_40_df = pd.DataFrame(rule_of_40_scenarios)\n", "\n", "# Create Rule of 40 visualization\n", "fig = go.Figure()\n", "\n", "# Add traces for each profit margin scenario\n", "for margin in [-20, -10, 0, 10, 20]:\n", "    data = rule_40_df[rule_40_df['profit_margin'] == margin]\n", "    fig.add_trace(\n", "        go.<PERSON>er(x=data['year'], y=data['rule_of_40_score'],\n", "                   mode='lines+markers', name=f'{margin}% Profit Margin',\n", "                   line=dict(width=2))\n", "    )\n", "\n", "# Add Rule of 40 threshold\n", "fig.add_hline(y=40, line_dash=\"dash\", line_color=\"green\", \n", "              annotation_text=\"Rule of 40 Threshold\")\n", "\n", "# Update layout\n", "fig.update_layout(\n", "    title=\"Rule of 40 Analysis: Growth + Profitability\",\n", "    xaxis_title=\"Year\",\n", "    yaxis_title=\"Rule of 40 Score\",\n", "    height=500,\n", "    hovermode='x unified'\n", ")\n", "\n", "fig.show()\n", "\n", "# Current year analysis\n", "current_rule_40 = metrics.calculate_rule_of_40(year=2024, estimated_profit_margin=0)\n", "print(f\"\\n📊 2024 Rule of 40 Analysis:\")\n", "print(f\"- Revenue Growth Rate: {current_rule_40['revenue_growth_rate']:.1f}%\")\n", "print(f\"- Estimated Profit Margin: {current_rule_40['profit_margin']:.1f}%\")\n", "print(f\"- Rule of 40 Score: {current_rule_40['rule_of_40_score']:.1f}\")\n", "print(f\"- {current_rule_40['interpretation']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Executive Summary & Valuation Implications"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create executive dashboard\n", "fig = make_subplots(\n", "    rows=3, cols=3,\n", "    subplot_titles=('Revenue Growth', 'Customer Growth', 'NDR Trend',\n", "                   'Revenue Quality', 'LTV:CAC Ratio', 'Rule of 40',\n", "                   'Concentration Risk', 'Valuation Range', 'Growth Trajectory'),\n", "    specs=[[{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],\n", "           [{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],\n", "           [{'type': 'indicator'}, {'type': 'bar'}, {'type': 'scatter'}]],\n", "    row_heights=[0.3, 0.3, 0.4]\n", ")\n", "\n", "# Key metrics\n", "summary = valuation_summary\n", "latest_ndr = metrics.calculate_ndr(2023, 2024)\n", "concentration = metrics.calculate_revenue_concentration()\n", "ltv_cac = metrics.calculate_ltv_cac_implied()\n", "rule_40 = metrics.calculate_rule_of_40()\n", "quality = metrics.calculate_revenue_quality_score()\n", "\n", "# Revenue growth indicator\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number+delta\",\n", "        value=summary['revenue_metrics']['arr_2024'],\n", "        title={\"text\": \"2024 ARR\"},\n", "        delta={'reference': 11937, 'relative': True},\n", "        number={'prefix': \"£\", 'valueformat': \",.0f\"}\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Customer growth\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number+delta\",\n", "        value=summary['revenue_metrics']['customer_count'],\n", "        title={\"text\": \"Total Customers\"},\n", "        delta={'reference': 2}\n", "    ),\n", "    row=1, col=2\n", ")\n", "\n", "# NDR\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"gauge+number\",\n", "        value=latest_ndr['ndr'],\n", "        title={\"text\": \"Net Dollar Retention\"},\n", "        gauge={'axis': {'range': [80, 120]},\n", "               'bar': {'color': \"darkblue\"},\n", "               'steps': [\n", "                   {'range': [80, 100], 'color': \"lightgray\"},\n", "                   {'range': [100, 110], 'color': \"gray\"},\n", "                   {'range': [110, 120], 'color': \"lightgreen\"}]}\n", "    ),\n", "    row=1, col=3\n", ")\n", "\n", "# Revenue quality\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=quality['revenue_quality_score'],\n", "        title={\"text\": \"Revenue Quality Score\"},\n", "        number={'suffix': \"/100\"}\n", "    ),\n", "    row=2, col=1\n", ")\n", "\n", "# LTV:CAC\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=ltv_cac['ltv_cac_ratio'],\n", "        title={\"text\": \"LTV:CAC Ratio\"},\n", "        number={'suffix': \":1\"}\n", "    ),\n", "    row=2, col=2\n", ")\n", "\n", "# Rule of 40\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=rule_40['rule_of_40_score'],\n", "        title={\"text\": \"Rule of 40 Score\"},\n", "        number={'suffix': \"%\"}\n", "    ),\n", "    row=2, col=3\n", ")\n", "\n", "# Concentration risk\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=concentration['hhi'],\n", "        title={\"text\": \"HHI Concentration\"},\n", "        number={'valueformat': \".4f\"}\n", "    ),\n", "    row=3, col=1\n", ")\n", "\n", "# Valuation range\n", "valuation_data = summary['valuation_indicators']\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=['Low (5x)', 'Mid (6.5x)', 'High (8x)'],\n", "        y=[valuation_data['implied_valuation_low'],\n", "           valuation_data['implied_valuation_mid'],\n", "           valuation_data['implied_valuation_high']],\n", "        text=[f\"£{v/1e6:.1f}M\" for v in [valuation_data['implied_valuation_low'],\n", "                                         valuation_data['implied_valuation_mid'],\n", "                                         valuation_data['implied_valuation_high']]],\n", "        textposition='outside',\n", "        marker_color=['lightcoral', 'lightblue', 'lightgreen']\n", "    ),\n", "    row=3, col=2\n", ")\n", "\n", "# Growth trajectory\n", "growth_df = metrics.calculate_growth_accounting()\n", "future_years = [2025, 2026, 2027]\n", "future_revenue = [growth_df['total_revenue'].iloc[-1] * (1.5 ** i) for i in range(1, 4)]\n", "\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=growth_df['year'], y=growth_df['total_revenue'],\n", "               mode='lines+markers', name='Historic',\n", "               line=dict(color='blue', width=3)),\n", "    row=3, col=3\n", ")\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=future_years, y=future_revenue,\n", "               mode='lines+markers', name='Projected',\n", "               line=dict(color='green', width=3, dash='dash')),\n", "    row=3, col=3\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=1000, showlegend=False,\n", "                  title_text=\"MCX3D Valuation Metrics Dashboard\",\n", "                  title_font_size=24)\n", "\n", "fig.show()\n", "\n", "# Print executive summary\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EXECUTIVE SUMMARY - MCX3D VALUATION METRICS\")\n", "print(\"=\"*80)\n", "print(f\"\\n📈 Growth Metrics:\")\n", "print(f\"   • Revenue Growth (2020-2024): {summary['revenue_metrics']['revenue_growth_2020_2024']:.0f}%\")\n", "print(f\"   • Current ARR: £{summary['revenue_metrics']['arr_2024']:,.0f}\")\n", "print(f\"   • Customer Base: {summary['revenue_metrics']['customer_count']} clients\")\n", "print(f\"   • Average Revenue per Customer: £{summary['revenue_metrics']['avg_revenue_per_customer']:,.0f}\")\n", "\n", "print(f\"\\n🎯 SaaS Health Indicators:\")\n", "print(f\"   • Net Dollar Retention: {summary['saas_health_metrics']['ndr']:.1f}%\")\n", "print(f\"   • LTV:CAC Ratio: {summary['saas_health_metrics']['ltv_cac_ratio']:.1f}:1\")\n", "print(f\"   • Rule of 40 Score: {summary['saas_health_metrics']['rule_of_40_score']:.1f}\")\n", "print(f\"   • Revenue Quality Score: {summary['saas_health_metrics']['revenue_quality_score']:.1f}/100\")\n", "\n", "print(f\"\\n⚠️  Risk Factors:\")\n", "print(f\"   • Customer Concentration (HHI): {summary['risk_metrics']['customer_concentration_hhi']:.3f}\")\n", "print(f\"   • Top 5 Customer Revenue Share: {summary['risk_metrics']['top_5_customer_share']:.1f}%\")\n", "print(f\"   • Recurring Revenue: {summary['risk_metrics']['recurring_revenue_percentage']:.1f}%\")\n", "\n", "print(f\"\\n💰 Valuation Range:\")\n", "print(f\"   • Conservative (5x ARR): £{summary['valuation_indicators']['implied_valuation_low']:,.0f}\")\n", "print(f\"   • Mid-Range (6.5x ARR): £{summary['valuation_indicators']['implied_valuation_mid']:,.0f}\")\n", "print(f\"   • Optimistic (8x ARR): £{summary['valuation_indicators']['implied_valuation_high']:,.0f}\")\n", "\n", "print(f\"\\n🚀 Path to £1M ARR:\")\n", "print(f\"   • Current Run Rate: £{summary['revenue_metrics']['arr_2024']:,.0f}\")\n", "print(f\"   • Required Growth: {(1_000_000 / summary['revenue_metrics']['arr_2024'] - 1) * 100:.0f}%\")\n", "print(f\"   • Timeline at 50% YoY Growth: ~2-3 years\")\n", "print(\"\\n\" + \"=\"*80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Export Metrics for Valuation Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all calculated metrics\n", "import os\n", "\n", "# Create output directory\n", "output_dir = '../data/derived_metrics'\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Save comprehensive metrics\n", "all_metrics = {\n", "    'valuation_summary': valuation_summary,\n", "    'growth_accounting': growth_accounting.to_dict('records'),\n", "    'ndr_analysis': cohort_df.to_dict('records'),\n", "    'concentration_metrics': concentration,\n", "    'revenue_quality': revenue_quality,\n", "    'ltv_cac_analysis': ltv_df.to_dict('records'),\n", "    'rule_of_40': rule_40_df.to_dict('records')\n", "}\n", "\n", "# Save to JSON\n", "output_path = os.path.join(output_dir, 'historic_growth_metrics.json')\n", "with open(output_path, 'w') as f:\n", "    json.dump(all_metrics, f, indent=2, default=str)\n", "\n", "print(f\"✅ Metrics saved to: {output_path}\")\n", "\n", "# Save key tables as CSV for easy import\n", "growth_accounting.to_csv(os.path.join(output_dir, 'growth_accounting.csv'), index=False)\n", "cohort_df.to_csv(os.path.join(output_dir, 'ndr_cohort_analysis.csv'), index=False)\n", "ltv_df.to_csv(os.path.join(output_dir, 'ltv_cac_scenarios.csv'), index=False)\n", "\n", "print(\"\\n📊 All metrics exported successfully!\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Review the generated metrics and visualizations\")\n", "print(\"2. Prepare the Historic Growth Chapter narrative\")\n", "print(\"3. Create DCF model using these baseline metrics\")\n", "print(\"4. Develop comparable company analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}
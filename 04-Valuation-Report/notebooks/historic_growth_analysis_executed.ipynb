{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MCX3D Historic Growth Analysis\n", "## Forensic Financial Analysis for Valuation Report\n", "\n", "This notebook provides CFO-level analysis of MCX3D's historic performance from 2020-2025."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:46.089447Z", "iopub.status.busy": "2025-07-29T07:55:46.089066Z", "iopub.status.idle": "2025-07-29T07:55:46.669467Z", "shell.execute_reply": "2025-07-29T07:55:46.669225Z"}}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import json\n", "import sys\n", "import os\n", "from datetime import datetime\n", "\n", "# Add src to path\n", "sys.path.append('../src')\n", "from valuation_metrics import SaaSMetricsCalculator\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', lambda x: '%.2f' % x)\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Validation"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:46.670799Z", "iopub.status.busy": "2025-07-29T07:55:46.670701Z", "iopub.status.idle": "2025-07-29T07:55:46.701222Z", "shell.execute_reply": "2025-07-29T07:55:46.701003Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MCX3D Valuation Metrics Summary\n", "==================================================\n", "{\n", "  \"timestamp\": \"2025-07-29T10:55:46.699714\",\n", "  \"revenue_metrics\": {\n", "    \"arr_2024\": 262466.1,\n", "    \"revenue_growth_2020_2024\": 2098.7,\n", "    \"customer_count\": 11.0,\n", "    \"avg_revenue_per_customer\": 23860.55\n", "  },\n", "  \"saas_health_metrics\": {\n", "    \"ndr\": 491.7,\n", "    \"gross_retention\": 100.0,\n", "    \"ltv_cac_ratio\": 3.55,\n", "    \"rule_of_40_score\": 288.1,\n", "    \"revenue_quality_score\": 70.3\n", "  },\n", "  \"risk_metrics\": {\n", "    \"customer_concentration_hhi\": 0.4436,\n", "    \"top_5_customer_share\": 85.4,\n", "    \"recurring_revenue_percentage\": 92.9\n", "  },\n", "  \"valuation_indicators\": {\n", "    \"revenue_multiple_range\": \"5x - 8x ARR\",\n", "    \"implied_valuation_low\": 1312330.0,\n", "    \"implied_valuation_mid\": 1706030.0,\n", "    \"implied_valuation_high\": 2099729.0\n", "  }\n", "}\n"]}], "source": ["# Load data paths\n", "base_path = \"../../03-Financial-Foundation/data\"\n", "transactions_path = os.path.join(base_path, \"all_transactions_2020_onwards.json\")\n", "customer_path = os.path.join(base_path, \"revenue/customer_revenue_analysis.json\")\n", "\n", "# Initialize calculator\n", "metrics = SaaSMetricsCalculator(transactions_path, customer_path)\n", "\n", "# Generate comprehensive metrics summary\n", "valuation_summary = metrics.generate_valuation_metrics_summary()\n", "\n", "print(\"MCX3D Valuation Metrics Summary\")\n", "print(\"=\" * 50)\n", "print(json.dumps(valuation_summary, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Revenue Growth Decomposition\n", "\n", "Breaking down revenue growth into its core components is crucial for understanding the quality and sustainability of growth."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:46.716018Z", "iopub.status.busy": "2025-07-29T07:55:46.715919Z", "iopub.status.idle": "2025-07-29T07:55:46.723336Z", "shell.execute_reply": "2025-07-29T07:55:46.723165Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Revenue Growth Accounting (2020-2024)\n", "================================================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year</th>\n", "      <th>total_revenue</th>\n", "      <th>new_revenue</th>\n", "      <th>expansion_revenue</th>\n", "      <th>contraction_revenue</th>\n", "      <th>churn_revenue</th>\n", "      <th>net_revenue_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020</td>\n", "      <td>11937.50</td>\n", "      <td>11937.50</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>11937.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021</td>\n", "      <td>8925.48</td>\n", "      <td>8625.48</td>\n", "      <td>0.00</td>\n", "      <td>-11637.50</td>\n", "      <td>0.00</td>\n", "      <td>-3012.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>15922.93</td>\n", "      <td>2629.00</td>\n", "      <td>6471.45</td>\n", "      <td>-2103.00</td>\n", "      <td>0.00</td>\n", "      <td>6997.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023</td>\n", "      <td>67626.06</td>\n", "      <td>45596.06</td>\n", "      <td>14277.07</td>\n", "      <td>-350.00</td>\n", "      <td>-7820.00</td>\n", "      <td>51703.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024</td>\n", "      <td>262466.10</td>\n", "      <td>31965.60</td>\n", "      <td>199116.50</td>\n", "      <td>-11346.06</td>\n", "      <td>-24896.00</td>\n", "      <td>194840.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   year  total_revenue  new_revenue  expansion_revenue  contraction_revenue  \\\n", "0  2020       11937.50     11937.50               0.00                 0.00   \n", "1  2021        8925.48      8625.48               0.00            -11637.50   \n", "2  2022       15922.93      2629.00            6471.45             -2103.00   \n", "3  2023       67626.06     45596.06           14277.07              -350.00   \n", "4  2024      262466.10     31965.60          199116.50            -11346.06   \n", "\n", "   churn_revenue  net_revenue_change  \n", "0           0.00            11937.50  \n", "1           0.00            -3012.02  \n", "2           0.00             6997.45  \n", "3       -7820.00            51703.13  \n", "4      -24896.00           194840.04  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get growth accounting data\n", "growth_accounting = metrics.calculate_growth_accounting()\n", "\n", "# Display growth accounting table\n", "print(\"Revenue Growth Accounting (2020-2024)\")\n", "print(\"=\" * 80)\n", "display(growth_accounting[['year', 'total_revenue', 'new_revenue', 'expansion_revenue', \n", "                          'contraction_revenue', 'churn_revenue', 'net_revenue_change']])\n", "\n", "# Calculate growth rates\n", "growth_accounting['yoy_growth'] = growth_accounting['total_revenue'].pct_change() * 100\n", "growth_accounting['new_customer_contribution'] = (growth_accounting['new_revenue'] / \n", "                                                  growth_accounting['total_revenue'].shift(1) * 100)\n", "growth_accounting['expansion_contribution'] = (growth_accounting['expansion_revenue'] / \n", "                                              growth_accounting['total_revenue'].shift(1) * 100)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:46.724333Z", "iopub.status.busy": "2025-07-29T07:55:46.724256Z", "iopub.status.idle": "2025-07-29T07:55:47.291673Z", "shell.execute_reply": "2025-07-29T07:55:47.291437Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"connector": {"line": {"color": "rgb(63, 63, 63)"}}, "decreasing": {"marker": {"color": "red"}}, "increasing": {"marker": {"color": "green"}}, "totals": {"marker": {"color": "blue"}}, "type": "waterfall", "x": ["2021 Start", "2021 New", "2021 Expansion", "2021 Contraction", "2021 Churn", "2022 Start", "2022 New", "2022 Expansion", "2022 Contraction", "2022 Churn", "2023 Start", "2023 New", "2023 Expansion", "2023 Contraction", "2023 Churn", "2024 Start", "2024 New", "2024 Expansion", "2024 Contraction", "2024 Churn"], "xaxis": "x", "y": [11937.5, 8625.48, 0.0, -11637.5, 0.0, 8925.48, 2629.0, 6471.45, -2103.0, 0.0, 15922.93, 45596.06, 14277.07, -350.0, -7820.0, 67626.06, 31965.6, 199116.5, -11346.06, -24896.0], "yaxis": "y"}, {"marker": {"color": "lightblue"}, "name": "New Customers", "type": "bar", "x": [2021, 2022, 2023, 2024], "xaxis": "x2", "y": [72.25532984293194, 29.454998498680183, 286.35470984297484, 47.26816851373568], "yaxis": "y2"}, {"marker": {"color": "darkblue"}, "name": "Expansion", "type": "bar", "x": [2021, 2022, 2023, 2024], "xaxis": "x2", "y": [0.0, 72.50534425039325, 89.66358578477704, 294.4375289644259], "yaxis": "y2"}, {"line": {"color": "purple", "width": 3}, "mode": "lines+markers", "name": "Total Customers", "type": "scatter", "x": {"bdata": "5AflB+YH5wfoBw==", "dtype": "i2"}, "xaxis": "x3", "y": {"bdata": "AQQIDQs=", "dtype": "i1"}, "yaxis": "y3"}, {"line": {"color": "green", "width": 3}, "mode": "lines+markers", "name": "Revenue per Customer", "type": "scatter", "x": {"bdata": "5AflB+YH5wfoBw==", "dtype": "i2"}, "xaxis": "x4", "y": {"bdata": "AAAAAMBQx0AK16NwvW6hQKRwPQp3GZ9A+015LgFStEDaN6x9I03XQA==", "dtype": "f8"}, "yaxis": "y4"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "Revenue Growth Waterfall", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Growth Rate Decomposition", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Customer Count Evolution", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Revenue per Customer Trend", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}], "height": 800, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"size": 20}, "text": "MCX3D Revenue Growth Analysis (2020-2024)"}, "xaxis": {"anchor": "y", "domain": [0.0, 0.45], "tickangle": -45}, "xaxis2": {"anchor": "y2", "domain": [0.55, 1.0], "tickangle": -45}, "xaxis3": {"anchor": "y3", "domain": [0.0, 0.45], "tickangle": -45}, "xaxis4": {"anchor": "y4", "domain": [0.55, 1.0], "tickangle": -45}, "yaxis": {"anchor": "x", "domain": [0.625, 1.0]}, "yaxis2": {"anchor": "x2", "domain": [0.625, 1.0]}, "yaxis3": {"anchor": "x3", "domain": [0.0, 0.375]}, "yaxis4": {"anchor": "x4", "domain": [0.0, 0.375]}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create revenue waterfall chart\n", "fig = make_subplots(rows=2, cols=2,\n", "                    subplot_titles=('Revenue Growth Waterfall', 'Growth Rate Decomposition',\n", "                                   'Customer Count Evolution', 'Revenue per Customer Trend'),\n", "                    specs=[[{'type': 'bar'}, {'type': 'bar'}],\n", "                           [{'type': 'scatter'}, {'type': 'scatter'}]])\n", "\n", "# Waterfall chart data\n", "years = growth_accounting['year'].tolist()[1:]  # Skip first year\n", "categories = []\n", "values = []\n", "\n", "for idx, year in enumerate(years):\n", "    row = growth_accounting[growth_accounting['year'] == year].iloc[0]\n", "    categories.extend([f'{year} Start', f'{year} New', f'{year} Expansion', \n", "                      f'{year} Contraction', f'{year} Churn'])\n", "    \n", "    prior_revenue = growth_accounting[growth_accounting['year'] == year-1].iloc[0]['total_revenue']\n", "    values.extend([prior_revenue, row['new_revenue'], row['expansion_revenue'],\n", "                  row['contraction_revenue'], row['churn_revenue']])\n", "\n", "# Add waterfall\n", "fig.add_trace(\n", "    go.Waterfall(\n", "        x=categories,\n", "        y=values,\n", "        connector={\"line\": {\"color\": \"rgb(63, 63, 63)\"}},\n", "        increasing={\"marker\": {\"color\": \"green\"}},\n", "        decreasing={\"marker\": {\"color\": \"red\"}},\n", "        totals={\"marker\": {\"color\": \"blue\"}}\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Growth rate decomposition\n", "fig.add_trace(\n", "    go.Bar(x=years, y=growth_accounting['new_customer_contribution'][1:].tolist(),\n", "           name='New Customers', marker_color='lightblue'),\n", "    row=1, col=2\n", ")\n", "fig.add_trace(\n", "    go.Bar(x=years, y=growth_accounting['expansion_contribution'][1:].tolist(),\n", "           name='Expansion', marker_color='darkblue'),\n", "    row=1, col=2\n", ")\n", "\n", "# Customer count evolution\n", "fig.add_trace(\n", "    go.<PERSON>er(x=growth_accounting['year'], y=growth_accounting['customer_count'],\n", "               mode='lines+markers', name='Total Customers',\n", "               line=dict(color='purple', width=3)),\n", "    row=2, col=1\n", ")\n", "\n", "# Revenue per customer\n", "revenue_per_customer = growth_accounting['total_revenue'] / growth_accounting['customer_count']\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=growth_accounting['year'], y=revenue_per_customer,\n", "               mode='lines+markers', name='Revenue per Customer',\n", "               line=dict(color='green', width=3)),\n", "    row=2, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=800, showlegend=True,\n", "                  title_text=\"MCX3D Revenue Growth Analysis (2020-2024)\",\n", "                  title_font_size=20)\n", "fig.update_xaxes(tickangle=-45)\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Net Dollar Retention (NDR) Analysis\n", "\n", "NDR is the most critical SaaS metric for valuation. Best-in-class B2B SaaS companies achieve 110%+ NDR."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.292882Z", "iopub.status.busy": "2025-07-29T07:55:47.292811Z", "iopub.status.idle": "2025-07-29T07:55:47.347030Z", "shell.execute_reply": "2025-07-29T07:55:47.346810Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Net Dollar Retention by Co<PERSON>t\n", "============================================================\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_cedc4_row0_col0, #T_cedc4_row0_col1, #T_cedc4_row0_col2, #T_cedc4_row0_col3, #T_cedc4_row1_col3, #T_cedc4_row2_col3 {\n", "  background-color: #a50026;\n", "  color: #f1f1f1;\n", "}\n", "#T_cedc4_row1_col0, #T_cedc4_row2_col0, #T_cedc4_row2_col1, #T_cedc4_row3_col0, #T_cedc4_row3_col1, #T_cedc4_row3_col2 {\n", "  background-color: #000000;\n", "  color: #f1f1f1;\n", "}\n", "#T_cedc4_row1_col1 {\n", "  background-color: #fee28f;\n", "  color: #000000;\n", "}\n", "#T_cedc4_row1_col2, #T_cedc4_row2_col2, #T_cedc4_row3_col3 {\n", "  background-color: #006837;\n", "  color: #f1f1f1;\n", "}\n", "</style>\n", "<table id=\"T_cedc4\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"index_name level0\" >measure_year</th>\n", "      <th id=\"T_cedc4_level0_col0\" class=\"col_heading level0 col0\" >2021</th>\n", "      <th id=\"T_cedc4_level0_col1\" class=\"col_heading level0 col1\" >2022</th>\n", "      <th id=\"T_cedc4_level0_col2\" class=\"col_heading level0 col2\" >2023</th>\n", "      <th id=\"T_cedc4_level0_col3\" class=\"col_heading level0 col3\" >2024</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >cohort_year</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_cedc4_level0_row0\" class=\"row_heading level0 row0\" >2020</th>\n", "      <td id=\"T_cedc4_row0_col0\" class=\"data row0 col0\" >2.500000</td>\n", "      <td id=\"T_cedc4_row0_col1\" class=\"data row0 col1\" >41.700000</td>\n", "      <td id=\"T_cedc4_row0_col2\" class=\"data row0 col2\" >0.000000</td>\n", "      <td id=\"T_cedc4_row0_col3\" class=\"data row0 col3\" >5.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cedc4_level0_row1\" class=\"row_heading level0 row1\" >2021</th>\n", "      <td id=\"T_cedc4_row1_col0\" class=\"data row1 col0\" >nan</td>\n", "      <td id=\"T_cedc4_row1_col1\" class=\"data row1 col1\" >96.400000</td>\n", "      <td id=\"T_cedc4_row1_col2\" class=\"data row1 col2\" >162.100000</td>\n", "      <td id=\"T_cedc4_row1_col3\" class=\"data row1 col3\" >73.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cedc4_level0_row2\" class=\"row_heading level0 row2\" >2022</th>\n", "      <td id=\"T_cedc4_row2_col0\" class=\"data row2 col0\" >nan</td>\n", "      <td id=\"T_cedc4_row2_col1\" class=\"data row2 col1\" >nan</td>\n", "      <td id=\"T_cedc4_row2_col2\" class=\"data row2 col2\" >306.200000</td>\n", "      <td id=\"T_cedc4_row2_col3\" class=\"data row2 col3\" >0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cedc4_level0_row3\" class=\"row_heading level0 row3\" >2023</th>\n", "      <td id=\"T_cedc4_row3_col0\" class=\"data row3 col0\" >nan</td>\n", "      <td id=\"T_cedc4_row3_col1\" class=\"data row3 col1\" >nan</td>\n", "      <td id=\"T_cedc4_row3_col2\" class=\"data row3 col2\" >nan</td>\n", "      <td id=\"T_cedc4_row3_col3\" class=\"data row3 col3\" >491.700000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x132f3a510>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate NDR for each cohort\n", "cohort_analysis = []\n", "\n", "for cohort_year in range(2020, 2024):\n", "    for measure_year in range(cohort_year + 1, 2025):\n", "        ndr_data = metrics.calculate_ndr(cohort_year, measure_year)\n", "        ndr_data['cohort_year'] = cohort_year\n", "        ndr_data['measure_year'] = measure_year\n", "        ndr_data['years_since_cohort'] = measure_year - cohort_year\n", "        cohort_analysis.append(ndr_data)\n", "\n", "cohort_df = pd.DataFrame(cohort_analysis)\n", "\n", "# Display NDR summary\n", "print(\"Net Dollar Retention by Co<PERSON><PERSON>\")\n", "print(\"=\" * 60)\n", "ndr_pivot = cohort_df.pivot(index='cohort_year', columns='measure_year', values='ndr')\n", "display(ndr_pivot.style.background_gradient(cmap='RdYlGn', vmin=80, vmax=120))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.348199Z", "iopub.status.busy": "2025-07-29T07:55:47.348099Z", "iopub.status.idle": "2025-07-29T07:55:47.373439Z", "shell.execute_reply": "2025-07-29T07:55:47.372826Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"line": {"width": 3}, "mode": "lines+markers", "name": "2020 Cohort", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "xaxis": "x", "y": {"bdata": "AAAAAAAABECamZmZmdlEQAAAAAAAAAAAAAAAAAAAFEA=", "dtype": "f8"}, "yaxis": "y"}, {"line": {"width": 3}, "mode": "lines+markers", "name": "2021 Cohort", "type": "scatter", "x": {"bdata": "5gfnB+gH", "dtype": "i2"}, "xaxis": "x", "y": {"bdata": "mpmZmZkZWEAzMzMzM0NkQAAAAAAAQFJA", "dtype": "f8"}, "yaxis": "y"}, {"line": {"width": 3}, "mode": "lines+markers", "name": "2022 Cohort", "type": "scatter", "x": {"bdata": "5wfoBw==", "dtype": "i2"}, "xaxis": "x", "y": {"bdata": "MzMzMzMjc0AAAAAAAAAAAA==", "dtype": "f8"}, "yaxis": "y"}, {"line": {"width": 3}, "mode": "lines+markers", "name": "2023 Cohort", "type": "scatter", "x": {"bdata": "6Ac=", "dtype": "i2"}, "xaxis": "x", "y": {"bdata": "MzMzMzO7fkA=", "dtype": "f8"}, "yaxis": "y"}, {"marker": {"color": ["lightcoral", "lightgreen"]}, "text": ["100.0", "391.7"], "textposition": "outside", "texttemplate": "%{text:.1f}%", "type": "bar", "x": ["Gross Retention", "Net Expansion"], "xaxis": "x2", "y": [100.0, 391.7], "yaxis": "y2"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "NDR Trends by Cohort", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "NDR Components Analysis", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"showarrow": false, "text": "100% Retention", "x": 1, "xanchor": "right", "xref": "x domain", "y": 100, "yanchor": "bottom", "yref": "y"}, {"showarrow": false, "text": "Best-in-class (110%)", "x": 1, "xanchor": "right", "xref": "x domain", "y": 110, "yanchor": "bottom", "yref": "y"}], "height": 400, "shapes": [{"line": {"color": "gray", "dash": "dash"}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 100, "y1": 100, "yref": "y"}, {"line": {"color": "green", "dash": "dash"}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 110, "y1": 110, "yref": "y"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"size": 18}, "text": "Net Dollar Retention Analysis"}, "xaxis": {"anchor": "y", "domain": [0.0, 0.45]}, "xaxis2": {"anchor": "y2", "domain": [0.55, 1.0]}, "yaxis": {"anchor": "x", "domain": [0.0, 1.0], "title": {"text": "NDR %"}}, "yaxis2": {"anchor": "x2", "domain": [0.0, 1.0], "title": {"text": "Percentage"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 NDR Insights:\n", "- Latest NDR (2023 cohort): 491.7%\n", "- Gross Retention: 100.0%\n", "- Net Expansion: 391.7%\n", "\n", "💡 Interpretation: Excellent - Best-in-class retention with strong expansion\n"]}], "source": ["# Create NDR visualization\n", "fig = make_subplots(rows=1, cols=2,\n", "                    subplot_titles=('NDR Trends by Cohort', 'NDR Components Analysis'))\n", "\n", "# NDR trends by cohort\n", "for cohort in cohort_df['cohort_year'].unique():\n", "    cohort_data = cohort_df[cohort_df['cohort_year'] == cohort]\n", "    fig.add_trace(\n", "        go.<PERSON>er(x=cohort_data['measure_year'], y=cohort_data['ndr'],\n", "                   mode='lines+markers', name=f'{cohort} Cohort',\n", "                   line=dict(width=3)),\n", "        row=1, col=1\n", "    )\n", "\n", "# Add 100% reference line\n", "fig.add_hline(y=100, line_dash=\"dash\", line_color=\"gray\", \n", "              annotation_text=\"100% Retention\", row=1, col=1)\n", "fig.add_hline(y=110, line_dash=\"dash\", line_color=\"green\", \n", "              annotation_text=\"Best-in-class (110%)\", row=1, col=1)\n", "\n", "# NDR components for latest cohort\n", "latest_cohort = cohort_df[cohort_df['cohort_year'] == 2023].iloc[-1]\n", "components = ['Gross Retention', 'Net Expansion']\n", "values = [latest_cohort['gross_retention'], latest_cohort['net_expansion']]\n", "\n", "fig.add_trace(\n", "    go.Bar(x=components, y=values, text=values,\n", "           texttemplate='%{text:.1f}%', textposition='outside',\n", "           marker_color=['lightcoral', 'lightgreen']),\n", "    row=1, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=400, title_text=\"Net Dollar Retention Analysis\",\n", "                  title_font_size=18)\n", "fig.update_yaxes(title_text=\"NDR %\", row=1, col=1)\n", "fig.update_yaxes(title_text=\"Percentage\", row=1, col=2)\n", "\n", "fig.show()\n", "\n", "# Print interpretation\n", "print(f\"\\n📊 NDR Insights:\")\n", "print(f\"- Latest NDR (2023 cohort): {latest_cohort['ndr']:.1f}%\")\n", "print(f\"- Gross Retention: {latest_cohort['gross_retention']:.1f}%\")\n", "print(f\"- Net Expansion: {latest_cohort['net_expansion']:.1f}%\")\n", "print(f\"\\n💡 Interpretation: \", end=\"\")\n", "if latest_cohort['ndr'] >= 110:\n", "    print(\"Excellent - Best-in-class retention with strong expansion\")\n", "elif latest_cohort['ndr'] >= 100:\n", "    print(\"Good - Positive net retention, approaching best-in-class\")\n", "else:\n", "    print(\"Needs improvement - Focus on reducing churn and driving expansion\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Customer Concentration Risk Analysis\n", "\n", "Understanding customer concentration is critical for risk assessment and valuation."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.378060Z", "iopub.status.busy": "2025-07-29T07:55:47.377742Z", "iopub.status.idle": "2025-07-29T07:55:47.506535Z", "shell.execute_reply": "2025-07-29T07:55:47.506327Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Customer Concentration Analysis\n", "==================================================\n", "HHI Score: 0.4436\n", "Interpretation: High concentration - Significant customer risk\n", "\n", "Top Customer: <PERSON> (65.6% of revenue)\n", "Top 5 Customers: 85.4% of revenue\n", "Top 10 Customers: 95.6% of revenue\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate concentration metrics\n", "concentration = metrics.calculate_revenue_concentration()\n", "\n", "print(\"Customer Concentration Analysis\")\n", "print(\"=\" * 50)\n", "print(f\"HHI Score: {concentration['hhi']:.4f}\")\n", "print(f\"Interpretation: {concentration['hhi_interpretation']}\")\n", "print(f\"\\nTop Customer: {concentration['top_1_customer']} ({concentration['top_1_share']:.1f}% of revenue)\")\n", "print(f\"Top 5 Customers: {concentration['top_5_share']:.1f}% of revenue\")\n", "print(f\"Top 10 Customers: {concentration['top_10_share']:.1f}% of revenue\")\n", "\n", "# Visualize concentration\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))\n", "\n", "# Pareto chart\n", "revenue_dist = concentration['revenue_distribution']\n", "customers = [item[0] for item in revenue_dist]\n", "shares = [item[1] * 100 for item in revenue_dist]\n", "cumulative = np.cumsum(shares)\n", "\n", "ax1.bar(range(len(customers)), shares, color='skyblue')\n", "ax1_twin = ax1.twinx()\n", "ax1_twin.plot(range(len(customers)), cumulative, color='red', marker='o', linewidth=2)\n", "ax1.set_xlabel('Customer Rank')\n", "ax1.set_ylabel('Revenue Share (%)', color='blue')\n", "ax1_twin.set_ylabel('Cumulative Share (%)', color='red')\n", "ax1.set_title('Customer Revenue Concentration (Pareto Chart)')\n", "ax1.set_xticks(range(len(customers)))\n", "ax1.set_xticklabels([f'#{i+1}' for i in range(len(customers))], rotation=45)\n", "\n", "# HHI visualization\n", "hhi_ranges = [0, 0.15, 0.25, 1.0]\n", "hhi_labels = ['Low Risk\\n(Well Diversified)', 'Moderate Risk\\n(Monitor)', 'High Risk\\n(Concentrated)']\n", "hhi_colors = ['green', 'yellow', 'red']\n", "\n", "for i in range(len(hhi_ranges)-1):\n", "    ax2.axhspan(hhi_ranges[i], hhi_ranges[i+1], alpha=0.3, color=hhi_colors[i])\n", "    ax2.text(0.5, (hhi_ranges[i] + hhi_ranges[i+1])/2, hhi_labels[i], \n", "             ha='center', va='center', fontsize=10, weight='bold')\n", "\n", "ax2.axhline(concentration['hhi'], color='black', linewidth=3, label=f\"MCX3D HHI: {concentration['hhi']:.3f}\")\n", "ax2.set_ylim(0, 0.4)\n", "ax2.set_xlim(0, 1)\n", "ax2.set_ylabel('HHI Score')\n", "ax2.set_title('Customer Concentration Risk Assessment')\n", "ax2.legend()\n", "ax2.set_xticks([])\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Revenue Quality & Predictability Analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.507661Z", "iopub.status.busy": "2025-07-29T07:55:47.507572Z", "iopub.status.idle": "2025-07-29T07:55:47.531559Z", "shell.execute_reply": "2025-07-29T07:55:47.531359Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Revenue Quality Assessment\n", "==================================================\n", "Overall Revenue Quality Score: 70.3/100\n", "\n", "Components:\n", "- Recurring Revenue: 92.9%\n", "- Predictability Score: 46.7/100\n", "- Repeat Customer Rate: 63.6%\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"domain": {"x": [0.0, 0.45], "y": [0.625, 1.0]}, "labels": ["Recurring (SaaS)", "One-Time"], "marker": {"colors": ["lightgreen", "lightcoral"]}, "type": "pie", "values": [368891.37, 28016.5]}, {"line": {"color": "blue", "width": 2}, "mode": "lines", "type": "scatter", "x": ["2021-06", "2021-07", "2021-08", "2021-09", "2021-10", "2021-11", "2021-12", "2022-01", "2022-02", "2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08", "2022-09", "2022-10", "2022-11", "2022-12", "2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10", "2023-11", "2023-12", "2024-01", "2024-02", "2024-03", "2024-04", "2024-05", "2024-06", "2024-07", "2024-10", "2024-11", "2024-12", "2025-02", "2025-04"], "xaxis": "x", "y": {"bdata": "AAAAAACApkAAAAAAAMCCQAAAAAAAQJpAAAAAAACAhkAAAAAAAICGQAAAAAAAgIZAAAAAAADAckAAAAAAAICGQAAAAAAA4IBAAAAAAACAm0AAAAAAAGCYQAAAAAAAgJZAAAAAAADAl0AAAAAAAECVQAAAAAAAAI5AAAAAAACAhkAAAAAAAOScQAAAAAAA4I9AAAAAAAAAnkAAAAAAAAazQAAAAAAAcJxAAAAAAAD0oEAAAAAAAMCXQAAAAAAAnMhAAAAAAAAgjEAAAAAAAErFQAAAAAAAPctAAAAAAADyskAAAAAAAISsQOF6FK4HjsJAAAAAAAAgjEAAAAAAAMK6QAAAAAAAUJRAAAAAAAAQvUAAAAAAACCMQAAAAAAAIIxAAAAAAAAgjEAAAAAAAMByQAAAAAAAwIJAAAAAAACIs0AAAAAAAOC1QAAAAAAArbtAXI/C9ahGoEA=", "dtype": "f8"}, "yaxis": "y"}, {"domain": {"x": [0.0, 0.45], "y": [0.0, 0.375]}, "gauge": {"axis": {"range": [0, 100]}, "bar": {"color": "darkblue"}, "steps": [{"color": "lightgray", "range": [0, 50]}, {"color": "gray", "range": [50, 75]}, {"color": "lightgreen", "range": [75, 100]}], "threshold": {"line": {"color": "red", "width": 4}, "thickness": 0.75, "value": 80}}, "mode": "gauge+number", "title": {"text": "Revenue Quality Score"}, "type": "indicator", "value": 70.3}, {"name": "Expense", "type": "bar", "x": {"bdata": "5QcAAOYHAADnBwAA6AcAAOkHAAA=", "dtype": "i4"}, "xaxis": "x2", "y": {"bdata": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "dtype": "f8"}, "yaxis": "y2"}, {"name": "Revenue", "type": "bar", "x": {"bdata": "5QcAAOYHAADnBwAA6AcAAOkHAAA=", "dtype": "i4"}, "xaxis": "x2", "y": {"bdata": "AAAAAAAAWUAAAAAAAABZQAAAAAAAAFlAAAAAAAAAWUAAAAAAAABZQA==", "dtype": "f8"}, "yaxis": "y2"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "Revenue Type Split", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Monthly Revenue Volatility", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Revenue Quality Score", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Business Model Evolution", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}], "height": 800, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"size": 20}, "text": "Revenue Quality & Predictability Analysis"}, "xaxis": {"anchor": "y", "domain": [0.55, 1.0], "title": {"text": "Month"}}, "xaxis2": {"anchor": "y2", "domain": [0.55, 1.0], "title": {"text": "Year"}}, "yaxis": {"anchor": "x", "domain": [0.625, 1.0], "title": {"text": "Revenue (£)"}}, "yaxis2": {"anchor": "x2", "domain": [0.0, 0.375], "title": {"text": "Percentage"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate revenue quality metrics\n", "revenue_quality = metrics.calculate_revenue_quality_score()\n", "\n", "print(\"Revenue Quality Assessment\")\n", "print(\"=\" * 50)\n", "print(f\"Overall Revenue Quality Score: {revenue_quality['revenue_quality_score']:.1f}/100\")\n", "print(f\"\\nComponents:\")\n", "print(f\"- Recurring Revenue: {revenue_quality['recurring_percentage']:.1f}%\")\n", "print(f\"- Predictability Score: {revenue_quality['predictability_score']:.1f}/100\")\n", "print(f\"- Repeat Customer Rate: {revenue_quality['repeat_customer_rate']:.1f}%\")\n", "\n", "# Create revenue quality dashboard\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Revenue Type Split', 'Monthly Revenue Volatility',\n", "                   'Revenue Quality Score', 'Business Model Evolution'),\n", "    specs=[[{'type': 'pie'}, {'type': 'scatter'}],\n", "           [{'type': 'indicator'}, {'type': 'bar'}]]\n", ")\n", "\n", "# Revenue type split\n", "fig.add_trace(\n", "    go.Pie(labels=['Recurring (SaaS)', 'One-Time'],\n", "           values=[revenue_quality['recurring_revenue'], revenue_quality['one_time_revenue']],\n", "           marker_colors=['lightgreen', 'lightcoral']),\n", "    row=1, col=1\n", ")\n", "\n", "# Monthly revenue trend\n", "monthly_revenue = metrics.transactions.groupby('month')['amount'].sum()\n", "fig.add_trace(\n", "    go.Scatter(x=monthly_revenue.index.astype(str), y=monthly_revenue.values,\n", "               mode='lines', line=dict(color='blue', width=2)),\n", "    row=1, col=2\n", ")\n", "\n", "# Revenue quality gauge\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"gauge+number\",\n", "        value=revenue_quality['revenue_quality_score'],\n", "        title={'text': \"Revenue Quality Score\"},\n", "        gauge={'axis': {'range': [0, 100]},\n", "               'bar': {'color': \"darkblue\"},\n", "               'steps': [\n", "                   {'range': [0, 50], 'color': \"lightgray\"},\n", "                   {'range': [50, 75], 'color': \"gray\"},\n", "                   {'range': [75, 100], 'color': \"lightgreen\"}],\n", "               'threshold': {'line': {'color': \"red\", 'width': 4},\n", "                            'thickness': 0.75, 'value': 80}}),\n", "    row=2, col=1\n", ")\n", "\n", "# Business model evolution - fixed to use 'revenue_type' instead of 'transaction_type'\n", "# First check if column exists and what it's called\n", "if 'revenue_type' in metrics.transactions.columns:\n", "    type_column = 'revenue_type'\n", "elif 'category' in metrics.transactions.columns:\n", "    type_column = 'category'\n", "else:\n", "    # Fallback - create a simple type column\n", "    type_column = 'type'\n", "    metrics.transactions['type'] = 'Revenue'\n", "\n", "yearly_type = metrics.transactions.groupby(['year', type_column])['amount'].sum().unstack(fill_value=0)\n", "yearly_type_pct = yearly_type.div(yearly_type.sum(axis=1), axis=0) * 100\n", "\n", "for col in yearly_type_pct.columns:\n", "    fig.add_trace(\n", "        go.Bar(x=yearly_type_pct.index, y=yearly_type_pct[col],\n", "               name=col),\n", "        row=2, col=2\n", "    )\n", "\n", "# Update layout\n", "fig.update_layout(height=800, showlegend=True,\n", "                  title_text=\"Revenue Quality & Predictability Analysis\",\n", "                  title_font_size=20)\n", "fig.update_xaxes(title_text=\"Month\", row=1, col=2)\n", "fig.update_yaxes(title_text=\"Revenue (£)\", row=1, col=2)\n", "fig.update_xaxes(title_text=\"Year\", row=2, col=2)\n", "fig.update_yaxes(title_text=\"Percentage\", row=2, col=2)\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. LTV:CAC Analysis (Implied Metrics)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.532688Z", "iopub.status.busy": "2025-07-29T07:55:47.532611Z", "iopub.status.idle": "2025-07-29T07:55:47.642562Z", "shell.execute_reply": "2025-07-29T07:55:47.642323Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LTV:CAC Ratio Scenarios\n", "============================================================\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_bcd6e_row0_col3 {\n", "  background-color: #016a38;\n", "  color: #f1f1f1;\n", "}\n", "#T_bcd6e_row1_col3 {\n", "  background-color: #e2f397;\n", "  color: #000000;\n", "}\n", "#T_bcd6e_row2_col3 {\n", "  background-color: #fed27f;\n", "  color: #000000;\n", "}\n", "#T_bcd6e_row3_col3, #T_bcd6e_row6_col3 {\n", "  background-color: #006837;\n", "  color: #f1f1f1;\n", "}\n", "#T_bcd6e_row4_col3 {\n", "  background-color: #c5e67e;\n", "  color: #000000;\n", "}\n", "#T_bcd6e_row5_col3 {\n", "  background-color: #fee593;\n", "  color: #000000;\n", "}\n", "#T_bcd6e_row7_col3 {\n", "  background-color: #a7d96b;\n", "  color: #000000;\n", "}\n", "#T_bcd6e_row8_col3 {\n", "  background-color: #fff2aa;\n", "  color: #000000;\n", "}\n", "</style>\n", "<table id=\"T_bcd6e\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_bcd6e_level0_col0\" class=\"col_heading level0 col0\" >scenario</th>\n", "      <th id=\"T_bcd6e_level0_col1\" class=\"col_heading level0 col1\" >ltv</th>\n", "      <th id=\"T_bcd6e_level0_col2\" class=\"col_heading level0 col2\" >implied_cac</th>\n", "      <th id=\"T_bcd6e_level0_col3\" class=\"col_heading level0 col3\" >ltv_cac_ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_bcd6e_row0_col0\" class=\"data row0 col0\" >70% GM, 2mo CAC</td>\n", "      <td id=\"T_bcd6e_row0_col1\" class=\"data row0 col1\" >12628.890000</td>\n", "      <td id=\"T_bcd6e_row0_col2\" class=\"data row0 col2\" >2540.800000</td>\n", "      <td id=\"T_bcd6e_row0_col3\" class=\"data row0 col3\" >4.970000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_bcd6e_row1_col0\" class=\"data row1 col0\" >70% GM, 3mo CAC</td>\n", "      <td id=\"T_bcd6e_row1_col1\" class=\"data row1 col1\" >12628.890000</td>\n", "      <td id=\"T_bcd6e_row1_col2\" class=\"data row1 col2\" >3811.200000</td>\n", "      <td id=\"T_bcd6e_row1_col3\" class=\"data row1 col3\" >3.310000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_bcd6e_row2_col0\" class=\"data row2 col0\" >70% GM, 4mo CAC</td>\n", "      <td id=\"T_bcd6e_row2_col1\" class=\"data row2 col1\" >12628.890000</td>\n", "      <td id=\"T_bcd6e_row2_col2\" class=\"data row2 col2\" >5081.600000</td>\n", "      <td id=\"T_bcd6e_row2_col3\" class=\"data row2 col3\" >2.490000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_bcd6e_row3_col0\" class=\"data row3 col0\" >75% GM, 2mo CAC</td>\n", "      <td id=\"T_bcd6e_row3_col1\" class=\"data row3 col1\" >13530.950000</td>\n", "      <td id=\"T_bcd6e_row3_col2\" class=\"data row3 col2\" >2540.800000</td>\n", "      <td id=\"T_bcd6e_row3_col3\" class=\"data row3 col3\" >5.330000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_bcd6e_row4_col0\" class=\"data row4 col0\" >75% GM, 3mo CAC</td>\n", "      <td id=\"T_bcd6e_row4_col1\" class=\"data row4 col1\" >13530.950000</td>\n", "      <td id=\"T_bcd6e_row4_col2\" class=\"data row4 col2\" >3811.200000</td>\n", "      <td id=\"T_bcd6e_row4_col3\" class=\"data row4 col3\" >3.550000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_bcd6e_row5_col0\" class=\"data row5 col0\" >75% GM, 4mo CAC</td>\n", "      <td id=\"T_bcd6e_row5_col1\" class=\"data row5 col1\" >13530.950000</td>\n", "      <td id=\"T_bcd6e_row5_col2\" class=\"data row5 col2\" >5081.600000</td>\n", "      <td id=\"T_bcd6e_row5_col3\" class=\"data row5 col3\" >2.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_bcd6e_row6_col0\" class=\"data row6 col0\" >80% GM, 2mo CAC</td>\n", "      <td id=\"T_bcd6e_row6_col1\" class=\"data row6 col1\" >14433.010000</td>\n", "      <td id=\"T_bcd6e_row6_col2\" class=\"data row6 col2\" >2540.800000</td>\n", "      <td id=\"T_bcd6e_row6_col3\" class=\"data row6 col3\" >5.680000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_bcd6e_row7_col0\" class=\"data row7 col0\" >80% GM, 3mo CAC</td>\n", "      <td id=\"T_bcd6e_row7_col1\" class=\"data row7 col1\" >14433.010000</td>\n", "      <td id=\"T_bcd6e_row7_col2\" class=\"data row7 col2\" >3811.200000</td>\n", "      <td id=\"T_bcd6e_row7_col3\" class=\"data row7 col3\" >3.790000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_bcd6e_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_bcd6e_row8_col0\" class=\"data row8 col0\" >80% GM, 4mo CAC</td>\n", "      <td id=\"T_bcd6e_row8_col1\" class=\"data row8 col1\" >14433.010000</td>\n", "      <td id=\"T_bcd6e_row8_col2\" class=\"data row8 col2\" >5081.600000</td>\n", "      <td id=\"T_bcd6e_row8_col3\" class=\"data row8 col3\" >2.840000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x13351f4d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Best Estimate (75% GM, 3mo CAC):\n", "- LTV: £13,531\n", "- Implied CAC: £3,811\n", "- LTV:CAC Ratio: 3.5:1\n", "\n", "💡 Target: 3:1 ratio for healthy SaaS businesses\n"]}], "source": ["# Calculate LTV:CAC with different assumptions\n", "ltv_scenarios = []\n", "\n", "for gross_margin in [0.70, 0.75, 0.80]:\n", "    for cac_months in [2, 3, 4]:\n", "        ltv_cac = metrics.calculate_ltv_cac_implied(\n", "            assumed_gross_margin=gross_margin,\n", "            assumed_cac_months=cac_months\n", "        )\n", "        ltv_cac['scenario'] = f\"{int(gross_margin*100)}% GM, {cac_months}mo CAC\"\n", "        ltv_scenarios.append(ltv_cac)\n", "\n", "ltv_df = pd.DataFrame(ltv_scenarios)\n", "\n", "# Display LTV:CAC scenarios\n", "print(\"LTV:CAC Ratio Scenarios\")\n", "print(\"=\" * 60)\n", "display(ltv_df[['scenario', 'ltv', 'implied_cac', 'ltv_cac_ratio']].style.background_gradient(\n", "    subset=['ltv_cac_ratio'], cmap='RdYlGn', vmin=1, vmax=5\n", "))\n", "\n", "# Visualize LTV:CAC\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# Create heatmap\n", "pivot_data = ltv_df.pivot_table(\n", "    values='ltv_cac_ratio',\n", "    index='assumed_gross_margin',\n", "    columns='assumed_cac_months'\n", ")\n", "\n", "sns.heatmap(pivot_data, annot=True, fmt='.2f', cmap='RdYlGn', \n", "            vmin=1, vmax=5, center=3, ax=ax,\n", "            cbar_kws={'label': 'LTV:CAC Ratio'})\n", "\n", "ax.set_title('LTV:CAC Ratio Sensitivity Analysis', fontsize=16, pad=20)\n", "ax.set_xlabel('Assumed CAC (Months of Revenue)', fontsize=12)\n", "ax.set_ylabel('Assumed Gross Margin', fontsize=12)\n", "\n", "# Add reference lines\n", "ax.axhline(y=1.5, color='black', linestyle='--', linewidth=2)\n", "ax.axvline(x=1.5, color='black', linestyle='--', linewidth=2)\n", "ax.text(1.5, 1.6, 'Target: 3:1 <PERSON>io', ha='center', fontsize=10, weight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Best estimate\n", "best_estimate = metrics.calculate_ltv_cac_implied()\n", "print(f\"\\n📊 Best Estimate (75% GM, 3mo CAC):\")\n", "print(f\"- LTV: £{best_estimate['ltv']:,.0f}\")\n", "print(f\"- Implied CAC: £{best_estimate['implied_cac']:,.0f}\")\n", "print(f\"- LTV:CAC Ratio: {best_estimate['ltv_cac_ratio']:.1f}:1\")\n", "print(f\"\\n💡 Target: 3:1 ratio for healthy SaaS businesses\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Rule of 40 Analysis"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.643748Z", "iopub.status.busy": "2025-07-29T07:55:47.643682Z", "iopub.status.idle": "2025-07-29T07:55:47.659291Z", "shell.execute_reply": "2025-07-29T07:55:47.659088Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"line": {"width": 2}, "mode": "lines+markers", "name": "-20% Profit <PERSON>", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "y": {"bdata": "mpmZmZmZRsAzMzMzMzNNQDMzMzMzC3NAmpmZmZnBcEA=", "dtype": "f8"}}, {"line": {"width": 2}, "mode": "lines+markers", "name": "-10% Profit <PERSON>", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "y": {"bdata": "mpmZmZmZQcCamZmZmRlRQDMzMzMzq3NAmpmZmZlhcUA=", "dtype": "f8"}}, {"line": {"width": 2}, "mode": "lines+markers", "name": "0% Profit <PERSON>gin", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "y": {"bdata": "MzMzMzMzOcCamZmZmZlTQDMzMzMzS3RAmpmZmZkBckA=", "dtype": "f8"}}, {"line": {"width": 2}, "mode": "lines+markers", "name": "10% Profit <PERSON>", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "y": {"bdata": "ZmZmZmZmLsCamZmZmRlWQDMzMzMz63RAmpmZmZmhckA=", "dtype": "f8"}}, {"line": {"width": 2}, "mode": "lines+markers", "name": "20% Profit <PERSON>gin", "type": "scatter", "x": {"bdata": "5QfmB+cH6Ac=", "dtype": "i2"}, "y": {"bdata": "zczMzMzMFMCamZmZmZlYQDMzMzMzi3VAmpmZmZlBc0A=", "dtype": "f8"}}], "layout": {"annotations": [{"showarrow": false, "text": "Rule of 40 Threshold", "x": 1, "xanchor": "right", "xref": "x domain", "y": 40, "yanchor": "bottom", "yref": "y"}], "height": 500, "hovermode": "x unified", "shapes": [{"line": {"color": "green", "dash": "dash"}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 40, "y1": 40, "yref": "y"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Rule of 40 Analysis: Growth + Profitability"}, "xaxis": {"title": {"text": "Year"}}, "yaxis": {"title": {"text": "Rule of 40 Score"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 2024 Rule of 40 Analysis:\n", "- Revenue Growth Rate: 288.1%\n", "- Estimated Profit Margin: 0.0%\n", "- Rule of 40 Score: 288.1\n", "- Excellent - Meets Rule of 40 benchmark\n"]}], "source": ["# Calculate Rule of 40 for different scenarios\n", "rule_of_40_scenarios = []\n", "\n", "# Historical years with different profit margin assumptions\n", "for year in range(2021, 2025):\n", "    for profit_margin in [-20, -10, 0, 10, 20]:\n", "        rule_40 = metrics.calculate_rule_of_40(year=year, estimated_profit_margin=profit_margin)\n", "        rule_of_40_scenarios.append(rule_40)\n", "\n", "rule_40_df = pd.DataFrame(rule_of_40_scenarios)\n", "\n", "# Create Rule of 40 visualization\n", "fig = go.Figure()\n", "\n", "# Add traces for each profit margin scenario\n", "for margin in [-20, -10, 0, 10, 20]:\n", "    data = rule_40_df[rule_40_df['profit_margin'] == margin]\n", "    fig.add_trace(\n", "        go.<PERSON>er(x=data['year'], y=data['rule_of_40_score'],\n", "                   mode='lines+markers', name=f'{margin}% Profit Margin',\n", "                   line=dict(width=2))\n", "    )\n", "\n", "# Add Rule of 40 threshold\n", "fig.add_hline(y=40, line_dash=\"dash\", line_color=\"green\", \n", "              annotation_text=\"Rule of 40 Threshold\")\n", "\n", "# Update layout\n", "fig.update_layout(\n", "    title=\"Rule of 40 Analysis: Growth + Profitability\",\n", "    xaxis_title=\"Year\",\n", "    yaxis_title=\"Rule of 40 Score\",\n", "    height=500,\n", "    hovermode='x unified'\n", ")\n", "\n", "fig.show()\n", "\n", "# Current year analysis\n", "current_rule_40 = metrics.calculate_rule_of_40(year=2024, estimated_profit_margin=0)\n", "print(f\"\\n📊 2024 Rule of 40 Analysis:\")\n", "print(f\"- Revenue Growth Rate: {current_rule_40['revenue_growth_rate']:.1f}%\")\n", "print(f\"- Estimated Profit Margin: {current_rule_40['profit_margin']:.1f}%\")\n", "print(f\"- Rule of 40 Score: {current_rule_40['rule_of_40_score']:.1f}\")\n", "print(f\"- {current_rule_40['interpretation']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Executive Summary & Valuation Implications"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.660409Z", "iopub.status.busy": "2025-07-29T07:55:47.660336Z", "iopub.status.idle": "2025-07-29T07:55:47.685722Z", "shell.execute_reply": "2025-07-29T07:55:47.685517Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"delta": {"reference": 11937, "relative": true}, "domain": {"x": [0.0, 0.2888888888888889], "y": [0.8, 1.0]}, "mode": "number+delta", "number": {"prefix": "£", "valueformat": ",.0f"}, "title": {"text": "2024 ARR"}, "type": "indicator", "value": 262466.1}, {"delta": {"reference": 2}, "domain": {"x": [0.35555555555555557, 0.6444444444444445], "y": [0.8, 1.0]}, "mode": "number+delta", "title": {"text": "Total Customers"}, "type": "indicator", "value": 11.0}, {"domain": {"x": [0.7111111111111111, 1.0], "y": [0.8, 1.0]}, "gauge": {"axis": {"range": [80, 120]}, "bar": {"color": "darkblue"}, "steps": [{"color": "lightgray", "range": [80, 100]}, {"color": "gray", "range": [100, 110]}, {"color": "lightgreen", "range": [110, 120]}]}, "mode": "gauge+number", "title": {"text": "Net Dollar Retention"}, "type": "indicator", "value": 491.7}, {"domain": {"x": [0.0, 0.2888888888888889], "y": [0.43333333333333335, 0.6333333333333333]}, "mode": "number", "number": {"suffix": "/100"}, "title": {"text": "Revenue Quality Score"}, "type": "indicator", "value": 70.3}, {"domain": {"x": [0.35555555555555557, 0.6444444444444445], "y": [0.43333333333333335, 0.6333333333333333]}, "mode": "number", "number": {"suffix": ":1"}, "title": {"text": "LTV:CAC Ratio"}, "type": "indicator", "value": 3.55}, {"domain": {"x": [0.7111111111111111, 1.0], "y": [0.43333333333333335, 0.6333333333333333]}, "mode": "number", "number": {"suffix": "%"}, "title": {"text": "Rule of 40 Score"}, "type": "indicator", "value": 288.1}, {"domain": {"x": [0.0, 0.2888888888888889], "y": [0.0, 0.2666666666666667]}, "mode": "number", "number": {"valueformat": ".4f"}, "title": {"text": "HHI Concentration"}, "type": "indicator", "value": 0.4436}, {"marker": {"color": ["lightcoral", "lightblue", "lightgreen"]}, "text": ["£1.3M", "£1.7M", "£2.1M"], "textposition": "outside", "type": "bar", "x": ["Low (5x)", "Mid (6.5x)", "High (8x)"], "xaxis": "x", "y": [1312330.0, 1706030.0, 2099729.0], "yaxis": "y"}, {"line": {"color": "blue", "width": 3}, "mode": "lines+markers", "name": "Historic", "type": "scatter", "x": {"bdata": "5AflB+YH5wfoBw==", "dtype": "i2"}, "xaxis": "x2", "y": {"bdata": "AAAAAMBQx0AK16NwvW7BQKRwPQp3Gc9AXI/C9aCC8EBmZmZmCAUQQQ==", "dtype": "f8"}, "yaxis": "y2"}, {"line": {"color": "green", "dash": "dash", "width": 3}, "mode": "lines+markers", "name": "Projected", "type": "scatter", "x": [2025, 2026, 2027], "xaxis": "x2", "y": [393699.14999999997, 590548.725, 885823.0874999999], "yaxis": "y2"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "Revenue Growth", "x": 0.14444444444444446, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Customer Growth", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "NDR Trend", "x": 0.8555555555555556, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Revenue Quality", "x": 0.14444444444444446, "xanchor": "center", "xref": "paper", "y": 0.6333333333333333, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "LTV:CAC Ratio", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.6333333333333333, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Rule of 40", "x": 0.8555555555555556, "xanchor": "center", "xref": "paper", "y": 0.6333333333333333, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Concentration Risk", "x": 0.14444444444444446, "xanchor": "center", "xref": "paper", "y": 0.2666666666666667, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Valuation Range", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.2666666666666667, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Growth Trajectory", "x": 0.8555555555555556, "xanchor": "center", "xref": "paper", "y": 0.2666666666666667, "yanchor": "bottom", "yref": "paper"}], "height": 1000, "showlegend": false, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"size": 24}, "text": "MCX3D Valuation Metrics Dashboard"}, "xaxis": {"anchor": "y", "domain": [0.35555555555555557, 0.6444444444444445]}, "xaxis2": {"anchor": "y2", "domain": [0.7111111111111111, 1.0]}, "yaxis": {"anchor": "x", "domain": [0.0, 0.2666666666666667]}, "yaxis2": {"anchor": "x2", "domain": [0.0, 0.2666666666666667]}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "EXECUTIVE SUMMARY - MCX3D VALUATION METRICS\n", "================================================================================\n", "\n", "📈 Growth Metrics:\n", "   • Revenue Growth (2020-2024): 2099%\n", "   • Current ARR: £262,466\n", "   • Customer Base: 11.0 clients\n", "   • Average Revenue per Customer: £23,861\n", "\n", "🎯 SaaS Health Indicators:\n", "   • Net Dollar Retention: 491.7%\n", "   • LTV:CAC Ratio: 3.5:1\n", "   • Rule of 40 Score: 288.1\n", "   • Revenue Quality Score: 70.3/100\n", "\n", "⚠️  Risk Factors:\n", "   • Customer Concentration (HHI): 0.444\n", "   • Top 5 Customer Revenue Share: 85.4%\n", "   • Recurring Revenue: 92.9%\n", "\n", "💰 Valuation Range:\n", "   • Conservative (5x ARR): £1,312,330\n", "   • Mid-Range (6.5x ARR): £1,706,030\n", "   • Optimistic (8x ARR): £2,099,729\n", "\n", "🚀 Path to £1M ARR:\n", "   • Current Run Rate: £262,466\n", "   • Required Growth: 281%\n", "   • Timeline at 50% YoY Growth: ~2-3 years\n", "\n", "================================================================================\n"]}], "source": ["# Create executive dashboard\n", "fig = make_subplots(\n", "    rows=3, cols=3,\n", "    subplot_titles=('Revenue Growth', 'Customer Growth', 'NDR Trend',\n", "                   'Revenue Quality', 'LTV:CAC Ratio', 'Rule of 40',\n", "                   'Concentration Risk', 'Valuation Range', 'Growth Trajectory'),\n", "    specs=[[{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],\n", "           [{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],\n", "           [{'type': 'indicator'}, {'type': 'bar'}, {'type': 'scatter'}]],\n", "    row_heights=[0.3, 0.3, 0.4]\n", ")\n", "\n", "# Key metrics\n", "summary = valuation_summary\n", "latest_ndr = metrics.calculate_ndr(2023, 2024)\n", "concentration = metrics.calculate_revenue_concentration()\n", "ltv_cac = metrics.calculate_ltv_cac_implied()\n", "rule_40 = metrics.calculate_rule_of_40()\n", "quality = metrics.calculate_revenue_quality_score()\n", "\n", "# Revenue growth indicator\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number+delta\",\n", "        value=summary['revenue_metrics']['arr_2024'],\n", "        title={\"text\": \"2024 ARR\"},\n", "        delta={'reference': 11937, 'relative': True},\n", "        number={'prefix': \"£\", 'valueformat': \",.0f\"}\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Customer growth\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number+delta\",\n", "        value=summary['revenue_metrics']['customer_count'],\n", "        title={\"text\": \"Total Customers\"},\n", "        delta={'reference': 2}\n", "    ),\n", "    row=1, col=2\n", ")\n", "\n", "# NDR\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"gauge+number\",\n", "        value=latest_ndr['ndr'],\n", "        title={\"text\": \"Net Dollar Retention\"},\n", "        gauge={'axis': {'range': [80, 120]},\n", "               'bar': {'color': \"darkblue\"},\n", "               'steps': [\n", "                   {'range': [80, 100], 'color': \"lightgray\"},\n", "                   {'range': [100, 110], 'color': \"gray\"},\n", "                   {'range': [110, 120], 'color': \"lightgreen\"}]}\n", "    ),\n", "    row=1, col=3\n", ")\n", "\n", "# Revenue quality\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=quality['revenue_quality_score'],\n", "        title={\"text\": \"Revenue Quality Score\"},\n", "        number={'suffix': \"/100\"}\n", "    ),\n", "    row=2, col=1\n", ")\n", "\n", "# LTV:CAC\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=ltv_cac['ltv_cac_ratio'],\n", "        title={\"text\": \"LTV:CAC Ratio\"},\n", "        number={'suffix': \":1\"}\n", "    ),\n", "    row=2, col=2\n", ")\n", "\n", "# Rule of 40\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=rule_40['rule_of_40_score'],\n", "        title={\"text\": \"Rule of 40 Score\"},\n", "        number={'suffix': \"%\"}\n", "    ),\n", "    row=2, col=3\n", ")\n", "\n", "# Concentration risk\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode=\"number\",\n", "        value=concentration['hhi'],\n", "        title={\"text\": \"HHI Concentration\"},\n", "        number={'valueformat': \".4f\"}\n", "    ),\n", "    row=3, col=1\n", ")\n", "\n", "# Valuation range\n", "valuation_data = summary['valuation_indicators']\n", "fig.add_trace(\n", "    go.Bar(\n", "        x=['Low (5x)', 'Mid (6.5x)', 'High (8x)'],\n", "        y=[valuation_data['implied_valuation_low'],\n", "           valuation_data['implied_valuation_mid'],\n", "           valuation_data['implied_valuation_high']],\n", "        text=[f\"£{v/1e6:.1f}M\" for v in [valuation_data['implied_valuation_low'],\n", "                                         valuation_data['implied_valuation_mid'],\n", "                                         valuation_data['implied_valuation_high']]],\n", "        textposition='outside',\n", "        marker_color=['lightcoral', 'lightblue', 'lightgreen']\n", "    ),\n", "    row=3, col=2\n", ")\n", "\n", "# Growth trajectory\n", "growth_df = metrics.calculate_growth_accounting()\n", "future_years = [2025, 2026, 2027]\n", "future_revenue = [growth_df['total_revenue'].iloc[-1] * (1.5 ** i) for i in range(1, 4)]\n", "\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=growth_df['year'], y=growth_df['total_revenue'],\n", "               mode='lines+markers', name='Historic',\n", "               line=dict(color='blue', width=3)),\n", "    row=3, col=3\n", ")\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=future_years, y=future_revenue,\n", "               mode='lines+markers', name='Projected',\n", "               line=dict(color='green', width=3, dash='dash')),\n", "    row=3, col=3\n", ")\n", "\n", "# Update layout\n", "fig.update_layout(height=1000, showlegend=False,\n", "                  title_text=\"MCX3D Valuation Metrics Dashboard\",\n", "                  title_font_size=24)\n", "\n", "fig.show()\n", "\n", "# Print executive summary\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EXECUTIVE SUMMARY - MCX3D VALUATION METRICS\")\n", "print(\"=\"*80)\n", "print(f\"\\n📈 Growth Metrics:\")\n", "print(f\"   • Revenue Growth (2020-2024): {summary['revenue_metrics']['revenue_growth_2020_2024']:.0f}%\")\n", "print(f\"   • Current ARR: £{summary['revenue_metrics']['arr_2024']:,.0f}\")\n", "print(f\"   • Customer Base: {summary['revenue_metrics']['customer_count']} clients\")\n", "print(f\"   • Average Revenue per Customer: £{summary['revenue_metrics']['avg_revenue_per_customer']:,.0f}\")\n", "\n", "print(f\"\\n🎯 SaaS Health Indicators:\")\n", "print(f\"   • Net Dollar Retention: {summary['saas_health_metrics']['ndr']:.1f}%\")\n", "print(f\"   • LTV:CAC Ratio: {summary['saas_health_metrics']['ltv_cac_ratio']:.1f}:1\")\n", "print(f\"   • Rule of 40 Score: {summary['saas_health_metrics']['rule_of_40_score']:.1f}\")\n", "print(f\"   • Revenue Quality Score: {summary['saas_health_metrics']['revenue_quality_score']:.1f}/100\")\n", "\n", "print(f\"\\n⚠️  Risk Factors:\")\n", "print(f\"   • Customer Concentration (HHI): {summary['risk_metrics']['customer_concentration_hhi']:.3f}\")\n", "print(f\"   • Top 5 Customer Revenue Share: {summary['risk_metrics']['top_5_customer_share']:.1f}%\")\n", "print(f\"   • Recurring Revenue: {summary['risk_metrics']['recurring_revenue_percentage']:.1f}%\")\n", "\n", "print(f\"\\n💰 Valuation Range:\")\n", "print(f\"   • Conservative (5x ARR): £{summary['valuation_indicators']['implied_valuation_low']:,.0f}\")\n", "print(f\"   • Mid-Range (6.5x ARR): £{summary['valuation_indicators']['implied_valuation_mid']:,.0f}\")\n", "print(f\"   • Optimistic (8x ARR): £{summary['valuation_indicators']['implied_valuation_high']:,.0f}\")\n", "\n", "print(f\"\\n🚀 Path to £1M ARR:\")\n", "print(f\"   • Current Run Rate: £{summary['revenue_metrics']['arr_2024']:,.0f}\")\n", "print(f\"   • Required Growth: {(1_000_000 / summary['revenue_metrics']['arr_2024'] - 1) * 100:.0f}%\")\n", "print(f\"   • Timeline at 50% YoY Growth: ~2-3 years\")\n", "print(\"\\n\" + \"=\"*80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Export Metrics for Valuation Report"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:55:47.686744Z", "iopub.status.busy": "2025-07-29T07:55:47.686676Z", "iopub.status.idle": "2025-07-29T07:55:47.692347Z", "shell.execute_reply": "2025-07-29T07:55:47.692158Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Metrics saved to: ../data/derived_metrics/historic_growth_metrics.json\n", "\n", "📊 All metrics exported successfully!\n", "\n", "Next steps:\n", "1. Review the generated metrics and visualizations\n", "2. Prepare the Historic Growth Chapter narrative\n", "3. Create DCF model using these baseline metrics\n", "4. Develop comparable company analysis\n"]}], "source": ["# Save all calculated metrics\n", "import os\n", "\n", "# Create output directory\n", "output_dir = '../data/derived_metrics'\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Save comprehensive metrics\n", "all_metrics = {\n", "    'valuation_summary': valuation_summary,\n", "    'growth_accounting': growth_accounting.to_dict('records'),\n", "    'ndr_analysis': cohort_df.to_dict('records'),\n", "    'concentration_metrics': concentration,\n", "    'revenue_quality': revenue_quality,\n", "    'ltv_cac_analysis': ltv_df.to_dict('records'),\n", "    'rule_of_40': rule_40_df.to_dict('records')\n", "}\n", "\n", "# Save to JSON\n", "output_path = os.path.join(output_dir, 'historic_growth_metrics.json')\n", "with open(output_path, 'w') as f:\n", "    json.dump(all_metrics, f, indent=2, default=str)\n", "\n", "print(f\"✅ Metrics saved to: {output_path}\")\n", "\n", "# Save key tables as CSV for easy import\n", "growth_accounting.to_csv(os.path.join(output_dir, 'growth_accounting.csv'), index=False)\n", "cohort_df.to_csv(os.path.join(output_dir, 'ndr_cohort_analysis.csv'), index=False)\n", "ltv_df.to_csv(os.path.join(output_dir, 'ltv_cac_scenarios.csv'), index=False)\n", "\n", "print(\"\\n📊 All metrics exported successfully!\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Review the generated metrics and visualizations\")\n", "print(\"2. Prepare the Historic Growth Chapter narrative\")\n", "print(\"3. Create DCF model using these baseline metrics\")\n", "print(\"4. Develop comparable company analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
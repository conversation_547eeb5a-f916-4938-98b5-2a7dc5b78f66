{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MCX3D Client Evolution Story: From 2 to 22 Clients\n", "\n", "## A Data-Driven Journey Through 5 Years of Growth\n", "\n", "This notebook tells the comprehensive story of MCX3D Ltd's transformation from a 3D modeling service to a thriving SaaS platform, through the lens of actual client payment data. We'll explore how 22 diverse clients across multiple industries helped shape the company's evolution.\n", "\n", "**Key Highlights:**\n", "- 📈 Revenue Growth: £11,937 (2020) → £262,466 (2024) - **2,098% increase**\n", "- 🏢 Client Growth: 2 → 22 clients across 10+ industries\n", "- 💰 Contract Evolution: £600 average → £10,419 average (<PERSON>)\n", "- 🌍 Geographic Expansion: Lebanon → UK → Netherlands → Global"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:36.766212Z", "iopub.status.busy": "2025-07-29T07:46:36.765850Z", "iopub.status.idle": "2025-07-29T07:46:37.221887Z", "shell.execute_reply": "2025-07-29T07:46:37.221589Z"}}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('default')  # Use default style that's always available\n", "sns.set_palette(\"husl\")\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', lambda x: '£{:,.2f}'.format(x) if abs(x) > 0.01 else '£0.00')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.223248Z", "iopub.status.busy": "2025-07-29T07:46:37.223133Z", "iopub.status.idle": "2025-07-29T07:46:37.227989Z", "shell.execute_reply": "2025-07-29T07:46:37.227789Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Clients: 22\n", "Total Transactions: 146\n", "Analysis Period: 2020-2025\n"]}], "source": ["# Load the revenue data\n", "with open('../data/revenue/customer_revenue_analysis.json', 'r') as f:\n", "    customer_data = json.load(f)\n", "\n", "with open('../data/revenue/revenue_dataset_2020_2025.json', 'r') as f:\n", "    revenue_data = json.load(f)\n", "\n", "# Extract detailed transactions\n", "transactions = revenue_data.get('detailed_transactions', [])\n", "\n", "# Convert to DataFrame\n", "df_transactions = pd.DataFrame(transactions)\n", "df_transactions['date'] = pd.to_datetime(df_transactions['date'])\n", "df_transactions['year'] = df_transactions['date'].dt.year\n", "df_transactions['month'] = df_transactions['date'].dt.to_period('M')\n", "\n", "print(f\"Total Clients: {customer_data['customer_count']}\")\n", "print(f\"Total Transactions: {len(transactions)}\")\n", "print(f\"Analysis Period: {customer_data['analysis_period']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 1: The Beginning (2020-2021) - Finding Product-Market Fit\n", "\n", "MCX3D's journey began with a single client belief in their vision. Let's explore how the early adopters shaped the company's direction."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.243396Z", "iopub.status.busy": "2025-07-29T07:46:37.243297Z", "iopub.status.idle": "2025-07-29T07:46:37.249256Z", "shell.execute_reply": "2025-07-29T07:46:37.249064Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Enhanced Client Data Status:\n", "   • Total companies: 21\n", "   • Industries represented: 15\n", "   • Enterprise clients: 7\n", "\n", "🔧 MCX3D Technology Contributions:\n", "   • ongoing_operational: 1 documented\n", "   • platform_integrations: 1 documented\n", "\n", "🤝 Business Relationship Intelligence:\n", "   • entrepreneur_continuity: documented entrepreneur continuity patterns\n", "\n", "🏷️  Brand Name Revelations:\n", "   • ESC S.A.L → Patchit (high-end chocolate)\n", "   • Lines and Arts s.a.r.l → Bobeli Printing Boutique\n", "   • <PERSON> - Sin Dental → S.I.N. 360 (dental technology)\n", "\n", "✅ Using validated client data with business intelligence and technology contributions\n", "📝 Data source: Client verification + validated online research\n", "\n", "⚠️  Warning: 2 customers not found in industry mapping:\n", "   • L’atelier Nawbar\n", "   • Chill <PERSON>\n"]}], "source": ["# Load validated and enhanced industry classifications with business intelligence\n", "import sys\n", "import os\n", "\n", "# Add scripts directory to path\n", "sys.path.append('../scripts')\n", "from industry_data_loader import IndustryClassificationLoader\n", "\n", "# Load enhanced client data with business intelligence\n", "industry_loader = IndustryClassificationLoader()\n", "\n", "# Get industry mapping and enhanced data\n", "client_industries = industry_loader.get_industry_mapping()\n", "enhanced_client_data = industry_loader.get_enhanced_client_data()\n", "mcx3d_business_intel = industry_loader.get_mcx3d_business_intelligence()\n", "\n", "# Add data source attribution to our analysis\n", "data_attribution = industry_loader.add_data_source_attribution({})\n", "\n", "# Display enhanced data status\n", "industry_analysis = industry_loader.get_industry_analysis()\n", "print(f\"📊 Enhanced Client Data Status:\")\n", "print(f\"   • Total companies: {len(client_industries)}\")\n", "print(f\"   • Industries represented: {industry_analysis.get('total_industries', 0)}\")\n", "print(f\"   • Enterprise clients: {industry_analysis.get('client_type_distribution', {}).get('Enterprise', 0)}\")\n", "\n", "# Show MCX3D business intelligence highlights\n", "tech_contributions = mcx3d_business_intel.get('technology_contributions', {})\n", "if tech_contributions:\n", "    print(f\"\\n🔧 MCX3D Technology Contributions:\")\n", "    for category, items in tech_contributions.items():\n", "        print(f\"   • {category}: {len(items)} documented\")\n", "\n", "business_relationships = mcx3d_business_intel.get('business_relationships', {})\n", "if business_relationships:\n", "    print(f\"\\n🤝 Business Relationship Intelligence:\")\n", "    for category, items in business_relationships.items():\n", "        print(f\"   • {category}: documented entrepreneur continuity patterns\")\n", "\n", "brand_mappings = mcx3d_business_intel.get('brand_name_mappings', {})\n", "if brand_mappings:\n", "    print(f\"\\n🏷️  Brand Name Revelations:\")\n", "    for legal_name, brand in brand_mappings.items():\n", "        print(f\"   • {legal_name} → {brand}\")\n", "\n", "print(f\"\\n✅ Using validated client data with business intelligence and technology contributions\")\n", "print(f\"📝 Data source: {data_attribution['_data_sources']['industry_classifications']['source']}\")\n", "\n", "# Add industry to transactions (same as before, but now with enhanced accuracy)\n", "df_transactions['industry'] = df_transactions['customer'].map(client_industries)\n", "\n", "# Map the category field to revenue_type for compatibility\n", "df_transactions['revenue_type'] = df_transactions['category']\n", "\n", "# Report any unmapped customers\n", "unmapped_customers = df_transactions[df_transactions['industry'].isna()]['customer'].unique()\n", "if len(unmapped_customers) > 0:\n", "    print(f\"\\n⚠️  Warning: {len(unmapped_customers)} customers not found in industry mapping:\")\n", "    for customer in unmapped_customers:\n", "        print(f\"   • {customer}\")\n", "else:\n", "    print(f\"\\n✅ All customers successfully mapped to verified industries\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.250276Z", "iopub.status.busy": "2025-07-29T07:46:37.250190Z", "iopub.status.idle": "2025-07-29T07:46:37.327324Z", "shell.execute_reply": "2025-07-29T07:46:37.327077Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 The Early Believers (2020-2021):\n", "\n", "                   Total Revenue  Transactions  Avg Transaction  \\\n", "customer                                                          \n", "L’atelier Nawbar      £12,237.50             3        £4,079.17   \n", "L'atelier Nawbar       £7,063.00             7        £1,009.00   \n", "KITU KALI LIMITED        £960.00             1          £960.00   \n", "Packegha                 £602.48             1          £602.48   \n", "\n", "                        First Payment        Last Payment  \n", "customer                                                   \n", "L’atelier Nawbar  2020-11-12 02:00:00 2021-12-10 02:00:00  \n", "L'atelier Nawbar  2021-06-09 03:00:00 2021-11-02 02:00:00  \n", "KITU KALI LIMITED 2021-08-23 03:00:00 2021-08-23 03:00:00  \n", "Packegha          2021-11-26 02:00:00 2021-11-26 02:00:00  \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Early years analysis (2020-2021)\n", "early_years = df_transactions[df_transactions['year'].isin([2020, 2021])]\n", "early_clients = early_years.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'date': ['min', 'max']\n", "}).round(2)\n", "\n", "early_clients.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'First Payment', 'Last Payment']\n", "early_clients = early_clients.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🚀 The Early Believers (2020-2021):\\n\")\n", "print(early_clients)\n", "\n", "# Visualize early client contributions\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Revenue by client\n", "early_revenue = early_years.groupby('customer')['amount'].sum().sort_values(ascending=True)\n", "early_revenue.plot(kind='barh', ax=ax1, color='skyblue')\n", "ax1.set_title('Early Client Revenue Contributions (2020-2021)', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Revenue (£)')\n", "\n", "# Revenue type distribution\n", "early_type = early_years.groupby('revenue_type')['amount'].sum()\n", "early_type.plot(kind='pie', ax=ax2, autopct='%1.1f%%', startangle=90)\n", "ax2.set_title('Revenue Model in Early Years', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Insights from the Early Years:\n", "\n", "1. **L'atelier Nawbar** was the founding client, contributing 86% of 2020 revenue with modeling fees\n", "2. **The Pivot Moment**: In 2021, the company shifted from pure modeling fees to a sales model\n", "3. **Early Validation**: 4 diverse clients across jewelry, fashion, and packaging industries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 2: The Pivot (2022) - Diversification & Experimentation\n", "\n", "2022 marked a critical year of experimentation as MCX3D tested their platform across various industries."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.328564Z", "iopub.status.busy": "2025-07-29T07:46:37.328500Z", "iopub.status.idle": "2025-07-29T07:46:37.332821Z", "shell.execute_reply": "2025-07-29T07:46:37.332631Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 The Pivot Year - 2022 Client Portfolio:\n", "\n", "                        Total Revenue  Transactions  Avg Transaction  \\\n", "customer                                                               \n", "L’atelier Nawbar            £4,980.00             8          £622.50   \n", "L'atelier Nawbar            £4,960.00             8          £620.00   \n", "KITU KALI LIMITED           £1,840.00             8          £230.00   \n", "Packegha                    £1,513.93             4          £378.48   \n", "Lines and Arts s.a.r.l      £1,200.00             3          £400.00   \n", "High Tech XL Group B.V        £800.00             1          £800.00   \n", "ESC S.A.L                     £429.00             1          £429.00   \n", "INCORP                        £200.00             1          £200.00   \n", "\n", "                                          Industry  \n", "customer                                            \n", "L’atelier Nawbar                              None  \n", "L'atelier Nawbar                  Jewelry & Luxury  \n", "KITU KALI LIMITED                Fashion & Apparel  \n", "Packegha                        Packaging & Design  \n", "Lines and Arts s.a.r.l  Printing & Design Services  \n", "High Tech XL Group B.V                  Technology  \n", "ESC S.A.L                          Food & Beverage  \n", "INCORP                           Business Services  \n", "\n", "New Industries Entered: 7\n", "Average Contract Size: £468.32\n"]}], "source": ["# 2022 Analysis\n", "year_2022 = df_transactions[df_transactions['year'] == 2022]\n", "clients_2022 = year_2022.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'industry': 'first'\n", "}).round(2)\n", "\n", "clients_2022.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Industry']\n", "clients_2022 = clients_2022.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🔄 The Pivot Year - 2022 Client Portfolio:\\n\")\n", "print(clients_2022)\n", "print(f\"\\nNew Industries Entered: {clients_2022['Industry'].nunique()}\")\n", "print(f\"Average Contract Size: £{year_2022['amount'].mean():,.2f}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.333877Z", "iopub.status.busy": "2025-07-29T07:46:37.333807Z", "iopub.status.idle": "2025-07-29T07:46:37.881705Z", "shell.execute_reply": "2025-07-29T07:46:37.881478Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"line": {"color": "blue", "width": 3}, "marker": {"size": 10}, "mode": "lines+markers", "name": "Cumulative Clients", "type": "scatter", "x": {"bdata": "5AcAAOUHAADmBwAA5wcAAOgHAAA=", "dtype": "i4"}, "y": {"bdata": "AQQIERY=", "dtype": "i1"}}, {"marker": {"color": "lightblue"}, "name": "New Clients", "type": "bar", "x": {"bdata": "5AcAAOUHAADmBwAA5wcAAOgHAAA=", "dtype": "i4"}, "y": {"bdata": "AQMECQU=", "dtype": "i1"}, "yaxis": "y2"}], "layout": {"height": 500, "hovermode": "x unified", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Client Acquisition Journey: From 2 to 22"}, "xaxis": {"title": {"text": "Year"}}, "yaxis": {"title": {"text": "Cumulative Client Count"}}, "yaxis2": {"overlaying": "y", "side": "right", "title": {"text": "New Clients per Year"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create client acquisition timeline\n", "client_first_payment = df_transactions.groupby('customer')['date'].min().reset_index()\n", "client_first_payment['year'] = client_first_payment['date'].dt.year\n", "client_first_payment['industry'] = client_first_payment['customer'].map(client_industries)\n", "\n", "# Cumulative client count\n", "cumulative_clients = client_first_payment.groupby('year').size().cumsum()\n", "\n", "# Visualize client acquisition\n", "fig = go.Figure()\n", "\n", "# Add cumulative line\n", "fig.add_trace(go.<PERSON>(\n", "    x=cumulative_clients.index,\n", "    y=cumulative_clients.values,\n", "    mode='lines+markers',\n", "    name='Cumulative Clients',\n", "    line=dict(color='blue', width=3),\n", "    marker=dict(size=10)\n", "))\n", "\n", "# Add new clients per year bars\n", "new_clients_per_year = client_first_payment.groupby('year').size()\n", "fig.add_trace(go.Bar(\n", "    x=new_clients_per_year.index,\n", "    y=new_clients_per_year.values,\n", "    name='New Clients',\n", "    marker_color='lightblue',\n", "    yaxis='y2'\n", "))\n", "\n", "# Update layout\n", "fig.update_layout(\n", "    title='Client Acquisition Journey: From 2 to 22',\n", "    xaxis_title='Year',\n", "    yaxis_title='Cumulative Client Count',\n", "    yaxis2=dict(title='New Clients per Year', overlaying='y', side='right'),\n", "    hovermode='x unified',\n", "    height=500\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 3: The Breakthrough (2023) - Market Validation\n", "\n", "2023 was the year everything changed. The entry of <PERSON> and rapid expansion across industries validated MCX3D's platform approach."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.882915Z", "iopub.status.busy": "2025-07-29T07:46:37.882845Z", "iopub.status.idle": "2025-07-29T07:46:37.888103Z", "shell.execute_reply": "2025-07-29T07:46:37.887898Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 The Breakthrough Year - 2023:\n", "\n", "Total Revenue: £67,626.06\n", "Client Count: 13\n", "Average Contract: £1,470.13\n", "\n", "Top 5 Clients:\n", "                          Total Revenue               Industry  Transactions\n", "customer                                                                    \n", "<PERSON>              £20,400.00    E-commerce Platform             4\n", "L'atelier Nawbar             £10,680.00       Jewelry & Luxury            13\n", "Buff BBQ                     £10,000.00      Outdoor Equipment             2\n", "ESC S.A.L                     £7,200.00        Food & Beverage             1\n", "<PERSON> - Sin Dental      £7,100.06  Healthcare Technology             3\n", "\n", "⭐ <PERSON>'s Entry:\n", "   First Contract: £10,000.00\n", "   Total 2023 Revenue: £20,400.00\n", "   Revenue Share: 30.2%\n"]}], "source": ["# 2023 Deep Dive\n", "year_2023 = df_transactions[df_transactions['year'] == 2023]\n", "\n", "# Calculate key metrics\n", "clients_2023 = year_2023.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'industry': 'first',\n", "    'date': ['min', 'max']\n", "})\n", "\n", "clients_2023.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Industry', 'First Payment', 'Last Payment']\n", "clients_2023 = clients_2023.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🚀 The Breakthrough Year - 2023:\\n\")\n", "print(f\"Total Revenue: £{year_2023['amount'].sum():,.2f}\")\n", "print(f\"Client Count: {year_2023['customer'].nunique()}\")\n", "print(f\"Average Contract: £{year_2023['amount'].mean():,.2f}\")\n", "print(f\"\\nTop 5 Clients:\")\n", "print(clients_2023.head()[['Total Revenue', 'Industry', 'Transactions']])\n", "\n", "# Highlight <PERSON>'s entry\n", "devin_2023 = year_2023[year_2023['customer'] == '<PERSON>']\n", "print(f\"\\n⭐ <PERSON>'s Entry:\")\n", "print(f\"   First Contract: £{devin_2023['amount'].iloc[0]:,.2f}\")\n", "print(f\"   Total 2023 Revenue: £{devin_2023['amount'].sum():,.2f}\")\n", "print(f\"   Revenue Share: {(devin_2023['amount'].sum() / year_2023['amount'].sum() * 100):.1f}%\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:37.889237Z", "iopub.status.busy": "2025-07-29T07:46:37.889160Z", "iopub.status.idle": "2025-07-29T07:46:38.022910Z", "shell.execute_reply": "2025-07-29T07:46:38.022694Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"fillpattern": {"shape": ""}, "hovertemplate": "industry=Fashion & Apparel<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Fashion & Apparel", "line": {"color": "#636efa"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Fashion & Apparel", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5QcAAOYHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAAAAjkAAAAAAAMCcQA==", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Jewelry & Luxury<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Jewelry & Luxury", "line": {"color": "#EF553B"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Jewelry & Luxury", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5QcAAOYHAADnBwAA6AcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAACXu0AAAAAAAGCzQAAAAAAA3MRAAAAAAABwp0A=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Packaging & Design<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Packaging & Design", "line": {"color": "#00cc96"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Packaging & Design", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5QcAAOYHAADnBwAA6AcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "pHA9CtfTgkAehetRuKeXQAAAAAAAyKlAAAAAAADIqUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Business Services<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Business Services", "line": {"color": "#ab63fa"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Business Services", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5gcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAAAAaUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Food & Beverage<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Food & Beverage", "line": {"color": "#FFA15A"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Food & Beverage", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5gcAAOcHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADQekAAAAAAACC8QA==", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Printing & Design Services<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Printing & Design Services", "line": {"color": "#19d3f3"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Printing & Design Services", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5gcAAOcHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADAkkAAAAAAAJCKQA==", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Technology<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Technology", "line": {"color": "#FF6692"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Technology", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5gcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAAAAiUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Beauty & Cosmetics<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Beauty & Cosmetics", "line": {"color": "#B6E880"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Beauty & Cosmetics", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADgdUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Digital Marketing<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Digital Marketing", "line": {"color": "#FF97FF"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Digital Marketing", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAABAb0A=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Digital Services<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Digital Services", "line": {"color": "#FECB52"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Digital Services", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAABAb0A=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=E-commerce Platform<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "E-commerce Platform", "line": {"color": "#636efa"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "E-commerce Platform", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAOgHAADpBwAA", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADs00AAAAAAhMIKQUjhehReX9RA", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Healthcare Technology<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Healthcare Technology", "line": {"color": "#EF553B"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Healthcare Technology", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAOgHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "wvUoXA+8u0AAAAAAANSqQA==", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Lighting & Interior<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Lighting & Interior", "line": {"color": "#00cc96"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Lighting & Interior", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAAAwoUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Outdoor Equipment<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Outdoor Equipment", "line": {"color": "#ab63fa"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Outdoor Equipment", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAACIw0A=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Traditional Retail<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Traditional Retail", "line": {"color": "#FFA15A"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Traditional Retail", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "5wcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAACorUA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Consumer Goods<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Consumer Goods", "line": {"color": "#19d3f3"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Consumer Goods", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "6AcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAABwp0A=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Fashion & Accessories<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Fashion & Accessories", "line": {"color": "#FF6692"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Fashion & Accessories", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "6AcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADAokA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Individual/Consultant<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Individual/Consultant", "line": {"color": "#B6E880"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Individual/Consultant", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "6AcAAA==", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADAckA=", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Manufacturing<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Manufacturing", "line": {"color": "#FF97FF"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Manufacturing", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "6AcAAOkHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "AAAAAADctEAAAAAAAK27QA==", "dtype": "f8"}, "yaxis": "y"}, {"fillpattern": {"shape": ""}, "hovertemplate": "industry=Medical Devices<br>Year=%{x}<br>Revenue (£)=%{y}<extra></extra>", "legendgroup": "Medical Devices", "line": {"color": "#FECB52"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "Medical Devices", "orientation": "v", "showlegend": true, "stackgroup": "1", "type": "scatter", "x": {"bdata": "6AcAAOkHAAA=", "dtype": "i4"}, "xaxis": "x", "y": {"bdata": "ZmZmZmbZ00Bcj8L1qEagQA==", "dtype": "f8"}, "yaxis": "y"}], "layout": {"height": 500, "hovermode": "x unified", "legend": {"title": {"text": "industry"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Industry Diversification Over Time"}, "xaxis": {"anchor": "y", "domain": [0.0, 1.0], "title": {"text": "Year"}}, "yaxis": {"anchor": "x", "domain": [0.0, 1.0], "title": {"text": "Revenue (£)"}}}}}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA9kAAAJOCAYAAACjoMSlAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAxSRJREFUeJzs3Qe4lMX5uP8BEbBhRyzYo2jsGlvsGrtGRWPvPfYuVsQoRmNLbDH23jVRsaBGjbEjiiX23tBEBXtj/9c93//sb86yBw6yyDk79+e6jpyzu6fs6/vOO8/MM890qlQqlSBJkiRJksZb5/H/EZIkSZIkCQbZkiRJkiQ1iEG2JEmSJEkNYpAtSZIkSVKDGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSJEmS1CAG2ZIkqd158803Q6dOnaof999/fyhB//79q+95zjnnnNh/jiTpJzDIliRVvffee+Hcc88NW2yxRVh44YXDjDPOGCaddNL47xprrBEuu+yyUKlUWv3+6667Lr5u+umnD926dYtBws477xxeeeWVhv2uJ598Muy+++5hqaWWCjPPPHP8PZNNNlmYY445wsYbbxxuvvnmcXrPl1xySYtgLn106dIlvo9ll102/OEPfwgjRowYp5+r5rDDDju0OC+aXamDG5LUSF0a+tMkSR3a5ZdfHvr16zfa4//973/DvffeGz9uuOGGGMhOMskk1ecJhnfcccdw6aWXtvi+t956K1x00UXhqquuCjfeeGNYd911x/t30ek///zzR/u+t99+O37ccsst4YADDginnXbaeB2LH3/8MXzyySfhscceix9XXnllePzxx8NUU001Xj9XkiQ1N4NsSdJoevXqFQPiueeeO85sXXHFFeGbb76Jz916663h4osvDrvsskv19WeddVaLAJvZ6QUXXDBcc8014YUXXojfu9VWW4Xnn38+zDrrrOP1u5hhXnzxxeNMNt/btWvX8NJLL4Vrr702fP/99/E1Z5xxRjjkkEPiTPe42mOPPcI888wT/ve//8W/n78JL774Yvxb9t1333H+mVJJRo4cGXr06DGx/wxJmngqkiT9/6688srK5ZdfXvn+++9bPH7fffeRt1392GSTTarP8dqZZ565+txWW21Vfe5///tfZaqppqo+d+ihh47X7xqTP/zhDy2+79FHH23T91188cUtvu+f//xn9bn//Oc/LZ7bfffd6/6Mf/zjH5UNN9yw0qtXr8qkk05amWaaaSqrrrpq5YorrqiMGjWq+rp77rmnxc97/fXXW/ycH3/8scWx5D3lHnzwwcrmm29e6d27d6Vr167x2C677LKVs846q/Ldd9+N9nflv4v3effdd1dWWWWVyhRTTFGZcsopK2uvvXblueeea/E9vP/8+954440Wz88xxxzV54499tjRfufTTz9d2XHHHStzzz13pXv37vF3LbbYYpUTTjih8sUXX1Tait9b+//lmmuuqSy55JKVySabrDLjjDPG3/Phhx9Wv+eiiy6qvp7XfPbZZy1+5qeffhr//6TX8PPGZvvtt2/xd7T23Morr1x5//33K7vuums8D/j/06dPn8r5559f9+cOGzasst5668X/h3ystdZalSFDhsRjmn4mx3pM/z/H9LfU/q6tt946/jz+Lv6/cA5xjh5++OGVd999d7T/t/U+0s+t9//mggsuqCy++OLxZy+66KKV7bbbrvr8csstN9r7v+2226rPTzLJJJX33ntvrP8vJKmjMMiWJLXJ9NNPX+0Ur7/++tXHH3nkkRYd7htvvLHF922wwQbV5wg6xud31fPVV19Vhg4dGjvy6XsIJP773/+Od5A9cuTIFs8deeSRowXF22677RgDk80226zyww8/xNcTcOeBzIknntji5917773V5zp37lx55513qs8dccQRY/w9K6644mhBbP78r3/960qnTp1G+z6O9UcffdSQIPucc86pdOnSpdW/ccEFF6x88MEHbfr/UhvIEZDW+5kE8+nv//rrr1ucO2effXaLn5kH4dNOO23lm2++aViQzd+RD5DkHxdeeGGL73viiSfiIEft6whQV1999YYG2c8//3xl8sknH+O5c8cdd4xXkM25l39NkM17zB/j78jlQfi666471v8PktSRWPhMkjRWH374YYvCX0svvXT182HDhrV4LWnfrX398ssvh2+//fYn/67cNttsEwszTT755DF9/JFHHomPd+7cOfzpT3+KRcvGB+uxKXiW8Ls222yzFq85+eST49ry9Pymm24ajj/++LDTTjvFIm64/vrrwx//+Mfqa7bffvvq97NWPZd//Zvf/CbMNtts8XPS1k888cTqc2uttVYYMGBA2GuvvcKUU04ZH/vXv/4V16K35t///neYf/75wxFHHNFibTxp8RdeeGEYXw8//HDYe++9ww8//BC/pmAclbIPOuigMMMMM8THWDqw3Xbb/aSff/vtt4dVV101HHPMMWH11VevPv7666+Hww47LH7evXv3sOuuu1afu+CCC1r8DP5fJCxfoGheo/B3fPrpp2HPPfcMBx54YCzGl58nCbEy58cXX3xRPSe23nrrcOSRR8ZCgdQiaCSWcXz11Vfxc86nww8/PJ6jFA/89a9/3aLeAX8D50ft8olTTjklfvDe6uHco/Ag7/uoo44KyyyzTFzOwTlQ7//Fd999F/7+979Xv6aegyQ1lYkd5UuS2jfSufPZ6J49e7aY+Rw4cGCLGavXXnutxfcfddRRLZ4f00zm2H5XjvTX2pk2UpMvu+yycXp/tTPZ9T6Y9ST1u3YWe4YZZqi+5phjjmnx/Mknn9xitpjXgxTxfEb52WefjY9/++238fekx6+99trqzyINNz3ODGDuuuuuqz7HLDIp+kn+HkgPZma+3s/MU/J/6kz2xhtvXH2clPT0fvH444+3+JnPPPPMWP+/1M6WrrnmmtXUe/7l6zxz4csvv4zPvfXWWzH9OD1HCjY++eSTFqni6fFGzWTzccstt1SfO+OMM1o8l459beYH10cyYsSIFudUI2ay99133+rjXKu1OC58tHbc88yO1l4z11xzxVT8WiwJSa/hfXGO49Zbb21xbaTHJalZOJMtSWrV559/HjbccMNYgAxU1v7HP/4Rt9lqTe22W2Pa8mt8fhczkcyuMfvGzCwz2F9++WWcKf39738fGomZx9/97nctHqPYGpXQE2aW862PDj300BazxcziY6655gqrrLJK9bmrr746/nvnnXfGmVBMN9104be//W38nFnIp59+uvp6tjbLf0/+dzGLTAX0erbddtsWldHnm2++6ufp944PZsrzCvDMkKa/sTYbgVnvcZUyF/LZ33xm9Nlnn42fzz777NVjh7/97W/xX6rOp8J4iyyySFhiiSVCI80yyywtfi9ZA7l0jNmCLpe/D4qFbbDBBg39u1ZcccXq58wyL7/88vF8JruC/0/8zmmnnXa8fgcZFdNMM81oj5P5QXFCcK2k7fXyjALeP8ULJamZGGRLkup65513wgorrBDuuOOO+DXBLqmspILmatOyCZZb+5pAuF6Hvq2/K0dgffDBB8eUblKJ2e86Yf/tPB11XJAeSzptHpyceuqpYbfddhstnXxcfPzxx9XPCXJqg+w8VTxPZSY4a+tARe3vyZGKnMtTpUeNGtXqz6v93a2l+4/L8WjtbxyTnj17tvh6pplmavH1Z599Vv08rwDP8WWggj3c6x3/RhnT8c2Pcf53tuV9je//F5YwcJ3w97AtHcsqqJJP2jjp91TSp+r/+OjTp0/dx1kywfWUp4zXpopPiP8XkjSxuYWXJGk0zLYxq/zBBx9UZz0HDRoUO+S1mBWsXZu62GKLVb9+7bXXqp/zc2qDj3H5XWOSzyKCWbrax9pi8803jzPNrE1df/31q4E/QTwBQQq+mW3OsdZ6oYUWalMQ1rdv3zj7x1ZHb7zxRrjnnnuqM/i1a1RrZwg5VvkAQK3WZmjTGvEkzQrXYiAk9/XXX1c/5+8dPnx43e/jeHz00UfxcwZMxnTsmU0dV+lnJ7V/R36cVl555bDwwgvH2W3W9//1r3+trnVm1jSfPW6Uth7f2v+fvK/8XGrt+KafmYLr/P8LXnnllVa/j4wPZrHJIGArOrIqyBJ5//334172ZH488MAD4aeaYoopWn2Otd8nnHBCzCLg/wH/L1LNBWopLLrooj/590pSuzWx89UlSe3LTTfd1KIaMZWD83W+taicPcsss9Tdwuvjjz+O66TTc4cddth4/S6qR7MNVT1XXXVVi3WiBxxwwHhXF3/llVdarO9lrXHCmuO8kvXvfve7uj9/+PDh8X3W2m233Vqsac0rM9diC6y8Sni97brYrurqq68e7zW8rJdurTI22z3lz+VrsjfaaKPq4/POO29cX1yvEvyll15aaYufuiY7YeusvGp3+rxv375t+v3juia7dtus1ta2j8+a7HzNPlXtkzvvvLPFz8z/FmoA1FsvzTmZXk+l84TtvPKfdfvtt4/2vW1Zt52jTaj3/+Ivf/nLGL9PkjoqZ7IlSVWsldxiiy2qqa1TTz11rGR90UUXtXgdj6cqzqy97devX9hnn32qac98/4ILLhhTdVknnb4nT+P9Kb/rm2++CWuuuWasZEyFaSqXM0NGhfN8JhjMQo+veeedN85sp1RuZseZDWQmlhlfqimzJhykIzOLT1Vw1j5TJZ1Z+sceeyzO7G688cYtfjaz1eeff378nNns/PFahxxySHX2lbXPZA+wdpfUe9Z7Dx06NDz00ENh5plnjsd0fFN/+ftTmj+znLfddlt8P6mCez1UEScNmNj+1VdfjbP6m2yySUx/ZuaSWWVmS9O6+XF19913x//nK620UnyveRVu0uupMp/jeFF1nHR7zpv2UsmaJRC//OUvqynazPK++eabMdPhhhtuaLHOv9avfvWreBxAVfv33nsvVjFPj9Vz7bXXhmOPPTZmZ/ziF7+I5wj/D9IyhdrZdZZqMCuf1q9zfj/zzDPxMX4GVcPHFW1DuobS/wsyWvj/JklNaWJH+ZKk9oOZyXyGqrWP2hk2ZhZrZ/zyD2avamfEfsrvYkauLd/DntJtNaaZbFD9O68Gvs4664zTPtn1ZjmTBRZYoMXrxrS/d79+/cb5/8tPmcmuVxE+fSy11FKx4nu9mWywL/WY9smuNxvcmtrZUrII6v2sOeecM2YL1HPwwQe3eC37WKc9yyfWTDYee+yxFhke6YPq58svv3yr/z8HDx7c6l7nSy+9dN2/pbb6f72PP//5zy1+T14pPv845ZRTftJMNjh3avePl6RmZeEzSdJ4Y60oa5bZz3m11VaLM6ysfe3du3ecOWQmLN+beXzWflKEbKONNoqzzMxyM5POzCtrcFn/SXVtZgcbhRnZvOIza7Sfeuqp+Dmz2VT7pvAa66zZh5j3zSwds+183xlnnNFi1jBXO6vK61vb35t9spnFpso2Fcr5HcwuzjrrrHF2n+cbtccyldL5efwefgfvhWwFZqLz/Z9rMevNrDpF4lhbz+xyly5d4mw266SPPvroeC78FMzGsucz63jZD5vjxDp4Mgtqi4clrHvP15gzg57vCz2xUG2d/5frrLNO3OecD2bpyZQgE6I1a6yxRqzQzbp7zjOOATP2Q4YMCQsssEDd7+FaYW9xvpfZ8vT/hBnt9dZbL67NTlkoeUV2ji3/32rX6P9UeRYLLHgmqZl1ItKe2H+EJElSo5GazBZSqdAWRb9qt9bSz+PRRx8Nyy23XPycgSEKrrWHAQ9JmhBcky1JkpouoGOrLLIMUoDNTK4B9s8/yMH/C9bF59kle+65pwG2pKbmTLYkSWoqpEUzU5qQWk2wR6q5fj4UdGPJQY5ihSwZIEVekpqVa7IlSVJTYq0+1cjZh9wAe+KiajmV+u+77z4DbElNz5lsSZIkSZIaxJlsSZIkSZIaxCBbkiRJkqQGsbr4BDZq1Kjw/vvvx3Vh7CMrSZIkSepYWGX9+eefh1lmmSV07jzmuWqD7AmMALt3794T+8+QJEmSJI2nd955J8w222xjfI1B9gTGDHb6n9GjR4+J/edIkiRJksbRyJEj4+Rpiu/GxCB7Aksp4gTYBtmSJEmS1HG1ZQmwhc8kSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAbp0qgfpJ9J/41Du9P/5on9F0iSJElSu+BMtiRJkiRJDWKQLUmSJElSMwfZAwcODL/61a/CVFNNFXr27Bk22mij8NJLL7V4zTfffBP22muvMP3004cpp5wy9O3bNwwfPrzFa95+++2w3nrrhcknnzz+nEMOOST88MMPLV5z//33hyWWWCJ069YtzDvvvOGSSy4Z7e85++yzw5xzzhm6d+8elllmmfD4449PoHcuSZIkSerI2mWQ/cADD8QA+tFHHw2DBw8O33//fVhzzTXDl19+WX3NAQccEG699dZw/fXXx9e///77YZNNNqk+/+OPP8YA+7vvvgsPP/xwuPTSS2MAfcwxx1Rf88Ybb8TXrLrqquHpp58O+++/f9hll13CXXfdVX3NtddeGw488MBw7LHHhqeeeiosuuiiYa211gofffTRz3hEJEmSJEkdQadKpVIJ7dzHH38cZ6IJpldaaaUwYsSIMOOMM4arrroqbLrppvE1L774YlhggQXCI488EpZddtlwxx13hPXXXz8G3zPNNFN8zXnnnRcOO+yw+PO6du0aP7/99tvDc889V/1dW2yxRfjss8/CnXfeGb9m5ppZ9bPOOit+PWrUqNC7d++wzz77hMMPP3ysf/vIkSPD1FNPHf/mHj16jP/BsPCZJEmSJP2sxiWua5cz2bV4I5huuuniv0OGDImz22ussUb1NX369Amzzz57DLLBvwsvvHA1wAYz0Byc559/vvqa/Gek16SfwSw4vyt/TefOnePX6TW1vv322/g78g9JkiRJUhnafZDNzDFp3L/+9a/DQgstFB/78MMP40z0NNNM0+K1BNQ8l16TB9jp+fTcmF5DYPz111+H//73vzHtvN5r0s+ot56cEY70way3JEmSJKkM7T7IZm026dzXXHNN6Aj69esXZ97TxzvvvDOx/yRJkiRJ0s+kS2jH9t5773DbbbeFBx98MMw222zVx3v16hVTuVk7nc9mU12c59JraquAp+rj+WtqK5LzNTn2k002WZhkkkniR73XpJ9RiyrlfEiSJEmSytMuZ7KpxUaAffPNN4f77rsvzDXXXC2eX3LJJcOkk04a7r333upjbPHFll3LLbdc/Jp/n3322RZVwKlUTgC94IILVl+T/4z0mvQzSEnnd+WvIX2dr9NrJEmSJElq1zPZpIhTOfzvf/973Cs7rX9mjTMzzPy78847x621KIZG4Ey1bwJfKouDLb8Iprfddttw8sknx59x1FFHxZ+dZpr32GOPWDX80EMPDTvttFMM6K+77rpYcTzhd2y//fZhqaWWCksvvXQ444wz4lZiO+6440Q6OpIkSZKk9qpdBtnnnntu/HeVVVZp8fjFF18cdthhh/j56aefHit99+3bN1b0pir4OeecU30tad6kmu+5554x+J5iiilisDxgwIDqa5ghJ6Bmz+0zzzwzpqRfcMEF8Wclm2++edzyi/21CdQXW2yxuL1XbTE0SZIkSZI6xD7ZHZn7ZEuSJElSx9Z0+2RLkiRJktQRGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSJEmS1CAG2ZIkSZIkNYhBtiRJkiRJDWKQLUmSJElSgxhkS5IkSZLUIAbZkiRJkiQ1iEG2JEmSJEkNYpAtSZIkSVKDGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSJEmS1CAG2ZIkSZIkNYhBtiRJkiRJDWKQLUmSJElSgxhkS5IkSZLUIAbZkiRJkiQ1iEG2JEmSJEkNYpAtSZIkSVKDGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSJEmS1CAG2ZIkSZIkNYhBtiRJkiRJDWKQLUmSJElSMwfZDz74YNhggw3CLLPMEjp16hRuueWWFs/zWL2PU045pfqaOeecc7TnTzrppBY/Z9iwYWHFFVcM3bt3D7179w4nn3zyaH/L9ddfH/r06RNfs/DCC4dBgwZNwHcuSZIkSerI2mWQ/eWXX4ZFF100nH322XWf/+CDD1p8XHTRRTGI7tu3b4vXDRgwoMXr9tlnn+pzI0eODGuuuWaYY445wpAhQ2KA3r9//3D++edXX/Pwww+HLbfcMuy8885h6NChYaONNoofzz333AR895IkSZKkjqpLaIfWWWed+NGaXr16tfj673//e1h11VXD3HPP3eLxqaaaarTXJldeeWX47rvvYoDetWvX8Mtf/jI8/fTT4bTTTgu77bZbfM2ZZ54Z1l577XDIIYfEr48//vgwePDgcNZZZ4XzzjuvAe9UkiRJktRM2uVM9rgYPnx4uP322+Nscy3Sw6effvqw+OKLx5nqH374ofrcI488ElZaaaUYYCdrrbVWeOmll8Knn35afc0aa6zR4mfyGh5vzbfffhtnyfMPSZIkSVIZ2uVM9ri49NJL44z1Jpts0uLxfffdNyyxxBJhuummi2nf/fr1iynjzFTjww8/DHPNNVeL75lpppmqz0077bTx3/RY/hoeb83AgQPDcccd18B3KEmSJEnqKDp8kE2699Zbbx0Lk+UOPPDA6ueLLLJInLHefffdYxDcrVu3Cfb3EMznv5uZbIqqSZIkSZKaX4cOsv/1r3/F9O5rr712rK9dZpllYrr4m2++Geaff/64VptU81z6Oq3jbu01ra3zBgH8hAziJUmSJEntV4dek33hhReGJZdcMlYiHxuKmnXu3Dn07Nkzfr3ccsvFrcK+//776msoakYATqp4es29997b4ufwGh6XJEmSJKlDBNlffPFFDIr5wBtvvBE/f/vtt1ukYbOH9S677DLa91OY7IwzzgjPPPNMeP3112Ml8QMOOCBss8021QB6q622iinkFEx7/vnn42w41cTzVO/99tsv3HnnneHUU08NL774Ytzi68knnwx77733z3IcJEmSJEkdS7tMFyeQZUuuJAW+22+/fbjkkkvi59dcc02oVCpxH+tapGvzPEEx1b4pcEaQnQfQU089dbj77rvDXnvtFWfDZ5hhhnDMMcdUt+/C8ssvH6666qpw1FFHhSOOOCL84he/CLfccktYaKGFJvARkCRJkiR1RJ0qRKqaYJhxJ6AfMWJE6NGjx/j/wP4bh3an/80T+y+QJEmSpHYR17XLdHFJkiRJkjoig2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElq5iD7wQcfDBtssEGYZZZZQqdOncItt9zS4vkddtghPp5/rL322i1e88knn4Stt9469OjRI0wzzTRh5513Dl988UWL1wwbNiysuOKKoXv37qF3797h5JNPHu1vuf7660OfPn3iaxZeeOEwaNCgCfSuJUmSJEkdXbsMsr/88suw6KKLhrPPPrvV1xBUf/DBB9WPq6++usXzBNjPP/98GDx4cLjtttti4L7bbrtVnx85cmRYc801wxxzzBGGDBkSTjnllNC/f/9w/vnnV1/z8MMPhy233DIG6EOHDg0bbbRR/Hjuuecm0DuXJEmSJHVknSqVSiW0Y8xS33zzzTG4zWeyP/vss9FmuJP//Oc/YcEFFwxPPPFEWGqppeJjd955Z1h33XXDu+++G2fIzz333HDkkUeGDz/8MHTt2jW+5vDDD48/88UXX4xfb7755jHgJ0hPll122bDYYouF8847r01/P8H81FNPHUaMGBFn1cdb/41Du9P/5on9F0iSJEnSBDMucV27nMlui/vvvz/07NkzzD///GHPPfcM//vf/6rPPfLIIzFFPAXYWGONNULnzp3DY489Vn3NSiutVA2wsdZaa4WXXnopfPrpp9XX8H05XsPjkiRJkiTV6hI6IFLFN9lkkzDXXHOF1157LRxxxBFhnXXWicHvJJNMEmenCcBzXbp0CdNNN118DvzL9+dmmmmm6nPTTjtt/Dc9lr8m/Yx6vv322/iRj3hIkiRJksrQIYPsLbbYovo5xcgWWWSRMM8888TZ7dVXX32i/m0DBw4Mxx133ET9GyRJkiRJE0eHTRfPzT333GGGGWYIr776avy6V69e4aOPPmrxmh9++CFWHOe59Jrhw4e3eE36emyvSc/X069fv5innz7eeeedBr1LSZIkSVJ71xRBNsXMWJM988wzx6+XW265WBiNquHJfffdF0aNGhWWWWaZ6muoOP79999XX0MlctZ4kyqeXnPvvfe2+F28hsdb061bt7gQPv+QJEmSJJWhXQbZ7Gf99NNPxw+88cYb8fO33347PnfIIYeERx99NLz55psxCP7tb38b5p133liUDAsssEBct73rrruGxx9/PPz73/8Oe++9d0wzp7I4ttpqq1j0jO252Orr2muvDWeeeWY48MADq3/HfvvtF6uSn3rqqbHiOFt8Pfnkk/FnSZIkSZLUIYJsAtnFF188foDAl8+POeaYWNhs2LBhYcMNNwzzzTdfDJKXXHLJ8K9//SvOIidXXnll6NOnT1yjzdZdK6ywQos9sCm/fvfdd8cAnu8/6KCD4s/P99Jefvnlw1VXXRW/j327b7jhhrjF10ILLfQzHxFJkiRJUkfQ7vfJ7ujcJ1uSJEmSOrYi9smWJEmSJKm9MciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhqkS2ig77//Pnz44Yfhq6++CjPOOGOYbrrpGvnjJUmSJElq7pnszz//PJx77rlh5ZVXDj169AhzzjlnWGCBBWKQPcccc4Rdd901PPHEE435ayVJkiRJatYg+7TTTotB9cUXXxzWWGONcMstt4Snn346vPzyy+GRRx4Jxx57bPjhhx/CmmuuGdZee+3wyiuvNO4vlyRJkiSpmdLFmaF+8MEHwy9/+cu6zy+99NJhp512Cuedd14MxP/1r3+FX/ziF+PzKyVJkiRJas4g++qrr27T67p16xb22GOP8flVkiRJkiS1e+2yujiz4xtssEGYZZZZQqdOnWIael5c7bDDDgsLL7xwmGKKKeJrtttuu/D++++3+BmksfO9+cdJJ53U4jXDhg0LK664YujevXvo3bt3OPnkk0f7W66//vrQp0+f+Bp+56BBgybgO5ckSZIkFR1kkwr+wAMPtPr8p59+GlZbbbVx+plffvllWHTRRcPZZ5892nNULn/qqafC0UcfHf+96aabwksvvRQ23HDD0V47YMCA8MEHH1Q/9tlnn+pzI0eOjGvFKc42ZMiQcMopp4T+/fuH888/v/qahx9+OGy55ZZh5513DkOHDg0bbbRR/HjuuefG6f1IkiRJksrQqVKpVMbnB8w111wx0F188cXj17///e9jcDvDDDPEr4cPHx5nm3/88cef9gd26hRuvvnmGNyOaW0467/feuutMPvss1dnsvfff//4UQ8V0Y888si45VjXrl3jY4cffnicNX/xxRfj15tvvnkM+G+77bbq9y277LJhscUWi4MLbUEwP/XUU4cRI0bE6uvjrf/God3pf/PE/gskSZIkaYIZl7huvGeyCaLz/bCvuOKK+Af8nHijBOPTTDNNi8dJD59++unjAAAz1VQ6T6h+vtJKK1UDbKy11lpxVpzZ9/QaqqbneA2PS5IkSZLU0MJnacaYdcsHH3xw/JqJcQLen8s333wT12iT1p2PKOy7775hiSWWiAMApH3369cvpoyz7RiYwWYWPjfTTDNVn5t22mnjv+mx/DU83ppvv/02fiQ/94CDJEmSJKkDB9kEr2zTddRRR4XJJ588rplm5rhz5/+bJB/PbPQxogja7373u/g7SP/OHXjggdXPF1lkkThjvfvuu4eBAwfGaucTCj//uOOOm2A/X5IkSZLUxEH2tttuG5ZffvlYGOyLL74Ie+65Z1zr3LNnz/j8Z5991iLgbXSAzTrs++67b6x58csss0xMF3/zzTfD/PPPH3r16hVT3XPpa55L/9Z7TXq+tUGH/P0yk03lckmSJElS8xvvIBvzzDNP/MB1110XttlmmzDzzDNXg9IDDjggTIgA+5VXXgn//Oc/47rrsXn66afj7HoK/pdbbrk4GMDPmnTSSeNjgwcPjgE4qeLpNffee2+L4mm8hsdbwyz5hJwplyRJkiQ1eZCdq91HmjXMo0aNGqefwYz4q6++Wv36jTfeiEEy66sJ3jfddNO4fRdVv6lantZI8zxp4RQme+yxx8Kqq64apppqqvg1gT7Bfwqgt9pqq5jWzfZcrOlmW64zzzwznH766dXfu99++4WVV145nHrqqWG99dYL11xzTXjyySdbbPMlSZIkSVLDtvCaEO6///4YINfafvvt417WtQXLEma1V1lllRiAs5UYW3FRhIzXk9ZOGnc+yzxs2LCw1157xS3A2HKMfbQJuHMUdWO9OWnmv/jFL8LJJ58c1l133Ta/F7fwkiRJkqSObVziuvEKstdee+0Y9LJ39Jh8/vnn4ZxzzglTTjllDGpLYpAtSZIkSeXEdeOVLr7ZZpuFvn37xl+2wQYbhKWWWirMMsssoXv37nGv6RdeeCE89NBDMYWcdGv2qpYkSZIkqVmNV5DNembWOZNSfe2118a1ykT2YK/sBRdcMKy11loxHXuBBRZo1N8sSZIkSVJzFj5jjTOBNh8gyP76669jxe9UtVuSJEmSpBI0vLo4qeN8SJIkSZJUms4T+w+QJEmSJKlZGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSe0xyP7ss8/CBRdcEPr16xc++eST+NhTTz0V3nvvvUb+GkmSJEmSmru6+LBhw8Iaa6wRK4u/+eabYddddw3TTTdduOmmm8Lbb78dLrvsskb9KkmSJEmSmnsm+8ADDww77LBDeOWVV0L37t2rj6+77rrhwQcfbNSvkSRJkiSp+YPsJ554Iuy+++6jPT7rrLOGDz/8sFG/RpIkSZKk5g+yu3XrFkaOHDna4y+//HKYccYZG/VrJEmSJElq/iB7ww03DAMGDAjff/99/LpTp05xLfZhhx0W+vbt26hfI0mSJElS8wfZp556avjiiy9Cz549w9dffx1WXnnlMO+884apppoqnHDCCY36NZIkSZIkNX91caqKDx48ODz00EOx0jgB9xJLLBErjkuSJEmSVIKGBdnJCiusED8kSZIkSSpNw4Js1mOPyTHHHNOoXyVJkiRJUnMH2TfffHOLrymA9sYbb4QuXbqEeeaZxyBbkiRJktT0GhZkDx06dLTH2NJrhx12CBtvvHGjfo0kSZIkSc1fXbyeHj16hOOOOy4cffTRE/LXSJIkSZLU/EE2RowYET8kSZIkSWp2DUsX//Of/9zi60qlEj744INw+eWXh3XWWadRv0aSJEmSpOYPsk8//fQWX3fu3DnMOOOMYfvttw/9+vVr1K+RJEmSJKn5g2wqiUuSJEmSVLIJviZbkiRJkqRSNGwm+8svvwwnnXRSuPfee8NHH30URo0a1eL5119/vVG/Shqz/u1wy7j+LfeRlyRJktScGhZk77LLLuGBBx4I2267bZh55plDp06dGvWjJUmSJEkqK8i+4447wu233x5+/etfN+pHSpIkSZJU5prsaaedNkw33XSN+nGSJEmSJJUbZB9//PHhmGOOCV999VWjfqQkSZIkSWWmi5966qnhtddeCzPNNFOYc845w6STTtri+aeeeqpRv0qSJEmSpOYOsjfaaKNG/ShJkiRJkspOFz/22GPH+DEuHnzwwbDBBhuEWWaZJVYpv+WWW1o8X6lUYmo6Vcwnm2yysMYaa4RXXnmlxWs++eSTsPXWW4cePXqEaaaZJuy8887hiy++aPGaYcOGhRVXXDF079499O7dO5x88smj/S3XX3996NOnT3zNwgsvHAYNGjRO70WSJEmSVI6GBdn47LPPwgUXXBD69esXg9yUJv7ee++N857biy66aDj77LPrPk8w/Oc//zmcd9554bHHHgtTTDFFWGuttcI333xTfQ0B9vPPPx8GDx4cbrvtthi477bbbtXnR44cGdZcc80wxxxzhCFDhoRTTjkl9O/fP5x//vnV1zz88MNhyy23jAH60KFD42w9H88999xPODqSJEmSpGbXqcK0cAMwK8yM8tRTTx3efPPN8NJLL4W55547HHXUUeHtt98Ol1122U/7Azt1CjfffHM1HZ0/lxnugw46KBx88MHxsREjRsS14JdccknYYostwn/+85+w4IILhieeeCIstdRS8TV33nlnWHfddcO7774bv//cc88NRx55ZPjwww9D165d42sOP/zwOGv+4osvxq8333zzGPATpCfLLrtsWGyxxWKA3xYE8xwT/kZm1cdb/41Du9P/5tCueIwkSZIkNdC4xHUNm8k+8MADww477BDTtkmtTghsmUVulDfeeCMGxgT0CW92mWWWCY888kj8mn9JEU8BNnh9586d48x3es1KK61UDbDBbDiDA59++mn1NfnvSa9Jv0eSJEmSpAlS+IxZ47/+9a+jPT7rrLPGoLhR0s9i5jrH1+k5/u3Zs2eL57t06RL38c5fM9dcc432M9Jz7PvNv2P6PfV8++238SMf8ZAkSZIklaFhQXa3bt3qBpQvv/xymHHGGUMpBg4cGI477riJ/WdIY2davSRJktRwDUsX33DDDcOAAQPC999/X11LzVrsww47LPTt27dRvyb06tUr/jt8+PAWj/N1eo5/P/rooxbP//DDD7EYW/6aej8j/x2tvSY9Xw9F38jTTx/vvPPOeLxbSZIkSVKRQfapp54at8giTfvrr78OK6+8cph33nnDVFNNFU444YRG/ZqY4k2Qe++991YfYwadtdbLLbdc/Jp/qXRO1fDkvvvuC6NGjYprt9NrWCueBgVAJfL5558/poqn1+S/J70m/Z7WZvRZCJ9/SJIkSZLK0LB0cYqPEYA+9NBDsdI4AfcSSywxWuGwtuB7X3311RbFzp5++um4pnr22WcP+++/f/jDH/4QfvGLX8Sg++ijj44Vw1MF8gUWWCCsvfbaYdddd41VwAmk995771h5nNdhq622imndbM/FbDvbcp155pnh9NNPr/7e/fbbLw4WMICw3nrrhWuuuSY8+eSTLbb5kiRJkiSp4UE2adG9e/cOK6ywQvwYHwSyq666aovK5dh+++3jNl2HHnpo3FqLfa+Zseb3sUVXXtX8yiuvjIH16quvHquKk7LO3tr5oMDdd98d9tprr7DkkkuGGWaYIRxzzDEt9tJefvnlw1VXXRW3ITviiCNiUM8WXwsttNB4vT9JkiRJUnNq2D7Zk0wySQx2t9lmm7DppptWU65L5z7ZE4HHqG08TpIkSVL73Seb2eell146Fj+beeaZY+r2DTfc0GI7K0mSJEmSmlnDguzFF188nHLKKbGi+B133BG37SL1mn2ld9ppp0b9GkmSJEmSmj/ITti6i/XUf/vb38I999wTC5Ndeumljf41kiRJkiQ1f5D97rvvhpNPPjkstthiMX18yimnDGeffXajf40kSZIkSc1bXfyvf/1rrMT973//O/Tp0ydsvfXW4e9//3uYY445GvUrJEmSJEkqI8hm3+ott9wybpO16KKLNurHSpIkSZJUXpBNwTPWY0uSJEmSVKqGrckmwP7Xv/4V98lebrnlwnvvvRcfv/zyy8NDDz3UqF8jSZIkSVLzB9k33nhjWGuttcJkk00Whg4dWt0fm826TzzxxEb9GkmSJEmSmj/IZk32eeedF7fumnTSSauP//rXvw5PPfVUo36NJEmSJEnNH2S/9NJLYaWVVhrt8amnnjp89tlnjfo1kiRJkiQ1f5Ddq1ev8Oqrr472OOux55577kb9GkmSJEmSmj/I3nXXXcN+++0XHnvssVgE7f333w9XXnllOPjgg8Oee+7ZqF8jSZIkSVLzb+F1+OGHh1GjRoXVV189fPXVVzF1vFu3bjHI3meffRr1ayRJkiRJav4gm9nrI488MhxyyCExbfyLL74ICy64YJhyyinD119/HauOS5IkSZLUzBqWLp507do1BtdLL710rDJ+2mmnhbnmmqvRv0aSJEmSpOYLstkPu1+/fmGppZYKyy+/fLjlllvi4xdffHEMrk8//fRwwAEHNOJvlSRJkiSpudPFjznmmPDXv/41rLHGGuHhhx8Om222Wdhxxx3Do48+Gmex+XqSSSZpzF8rSZIkSVIzB9nXX399uOyyy8KGG24YnnvuubDIIouEH374ITzzzDNxnbYkSZIkSaUY73Txd999Nyy55JLx84UWWihWFCc93ABbkiRJklSa8Q6yf/zxx1jsLOnSpUusKC5JkiRJUmnGO128UqmEHXbYIc5g45tvvgl77LFHmGKKKVq87qabbhrfXyVJkiRJUnMH2dtvv32Lr7fZZpvx/ZGSJEmSJJUZZLNVlyRJkiRJasCabEmSJEmS9H8MsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKn0IHvOOecMnTp1Gu1jr732is+vssoqoz23xx57tPgZb7/9dlhvvfXC5JNPHnr27BkOOeSQ8MMPP7R4zf333x+WWGKJ0K1btzDvvPOGSy655Gd9n5IkSZKkjqNL6KCeeOKJ8OOPP1a/fu6558JvfvObsNlmm1Uf23XXXcOAAQOqXxNMJ3wvAXavXr3Cww8/HD744IOw3XbbhUknnTSceOKJ8TVvvPFGfA3B+ZVXXhnuvffesMsuu4SZZ545rLXWWj/be5UkSZIkdQwdNsieccYZW3x90kknhXnmmSesvPLKLYJqguh67r777vDCCy+Ee+65J8w000xhscUWC8cff3w47LDDQv/+/UPXrl3DeeedF+aaa65w6qmnxu9ZYIEFwkMPPRROP/10g2xJkiRJUvOki+e+++67cMUVV4SddtoppoUnzD7PMMMMYaGFFgr9+vULX331VfW5Rx55JCy88MIxwE4InEeOHBmef/756mvWWGONFr+L1/C4JEmSJElNM5Odu+WWW8Jnn30Wdthhh+pjW221VZhjjjnCLLPMEoYNGxZnqF966aVw0003xec//PDDFgE20tc8N6bXEIh//fXXYbLJJhvtb/n222/jR8JrJUmSJEllaIog+8ILLwzrrLNODKiT3Xbbrfo5M9aso1599dXDa6+9FtPKJ5SBAweG4447boL9fEmSJElS+9Xh08XfeuutuK6agmRjsswyy8R/X3311fgva7WHDx/e4jXp67SOu7XX9OjRo+4sNkhLHzFiRPXjnXfeGY93J0mSJEnqSDp8kH3xxRfH7beoAj4mTz/9dPyXGW0st9xy4dlnnw0fffRR9TWDBw+OAfSCCy5YfQ0VxXO8hsdbw1Zf/Iz8Q5IkSZJUhg4dZI8aNSoG2dtvv33o0uX/Zb6TEk6l8CFDhoQ333wz/OMf/4jbc6200kphkUUWia9Zc801YzC97bbbhmeeeSbcdddd4aijjor7bBMog627Xn/99XDooYeGF198MZxzzjnhuuuuCwcccMBEe8+SJEmSpParQwfZpIm//fbbsap4ju23eI5Auk+fPuGggw4Kffv2Dbfeemv1NZNMMkm47bbb4r/MTG+zzTYxEM/31Wb7rttvvz3OXi+66KJxK68LLrjA7bskSZIkSc1X+IwgulKpjPZ47969wwMPPDDW76f6+KBBg8b4mlVWWSUMHTp0vP5OSR1Y/41Du9P/5on9F0iSJKkZZ7IlSZIkSWpPOvRMtiSpnXDGX5IkKXImW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSSo5yO7fv3/o1KlTi48+ffpUn//mm2/CXnvtFaaffvow5ZRThr59+4bhw4e3+Blvv/12WG+99cLkk08eevbsGQ455JDwww8/tHjN/fffH5ZYYonQrVu3MO+884ZLLrnkZ3uPkiRJkqSOp0MG2fjlL38ZPvjgg+rHQw89VH3ugAMOCLfeemu4/vrrwwMPPBDef//9sMkmm1Sf//HHH2OA/d1334WHH344XHrppTGAPuaYY6qveeONN+JrVl111fD000+H/fffP+yyyy7hrrvu+tnfqyRJkiSpY+gSOqguXbqEXr16jfb4iBEjwoUXXhiuuuqqsNpqq8XHLr744rDAAguERx99NCy77LLh7rvvDi+88EK45557wkwzzRQWW2yxcPzxx4fDDjsszpJ37do1nHfeeWGuueYKp556avwZfD+B/Omnnx7WWmutn/39SpIkSZLavw47k/3KK6+EWWaZJcw999xh6623junfGDJkSPj+++/DGmusUX0tqeSzzz57eOSRR+LX/LvwwgvHADshcB45cmR4/vnnq6/Jf0Z6TfoZkiRJkiQ1xUz2MsssE9O7559//pgqftxxx4UVV1wxPPfcc+HDDz+MM9HTTDNNi+8hoOY58G8eYKfn03Njeg2B+Ndffx0mm2yyun/bt99+Gz8SXi9JkiRJKkOHDLLXWWed6ueLLLJIDLrnmGOOcN1117Ua/P5cBg4cGIN+SZIkSVJ5Omy6eI5Z6/nmmy+8+uqrcZ02Bc0+++yzFq+hunhaw82/tdXG09dje02PHj3GGMj369cvrgtPH++8807D3qckSZIkqX1riiD7iy++CK+99lqYeeaZw5JLLhkmnXTScO+991aff+mll+Ka7eWWWy5+zb/PPvts+Oijj6qvGTx4cAygF1xwwepr8p+RXpN+RmvY7oufk39IkiRJksrQIYPsgw8+OG7N9eabb8YtuDbeeOMwySSThC233DJMPfXUYeeddw4HHnhg+Oc//xkLoe24444xOKayONZcc80YTG+77bbhmWeeidtyHXXUUXFvbYJk7LHHHuH1118Phx56aHjxxRfDOeecE9PR2R5MkiRJkqSmWZP97rvvxoD6f//7X5hxxhnDCiusELfn4nOwzVbnzp1D3759YxEyqoITJCcE5LfddlvYc889Y/A9xRRThO233z4MGDCg+hq277r99ttjUH3mmWeG2WabLVxwwQVu3yVJkiRJaq4g+5prrhnj8927dw9nn312/GgNhdIGDRo0xp+zyiqrhKFDh/7kv1OSJEmSVJYOmS4uSZIkSVJ7ZJAtSZIkSVKDGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSJEmS1CAG2ZIkSZIkNYhBtiRJkiRJDWKQLUmSJElSgxhkS5IkSZLUIAbZkiRJkiQ1iEG2JEmSJEkNYpAtSZIkSVKDGGRLkiRJktQgXRr1gyRJ0lj03zi0O/1vnth/gSRJTcWZbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWqQLo36QZIkSQ3Rf+PQ7vS/eWL/BZKkDsKZbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSSo5yB44cGD41a9+FaaaaqrQs2fPsNFGG4WXXnqpxWtWWWWV0KlTpxYfe+yxR4vXvP3222G99dYLk08+efw5hxxySPjhhx9avOb+++8PSyyxROjWrVuYd955wyWXXPKzvEdJkiRJUsfTIYPsBx54IOy1117h0UcfDYMHDw7ff/99WHPNNcOXX37Z4nW77rpr+OCDD6ofJ598cvW5H3/8MQbY3333XXj44YfDpZdeGgPoY445pvqaN954I75m1VVXDU8//XTYf//9wy677BLuuuuun/X9SpIkSZI6hi6hA7rzzjtbfE1wzEz0kCFDwkorrVR9nBnqXr161f0Zd999d3jhhRfCPffcE2aaaaaw2GKLheOPPz4cdthhoX///qFr167hvPPOC3PNNVc49dRT4/cssMAC4aGHHgqnn356WGuttSbwu5QkSZIkdTQdcia71ogRI+K/0003XYvHr7zyyjDDDDOEhRZaKPTr1y989dVX1eceeeSRsPDCC8cAOyFwHjlyZHj++eerr1ljjTVa/Exew+OSJEmSJDXFTHZu1KhRMY3717/+dQymk6222irMMcccYZZZZgnDhg2LM9Ss277pppvi8x9++GGLABvpa54b02sIxL/++usw2WSTjfb3fPvtt/Ej4bWSJEmSpDJ0+CCbtdnPPfdcTOPO7bbbbtXPmbGeeeaZw+qrrx5ee+21MM8880zQomzHHXfcBPv5kiRJkqT2q0Oni++9997htttuC//85z/DbLPNNsbXLrPMMvHfV199Nf7LWu3hw4e3eE36Oq3jbu01PXr0qDuLDdLSSV9PH++88854vENJkiRJUkfSIYPsSqUSA+ybb7453HfffbE42dhQHRzMaGO55ZYLzz77bPjoo4+qr6FSOQH0ggsuWH3Nvffe2+Ln8Boebw1bffEz8g9JkiRJUhk6d9QU8SuuuCJcddVVca9s1k7zwTppkBJOpXCqjb/55pvhH//4R9huu+1i5fFFFlkkvoYtvwimt9122/DMM8/EbbmOOuqo+LMJlMG+2q+//no49NBDw4svvhjOOeeccN1114UDDjhgor5/SZIkSVL71CGD7HPPPTemYq+yyipxZjp9XHvttfF5tt9iay4C6T59+oSDDjoo9O3bN9x6663VnzHJJJPEVHP+ZWZ6m222iYH4gAEDqq9hhvz222+Ps9eLLrpo3MrrggsucPsuSZIkSVLzFD4jXXxMevfuHR544IGx/hyqjw8aNGiMryGQHzp06Dj/jZIkSRNU/41Du9L/5on9F0hSu9AhZ7IlSZIkSWqPDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIaxCBbkiRJkqQGMciWJEmSJKlBDLIlSZIkSWoQg2xJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqEINsSZIkSZIapEujfpAkSZLUrvTfOLQ7/W+e2H+BpAnMmWxJkiRJkhrEIFuSJEmSpAYxyJYkSZIkqUEMsiVJkiRJahCDbEmSJEmSGsQgW5IkSZKkBjHIliRJkiSpQQyyJUmSJElqkC6N+kGSJEmSOqD+G4d2p//NE/svkH4yZ7Lb6Oyzzw5zzjln6N69e1hmmWXC448/PrH/JEmSJElSO+NMdhtce+214cADDwznnXdeDLDPOOOMsNZaa4WXXnop9OzZc2L/eZIkSZImNGf826a/x8mZ7DY47bTTwq677hp23HHHsOCCC8Zge/LJJw8XXXTRxP7TJEmSJEntiEH2WHz33XdhyJAhYY011qg+1rlz5/j1I488MlH/NkmSJElS+2K6+Fj897//DT/++GOYaaaZWjzO1y+++OJor//222/jRzJixIj478iRIxvzB337fWh3GvXeGsVj1DYep7bxOLWNx6ltPE5t43HqmMfJY9Q2Hqe28Ti1jcfpZztOKZ6rVCpjfW2nSlteVbD3338/zDrrrOHhhx8Oyy23XPXxQw89NDzwwAPhsccea/H6/v37h+OOO24i/KWSJEmSpAnpnXfeCbPNNtsYX+NM9ljMMMMMYZJJJgnDhw9v8Thf9+rVa7TX9+vXLxZJS0aNGhU++eSTMP3004dOnTqF9oBRmN69e8cTpEePHhP7z2m3PE5t43FqG4/T2HmM2sbj1DYep7bxOLWNx6ltPE5t43HqmMeIuenPP/88zDLLLGN9rUH2WHTt2jUsueSS4d577w0bbbRRNXDm67333nu013fr1i1+5KaZZprQHnHCtpeTtj3zOLWNx6ltPE5j5zFqG49T23ic2sbj1DYep7bxOLWNx6njHaOpp566Ta8zyG4DZqa33377sNRSS4Wll146buH15ZdfxmrjkiRJkiQlBtltsPnmm4ePP/44HHPMMeHDDz8Miy22WLjzzjtHK4YmSZIkSSqbQXYbkRpeLz28IyKd/dhjjx0trV0teZzaxuPUNh6nsfMYtY3HqW08Tm3jcWobj1PbeJzaxuPU/MfI6uKSJEmSJDVI50b9IEmSJEmSSmeQLUmSJElSgxhkS5IkSZLUIAbZkiRJkiQ1iEG2ijFq1Ki6n0uasPLrzVqb9Q0cODCcfvrpE/vPUJNed99///1E/VvaK/sC0s+vUkg/wCBbxejc+f9O9wsuuCD861//ip97gx3dCy+8EL7++uv4+amnnhrefPPNif0ntUul3CQafe099NBD8XOPX0tffvllOOigg8L5558/sf+Udsv2+qddd3/605/CbbfdFn744YeJ/Se1u/MpHaOnnnoqfPDBBxP7T2q3vPbGznta28+lTp06xc+bvU0yyG4SXtxtP0bMGF144YXx83SD1f8ZMmRI2GKLLWJHf5999gmHHHJI+Pbbbyf2n9WubxJvv/12eP7558Nnn302sf+sdn3t8XHCCSeEiy66KD6Wjl/pPvzww/jvH/7wh3DSSSeFPffcM5x33nkT+89q1wHRfffdFz+efvrpif1ntfuA6JJLLgkDBgwIc889d5hkkkkm6t/VXs+nI488Muy7775x8D0NMKv+sbrsssvCgw8+GL777ruJ/We12z4BGUlnnnnmxP6T2v25dPLJJ4cDDzwwfP7556FZGWE0ATqvXNx0Og477LBw2mmnhWeeeWZi/1ntNshmVJ/O2eOPPz6x/6R2Z8kllwyrrrpq7OxffPHF4eGHHw7zzz9/+PHHHyf2n9auzqN0kzjqqKPCpptuGpZbbrmw3XbbxRuGWm+juPaYMXriiScm9p/ULvTv3z+eN2kg69BDDw3HH3982GuvvQy0a6RrjmO0ySabhF122SUsv/zy1UEbjX6s7rzzzjBixIiYkbToootO7D+rXR6jo48+Ovztb3+L/6699tphsskmm9h/Wru93x1++OGxjzls2DAHI1oJHJmooG95wAEHhCuvvHJi/2ntuh3/85//HOaZZ57mnqCoqCkMGjSoMsUUU1RWWmmlysILL1yZZ555KnfffXelZKNGjar7+Msvv1xZaKGFKqeeemr8+scff/yZ/7L26Ycffoj/XnbZZZXpp5++suCCC1ZOP/30yogRI+LjHqeWTjjhhMoMM8xQGTx4cOXDDz+s/O53v6tMM800lSeeeKJSutauvRdffLHyy1/+snLGGWfEr0s/pz766KPKs88+Gz///PPPW5xbnTt3rpx77rmV0uXn0n/+85/YLnGNPf3005WBAwfG40Q7pZbeeeedSqdOneLHgAEDJvaf0y4988wzlfnmm69y//33x68//fTTynPPPRevu/SY/s/JJ59cmXHGGStPPfVUta/QWjtfqsMPP7yy/PLLVzbZZJPKzDPPXOnevXvlr3/968T+s9qda665ptKzZ8/KkCFDqo998803lU8++aTy7bffVpqJQXaToFN2/vnnx89pBHfZZZfY4b/rrrsqJcob/+uvv75y3nnntejUn3LKKZVevXpV3njjjUrpagOdV199NR6X/fbbr7LkkkvGjmwKtPV/59b//ve/yhprrFG57rrr4mMMaDHIdcEFF1RvGKpUrr766spFF13U4prkfKID8vbbb1dKljqquO+++yrLLbdc7PQnBtotcd4cdNBBlYMPPrj6GOcUATaBZBq40f/zyCOPVHr37l1ZbbXVKsOHD5/Yf06789prr8UBd/oIjz/+eGX33Xev9OnTJ3507do1Tl6oUvnuu+/iIPJJJ50Uv37zzTcrf//73ytrrrlm5bDDDqs88MADldIROE455ZSVf//73zFQZLCGPhSPpb65/l9bzkAEhg0bVjnttNMq888/f5wc/OMf/1j56quvKs3CILuDSh1WAiJuFJtttlm8USSvvPJKZeedd46Bdmkz2nmAfemll1a22WabSo8ePSrrrrtu5U9/+lPliy++qLz77ruV1VdfvXLxxRcXPaOWv+9HH3003hjSwMP3339f2XPPPStLLbVUHMXmuGHfffetvPXWW5WS1I7Yf/nll5Vf/epX8Qbxj3/8I95IUzBEgE2w/fDDD1dKPp/ee++9ym9/+9vK5JNPXll//fVj5gg3T2bY6PRzbdZ+T6kIgGijVllllXgN1gbaJc6G5Nccndbf//73MZheb731RnstAfakk05aOf744yslyq+h2uvpX//6V2XqqaeubLnlli2yJUpTr515//33K2uttVZliSWWqHTp0qWy1157xeDxgw8+qPz6178uduCm9lhx/dEP4Nq78sorY1+KNnzDDTesLLroopVdd9019hdKntkmW2TFFVds8RiDETvssEM8tzhuJRr1/58T+blx4YUXxrZ8//33j5kkDOCceeaZcQCVbAn6583CILsDu/HGG2MQTfolHbSzzz67xfME2rvttls8me+9995KCfIL+eijj46BEJ1WGjtm95ktYmT/b3/7Wzxu66yzzkT9e9uLQw45JM4uzjTTTHHJwbXXXlu92dK55Qa76aabVn7zm9/EVHJuqKXIz6nUSSWtcJlllqlstNFGlemmm65yzjnntLju6LjdcMMNlVIxK5TSvhgI3HHHHSvLLrtsZY455ogDEKT8Enjr/3VoCbRnnXXWygorrNAi0Gb2iDY8ZQSUJmWFkEp4zDHHxEEHZo1q/eEPf4jHrrSOfh4QERTSqWewhuytF154IT7+4IMPxj7CVlttVWSgnR8jJh1og2655Za4XGPkyJGVe+65Jw5GJJxD9B1SBlypx4pg6Oabb65mSBIQzTbbbJVjjz228tBDD1WvO/pRpQ+WXnLJJfHYsBwxd+utt8b2m4F4jmfJg+7vv/9+5euvv45fk4HEQA2TE/QR8Prrr8frjmVlzcIgu4NJHQhmg2jwmOFgBpsAcpJJJokXdI6TdZ999mmqk7YtWLPHLFp+4yQwZAbyyCOPjKP6BJQ0fldccUWlNHlHlBls0nSYeSX9mZQ5BiJYm50ayhNPPLGy/fbbx04aqWO16a4l3CQImjfeeON4owCphMzSbrDBBtXXklbPKD+d3BKOT73jxBIVBmJIAaMTCwJuOvesWdtiiy3iaDXX3lVXXVUp9drj2HAupesJrO2vF2iTBZACppLOpb/85S+VVVddtfLxxx/HrwmIDjzwwHivqzeIVW/WpFnVvkfSdhnw419mGJmdJVvrySefjM9zL+T5tddeu6nSMcfFoYceWpl99tnjjCMD7qSK33HHHdXnOS5kchE0cvxKGkyuN/DOkjqCobTUgOsv3f/APY4BZWayS9HaYMJjjz1WWXzxxeM9jkmdhOuPbMojjjiisthii8W6EiXI26cBAwbEpYdpKUZahpGuL15LH4G2iWV4zTRgY5DdAdGJJX2XGcaEUX6CadJSagPtvBNXAtJQmXGlo/rZZ5/Fx2oDHkbVWAfJbPa2225bKRWzY6TIMTKdMCCz9957x85+CrRrj2EJnY+8oWcmaPPNN4/BI9khqaNBEECwSEeD9WlkASyyyCJFDUTkN1NGpWmbKPhCYRNm1lKAlJAKRrG4BRZYIM66lSQdK1JS6XD94he/qMw777xxcIsAOw+0GaihuFdJ8muO9jnN4tNGc4/LA23udWRz1SohwK5FASHOJdqphH4AmTZkjKT0S44p7VQzdWLHZaaRbC3WzKa6LN26daueQ5w3tFccH4Lwktrwev0CJiGGDh1aPVfyc4ZrkEwA0scZqEjHqtmvvfz9McHFhM1RRx1V7Q+R8szkF33zO++8M/alGLChz5CySXi8JP3794+D6mREsAyDjDYGupi1TgNbZJZwv2OQIp1LzdJGGWR3sIubDisXMh0PUi4pwJSQwkqgzexavc5Hs6q9GLlBUESBBq22QmjtTAej+wQEeZXDUtDxYraf48R5k3vppZfiYzSGpNbnmv1GWuuAAw6Io7A77bRTXKdH8EhwmIIiziHWFvHx5z//uXrDLWEgovZmyvIVMmv4IOuB6ut0XFOAlJ8/XJtce3TkSnLbbbfFa47iL8yabb311jGNnhkjOiFg5ohjQwet2aqtjsuMI0t++vbtG48XwWIeaLN+j/vgP//5z0pJyATJ66+ALKRpp512tPsYgzdzzTVXTPWt1Syd2LZiYCZNTNx0002VqaaaqpoOTr2R//73v/GeyNrZFFiX0IbnGTMJBQa32267FscgnS+038zGso6W/kMKipr9WOX9HoLrVOeH7BAGTOkzgXOKgRqybRj4Ys065xNtFpM6pSzdxP/+97848ZCyjhj4o4+QF/Pk2uNrJnuase9kkN2BsE6WNF5uBMwW0cFI1YzzQJv1j4wclbb2ilH6hJkitjKjg9balkrcNDhejJ6VUB2zXnBMgMha63qV6FlbxAwSN9JSMerMtUQqWEJwtPTSS8frLM1o194USpv94GbKDD6DDDk6tXRmCbTpxKbzkGuPgInOST771uxou0l/poJqShlnqQaDgmRJkBaeAm2eq13f14xqrx2Wr9BxTZ1RzhfaKa5D0qDTwDJZSmSSNFOHbGxYJsa5U5udRnBNRkRaP5u39WRFsHSjJPUGEAiyGchiTTbrY/MdR8h+O+uss4prw1laQHuU43iQspvXzEjHk/OO7JpUwDI9XtI1yAAolbG55nj/tEfcx8jMSqngLBtjYItjla5Fzj+C7jzdvtn7mK+//npccsC9n7oHeYFYgmv6UqmgbrNedwbZHWzWMS+wxGgaKXN5Sm/qfKRZtlIQJDNizwhsvn0QBbsIFNO6tFocTwYr8jU0zd7p4NzIO+/PP/98rE7PoARpvDm2WSptxiPHCGztdlPcSLj2JptsslgLIQVFPF7aLH/e5jBKn9qnVNwEK6+8cmXOOeeMARGj+QmFGrn2mrFSfb1rhseYlSbNkGuQzhrBNTUQwIAgs7dUFE/nVLNjmUVtthGdMa65dA9L1xQDXtzvyCJhcBQldvITrrU8eGZWbe65526xDRyZb8ykpUKWJcivPVLDqcMCBvqYXSRDJO0qktougkrSfktD0JwGbPKKzgRCBIRk3dQGTSxBYCAsaeb+Ae8/346TLFKuMVKbGWRImNBisoZ7IJkBeT+AwUL6oGR1NXPWVn4epAF1ULOGe1u+xSnoc1MXgaySZmaQ3QEQIJIixjpj1hLnHQqKKdDxKLF4V45ZHwIfKhOSaphQWInHKNrFnqG1GG1kG6Zmljf4rL0m9Zn1Vqw7YzSf84nZforBMRtJJ7dWM99I60nvl0EHZhpTJdX0OB0zUnw5lqxfL2kf8dYGElifx/FIUpozM/6s26Pyaj6IQ7vWzNcenYi0HR4diT322CN+nmYyaK84Zilg7NevX5zN5hjmy4CaGSnf6TxJ1xaDLix5qs3SYqCLgVTud2TflCaf4eFYMMBH25QPvNNpZaCGgJGZWdJWadNLGYSoTelldp+12AlrY+ns0xfg2mT7UwZ6GIwv5RjVO59YfsCAZ1qvzkANM9xkjrDkABwrAibOsWabbayHrCza4vycovgkgTQzss8++2yLdotAm/OI5WQcq/w+QEZXMxeuzPuHZNmyP3jawvTEE0+Mg6YsL0gY+GJQkMGtZj+XDLI7AFIquFmQLpdGiPITk3VrNJCljFa31snn2HAsqApKZcyE7V4IiKhwWDL2kOUGQEohQSFbUDFanVKcGJ1mHS2vaS3Fvlm1NojA46SC0bHIsx3YcoLZfwZ0mI0sZU1/fpzopNLZT7OuZEewnIVOa/5aCsZxfAgoGfAqAR12BrG4vgh2aJ8vv/zyFq+hIi/HJs20kYVD1dVUybek640iVHT0SUOlfd93333jkox87TEDWzvvvHPl9ttvj0F4iXuHp7RTAmsGiPmc9iffvpPBHAIkOvwMzpdYwIttpZg5ZClKfj0xW0uqL/uGk8bKMaLORmnHqDazjeuO9HCCoRQcMbDMPY7+AIPypEPTfpd0rNLAC2nfKa2Z+xyDfWRopXMr9UnJ1CLTJh2b9HgJxwr0hxgoZmAmZf+NGDEiDrQz2Ee7xH1v+eWXL6ZArEF2B8CFyqgaI9SMBqVtcfITk5tKM4+U1UPKF9Vnc6THEWizTQB7qibMoDXzhTy284fjQmciDcSQwsRo7Pnnn199DVj7yGx3Sccq73D84x//qPzpT3+KxW9Sahc3Um6qjGrTmeU1ZJXQgQUFhxitLWlwi5ky0uN475xX6f1zXpEaTkodnTYGKJhtA2tJCZxKSqln0IH0VILIeushWS9Lx4QiQ1yP7LFegtpzgOuJ9592xmCWiG1vqNTLcWI2kj1V2TGCThvXIllcJW63SPCYAiHOF2aNCLTzNcXMqtHhT99b0iwtmSEEQOne1lr9FlKBud+VtuSAvlDqGzHDyqAD752MElLB8/OLAVSyjSh+ynKNUgrCpewarh/OFQZJOQZpQJQiZ/THaZNqA+2kpD4UyICkn5TOndrMvyuuuCJmSzJQyoRXMxY5q8cgu51JFyophVy8qcw9j7P+ivL3pDyldMJSgyE6EVywjESz1jNH54KbLCNq+TZnpR2vXFqbx3FjtiwvQJG2UKhdG1vCscpvjAQ7pDQzykpnno+03QY3CYJGRl8JIrm5phsuo/tkSzSr2s4DA3oUpmKwgfd93HHHxfXppDuDgmYERsxGckzTaDXr0thnnA5MswfavGeCQbYIIgOJdipvyxM6HKzvo70qZbuuvB3P14EyaEUBRopWpo4sqYdckxw/Up/TuUTFWp5Ds59LCUW7GMzKB4/TzBqBNgPLedXepJmPD2107e4XZBxxf0vnUf7+udfV2yO8lOVQ1MqgXeZ64hriessnZ2ibqf1Dm5UHSyX1C/KgL1VeZ+cQsmfYuSYPtMmQZICwmYuZ1UPbm5ZCJQTRZDvkmSOjxtL2NPu5BIPsiSydhDTy6XPW79GZZzaIDj2d13ThM8tG6iopYbX7z5Yira1m3QvHgYEHiprkSKPjcdbLlnIDHVPDxjGgsBmBIqly+Sg/qc908tn3slScP4xMp44FM49du3aNHdd0XDiG3EDywIAZXWYjUwDVbGrXyzJIQ7CTp6fS6aD4IlXEL7zwwtF+Bpk3BAEMetXbLqaZcbzo1LOOj1n9tFavtnOROm7NLm+LyUIijTCtAwXpqbRPKUBKxyYPjFgKxDWXr3ss4TxiGQYzagwu1x5LAm22GiRoKqUdp0/EIF9emCoFkmTXEBilFN90vbGDBoOEzT57NrY2nYFkzqV8EiIdozSjTep4Cbuu5Dg/aIPAYAR97dT2cH3RJ8gDba47HqvdArWZ0b6wzKn2HsYkIDPZ9Waob7vtthbtfEkMsiey2v0rSbkgvZDZWdajse6M2ZB0YyWAItBmxIibSGkBJMdn0kknrY4cMmrNzD6NISP94KZLqiHbcuSDGCXOEtHJSMeAauuzzDJLXBtbW4Bi9dVXL2JUsd5xolPGWvQUOJKyyh6YzMZybEhZrd3ejNF/bsasVau3B20zoJPBwEMeAJIlQoDDkozaIIDjwaBX2qILrMvi5kuA2cyVVZGuM94n668Z2U+DNgzOMMhFvQgGtVL6PJ3cEqvSEyiTlnrjjTe2qNwP0ldZhkCmRB5cs40ee6nShjXrNZfUOx+oZpyWFaSB5rzNpk1isLCkdjyvN5IvH6BvRLFFBv3S8aCdZ5CZALK06y0h8KEt4jois4h+U//+/avPp0wRAm0G3vO+QgnHhkwQMv6Y5GLAqnb5Tr1Am/artGsu3d+pj5EGzjkODLTXZo+OHDkyFszLCzSWxCB7IkpFS0j9TictHdu8Cl/qXBBYslYW3CAIwmvTNZpR7c2QtXqk6ORb3BBoM2PNseTGyrpPZo5qi0+UhMqqBDaMLLLtBA0gHVaCIzqwa6+9dgwsSRmj819CAYokPx8YeKDzyqwYH9wwyB5J+z0z48+2LxyzfFSfGweDOKSMNSuqf3MdcT3lgTbXGp1Vtn7L7bnnnnU7ZRzfUrJu2PKNQkGkgJPKSjZSOpcoMMQ6dp7n+DGY2uzBYmszIQze5IMuDNLk6alUD2emjTWzCecgNSWaNWuktYFSguc0I8uyFTqstEdpq656bXazt+O1g+ZkRXC+MEud2ngmJri3ESxSbCn1C9K9rpR+QWsTDFxz9EFZ7pQH2um8I9AuZXIiR7o85xID7EmeLUGBSpZH0a7nW1U2+zVXOztN+0M7zrWVlhyw7JDq/VtvvXVsu++8887Y1yxph4NaBtkTcUaWtLi0hU1aY80NlJH82nRNZoMWXHDBuIVXKfKbYDoO3CAJglLV2fQaRmdJSWGEljVr6YIuoeGrRQod6xjZvow9eMl6YIaRNdccv7vvvjueYzzGnrylFKBA3mlgfSOzsvk2UoxkUwEzBZXMtDHzwQ21xHOJY0O7Q6GpdExYzpIyaVJHn/XHHLeS0uZqETSyljFlRFCdl8HRfKcDzj86aWRJ1A5SlIJZIAYA8eKLL8brkN0zWOefb83FUozUJpUSENUWF2TJEzPXdPzTTC3LL2iTWH6R2q4SgyHQLqeZNGbKOnfuXA0YOZbMZJPpxsRFvuyuhHtd7XlBYTxqQDDowHFhoJgPAm3Sxzm/aMepN7LTTjvV/RnNKK8Azj2OjEgmtBh8oE5EOlfyrBr6TuweUUq7VCvtSMM5xXGin5mW79xxxx1xVw36oAsssEBlnXXWKWoSp5ZB9kTCrBijrHTM2FKJ0aA0c8ZMRz6Cn0aImKVl9LE0jE6zmT1FlNjOjBFpOmpja/xLvJGC4PrUU0+tfs2SA2bQ6Gy0NvNaWuPHDYEbQ6pmnNBRIx2VKr6cP+wTSlZAaVtx5AikCbQ5h1KgzfVHWh0zQ2RDcKOlfSpllih/f+n6IyuCfT9Tdg2j/MzuJ3mBoVLOo3rnASmGdL7ozDNgSgo0g8gMkpI1QjXfEtvxeltQMSDK4GhKoU+z/2RyUUSQGbe0/KA0DDYQHKaCi8wqMsBFoE0xxtaUeD7Rd+K+dvjhh8dBLc6bNCDK1qccQ5ZFERixpCVNapTUd+Lz/H3T52YwkEA7tde8Jm1vmtq2Zr/X1WKCi3Mp7zNxnOhPsUY9DUgwgPrGG28UucNBziB7IuEEZCSMDxo8AiMwMs0NleJCzIQkzIak7UtKCxoZmSbNmWImrB3mePHBaD5BEDPXBJK1a/tKkDfwjE5znChKUVtxncEbbp50+vMqxqXdINJNgvOHDsX999/f4jluoKSJMUtEp4PgsqTZtHoDVzxGu0QROG6maUSfQiZsrUT2CHUiSpglSsen3nEig4TAh7adc4tOR3od+/XSkSVlvBT5MWJtIzOOaZkP1yADy2yVlwoJMgDIYA1LgkpFG0PwyAAEWTRpyyVSMOn0Iw1kcSwJmkoZsKmH2VcGbFKbxL8E2l26dCliW8W2oO1h2djjjz8ev2aJCvc/6kYkTN7QL2ApRynbdOXtE9Wy6XezvIdZbI4R1yKV62mTaNcZOKWSOEt9Sg2wwdIVgux8l4PzzjsvDsIzw18vQ+vHJs+GGBOD7J9ZXhSIGTJGXbmIWf+Yj/SnkWuCSmZHSC1v9sJBtWorW6ZGPxW/odNBQMkxYg1kaRdy/n7pwFN0gpF91noyw1h7vnC86PynrW9KxuwZHQ1m0PJ1VeBaZNCLVKiSlh3k5xMDfMws0jFLA3sESSnQbq0adjMfp3R8CBhJld9hhx1aFIBj1rFXr16xYA6djRxtFoXhSM8sQd755D7HlkHMzNJW5+nz6Zxh2zeWSpEVUVI7znGqvWboxNJhJfOBKuv5dousDaXjn2bTkpICovz8YuaRWiwHH3xw9Tke43jRvlM3o3Sk79I/SgOBnE+pCBVtewq+S2nHa9F3ooApExPc91m2QiBN/QMGbdg5g6xTqq1TKC4NcpWgdhAhrdNn4otjlG9dxkQX7Twz/7XbwZbMIPtnvEHkqSiMQlPdkfV5rHckqM7T5JgN4YJnDQ2pT3xdkn/+858xlTCvGJqOH6mqZADkSh5ZZOSQYlTpZnndddfFmyoz/bV779JpK+kG2lrnDKz/ZPCKKsZjunGWdLxAEESHgnWyrCmmbaKjlgJtZvcZGCxp6Uo6j7ieWHfNMWFtI1Xo084PoK2mc88MPx0NamhwPEvcvgzUfOC9055TAG+XXXaJxycFiXRiGfxjIJlMm3QdlhJoMzuWzwYxuMV1RWeVdcR0+POqvMz2U0iIugilyNts+kQUvsu35mLGmpn/PEuEwQiOUbMPPrQF9zfWyBIscr/LzydmrmnL8m0pS8JEBAPHKWuUPhRZELTfCW0RhTtZwlnKLH/tdVe7zJCJCLJr8mwIMGnBtoyltN9tYZD9M6HDRcDMDBBpYPPMM0+1YeMCZ3aIxo7OiCpxhIziQHTm87QUDBkyJM4WceHnAVCJATbVjJmdZuYj72QwYk3Hg0A7FacqLXDMG3oGZkiV5yNVewbXHB1Z1maXNELdGgKeVBOCWVcG/kifZ4/elFpP6jizknlxnGaW2hXe9+STT15tj1jLyEANQWR+jXGOcU5xHGm/GKwosYo4M0FUm09FKgcNGhSzbZiJTYOmqTgVMyMlLDeo7eAz4MBxYSCGwZu0vprBULbuzIugcj1yLVJgsIT2u/aezvIU2h1qQXDdpWuKwRuy/vJ2PVfK+ZTf7/Lzg+uQ4lN59XWQwUX2CIOEpfSdaoM/rkEmudLkRJ41wmAX12btYHIJ115+nNjRgf4ly5/SLjUpA4BjV7vLUWnb5o6NQfbPhFFECnaRDsc+e2kEKJ2I3EBSoM0ampK01sATNHIhc4HngTbpmoyi1c7SljpKTUeW41GbHk7jyBoi1vLX7vdYEjqwLC+g0AtrGOlskO6b0GEjICIYKOEGOiakOZPulWNrJdquVCiHNotgoKRjxe4PFHZjGUbtsgOKdbFbRL5NGUE3BS1Z8pJvN1hSG85sI8eM+xlLD/IOLAE2QREzR/n3lnROcXxY/0kwzQxjmtUmKOSYUOWYtoq0TIJrMidK2m4xPy/I1GLQmMF3BmVYesFxo7269957Y6pqvQ5/KfJjxTXG8SKLJA28M7FDG845ROBIf5SBU67PNAhRUlDEMaC9ZkaWrC22OWXiJu0MAQaVGYQoMQMpYVCG/gDLLsiG4BojA5fCsUwI0rdMWW75JEUpgzZtYZD9M2LdEDdNtuVIHTJulKlxozNLmhipcwTdpSFFh3STeoE2KT2pkAnHjDWOpYxQJ63dBGnsSA9nkKZ2xuziiy+Oe6+XdAOtPTYUfUnXEymEdM5IzczR+WC0v1Tp/Pj9738fO/XpsXSzJMWQWUiKMuWavaOf8L7JrGG/T7ZWwimnnBIHTGmv6bCyXo9lLAzW1K6ZLQEpurTJ6VxiBojClAREzDTmaap00hgcpCJ7yQgO6RNQm4UB09oOKu0XARPXJUF3abP9KWOEFHmC6RznzjbbbBNntxlE5RgSPJWMwJp2msCI+xwDEykVmkE/Cngx2EURWV5TwoBNWh/MdcUHQSFbd6ZaIxRg5BrMlybSlnEfJBOwpL5T/l5ZSsCSTTJH0zlCnRr6SWRqcR9kuRR1gNQ6g+yfAQ0ZFzedMmaDaPgo2JX2lcs7Joz6M9tNClRJKHzDjCLplXQ8ameRuJCZbWQNe66Uzkbe+DErREee9KaEGTM6rQzStFYgr4SbRe3afEbs0/p9Amw6GOn84iZLsaqSjs/Y3itp9XQ4qGicSzMhpB6WigG/AQMGxDXpjODTuScIIu2SNooBLtbRpiI5HKsSRvRZUsAAMjMdDEIQWOdpz2k2NhUY/PTTT6szs83cuW/LdUcKOMsQKKBHkEj2EcZ0XEo6ZuxaQHo4QXaeKZLQhjPTyPnEjiwlHZt65xNLEtNgBO0VWYBcZ/mWsMz2p6Uazd6HIgOS+3/aTz61V8sss0z12D3yyCOxfWKghoHAk046KZ5LZC2VViMioV9EenhaXsCgQ+1uNWRxUbOF+2Fpx2dcGGRPBMyiceETaFPEI0lbl9RWO25WtR1Q9tijMA4z/SmtMGFwgk4+6x1L6Ljm8vd7wAEHxLQmtpdifSgDMtwkQIef9CZuIPUqhpYkzbjeeeedld/+9rcxxZAAO5/Bvuuuu+K64jzFsISbRX4+MfDAQAQ7GiQULmE2hMCagUBmkuh00JEt7dqrReo3gTZ1EAioW5s5KSFFPGXKsO0NHS46tLvttlv8mpmONDPLNUcAyTnEemLufQTjJcyi5fK2hXoi+Tp+arUwUMFxos5GwjF99NFHKyWirWGQZo455oiDVqlNzzNs0r8EAenzEs+nxx57LGZCEhjlS8NoiygoyHVXO3CKZm/PGTRmRwNm8NPyQvoEHI8c/SX6VmS9kZnEcSwxawQMkJI5Otlkk7VYVld7XTFYyjHN9xDX6AyyJ5DUeDG7QTGhK664okXgQ8eDEUY6atxsKfwy++yzxxO32dFpT2pvltwgCHyYuU7phQw6kBZGMFBSFfGU3pQQDDKqT/oOHQ6KUJAizuBDWjdE4MixY0S7VFxrbK9EJ560XQpP0XllViSheAdpTwSUJZxLSf5eDzzwwFhsiXaH0WgCpYTlGKzzJ62OfWiZESllVD9VLh7bjDbHJaWO1+4eUQLuYaTLU/E5Lw7E/Yz0eQLtlFXDzAj3ODptfF+pHVgceuih8Zoj1ZIAgHWhXJcEitSPYOafgJsBVK7LEoPGhPdOUMS5RKZbvXastc9LGnhnCyraa84dlhXk6CeQIUjfoZRlLPnxYVkBQTWD7ezEwuAxS3vqnW+1M7YltE/1+tS012RFMptPvZH8tfWusVLaqJ/CIHsCSCchFzOdWNa/kELICD6BYsLsGjdSRmm56TIa2exYD0MxrnwP7HqBNgVNUqEFZmYp0FHSiFmq4JiceuqpMaWJdVRpr8KUbsisNmuHEm6kJRyj1gwcODB2OliCAIoMpmJnpNkzEMFoNbNp6SZaSucsYYaa65DsGXY5oOIzN9S8I0vhF5YmsDarlK1LKMZFcTy23mrLjDaBdu3uB6UMZHFN5dtO5tfQf/7znzgww0dtW1Zaxyxvi2l/GPRjfSzHjvsaM41kIaXjRxEm+goMLJcysJW/P9odzi/SnlMxONZak8G11VZb1f2ekuTXGVkOBNAcK5Yacl/j3LnyyitbfA8D9CVtrZQfIyYk6IsTaDOpxeAyE1z0qRjwY/kY/x5//PEtltqV0CfIzweO0/Dhw6tf0z4xgUOfIC/GXMJxaSSD7AmEGwUd/TQby8gQI9cUEshToemMcAIz2lgC1saQ/kVQmApy1Au06cTSESE9nMJnJVXApHNFsaB0PJglYs0exYPSdhNInVdSfVkfSrp9roRj1VplYgZmmM1OCCLpfJAGzaAXo9qlpavmKXQM7tHhSLOvnEt0bAm0822DciUcpyOPPDK226xFo5LxmNBG8TpeT8GhUnCupP2u0/6ptW0N7TUzasxApiVRpXfOWJrxxz/+sfKXv/ylRdYEM0Yp0E7XWF77oNkHtmpn+UnZ5T7HbCP/pg4+A/ScT1Q31v/tHtK3b9/Kfvvt1yKYZjCeQqi1gXYp7XjezrB8hcF0Zqi5vzEIwdprUqGZsGDyixl+sv9KW9OfHyfuX/SZyJxhYDRdc8QtLN+kP5qv61fbGWQ3AEFzWk+dborcLEi5TOtiuHFwopKSyXq+2k3cS5A6YqTsMAPEzSAPtPOOGh25VBiutM4GM/kp+EmBIIXw6KCR9syIa449nvN910vHzeOMM86Io9V5zQNmtpkZYblCCUVf6mHpBWm7zKbRucilQJvH6aSVisI3tNHMVI8t0GbG++STT64W+ioFx4VOKgPHKQU1td/pX+6JBOKl1odg0DMtjWKglGrPKRU8R6DNbCQfzNjmHf2SBiZYVterV6/qThAMYHHMyKRJCLQ5hrRhpcknIriXcf2RKVm7KwaBNrVZWIqQ9qQvETOz9LnzbBsGJiiixzFLWRKl7+9MJhZFha+55pp47jDQPv/881cn/sj8Y2KC88ltc8edQfZ4YoSM0R+Kvbz44ostOiEEkNxASblgnTFIk2btDEWYuKmUJjVgrCGuF2inNY90OPKiCyUihal3794xJTwdFzoeXbp0iTPbnG8E5Nw0mJks7eaQkC1CQE39g9SpZVCCDhszk60poQNb75yg88FWeXTQ2BqoNtCmcijZFKWdT/n7ZbvAtgbaJc1+5GiPyIaoF2hzTBh8pnNGUa/S0Bklw4EBmFSwi1RMlvYwe5YXPQPHiAyv1E8oSbp+aIvSAARZbGQdpZ0g6EelYoIU+ixtcDRvm1KWA5M3HDPaKVKfcwSQZAKw/VupfQImHkgR5/rL22hm+HmcjK18iWZr642bvQ3nPEkDWQzyTT311KMVHibdnqyA0voEjWCQPR7SBcmoIp0JbqCkf9emjZN+kWY6uPkSFDF6VDtTW4oxBdpshUPAyHrsfHP7EtR21jlXGFEklTAVFkqBNgWHSHmi6jqj1qkifQmNYD6iz/tOhUxY30gF7LQtFwWZKAyX9nksTV6Ii9lECiyltcYMRrClIMeMfdRb+74Szqdc/n65ztoaaJeqXqAN2ivadTr5pXVcE947GSOkzacAkX9JV2VQkIyuHG1ZKQM2vHcGRXMsDWOAj5mzfCcIjglV7JmUyIPrUgLtvE0irZdK2WnvZ2YbCX5I6SV7K8e5Vlr7nc4X7v2kiVPvKBUzy4uaMWvL89TfKEmasEmYpKE9ou1J110KsGnD6SPU9sNLPKfGh0H2T1DvJOPkZU0DwWEeaFOQgpGhNFLE+mLSfEqoIj4ugTZ7q7LlCwE22wekC7uUG2mOkfoU6HB8CIRI4U2BNjdPZkiYiWRGu7XKmM1+7dWeG+wbzkwQezduv/32sdPGiDUp0CVhSQoBdXLYYYfFtYwUV2R2P623YkArBdolZ42kIJBrrrayOEszDLTbHmindELSMRkcLLGwYB4oU2SJOix5oM15ROefQPuFF14Y4/c3IwKglKmWTzSQAs62lGT6UTciYRKDVFWuwZJReZ6AiBTwfNtJlkQRaNP/pHBjrWYPiuq1LbTj1EDgOmOtdb0BZKpmN/uxybEdHoUD8yKLoAAqmWsE2PnyAiYGmbyg+GlpbXgjGWSPo3RRcnMgwKEjnxq2FGjnM9qk7bAfNp1bOh15R6R0eaBNCh1rrfL9U0sMsJkN4jiQrjqmQJv117yG84nU3xLkN0Q6rRQvo2AJtQ/yQSv2AyWNLp1TjPyXgg4pA1XTTz99XBNLWipLWQisWZvG1m6sc0x7GBNos7UZAza1W7+UIHUcKB5IESGW/jC7kVdTTYE2s0jWPWg90OY+R3FGMrfIwCm1sGBbA23OKc63PGBqduyoQho4faZUNC9HJWOqiDP4QNYNM7ZsB0dQXmJ/IKF9ImDM05vpb6YliixFYEcW2noGm0uR9wk4V0gN556WB9pce0zitDYZUUL7RBYI667Z7i1tycWx45oijuH+v/XWW7dYNkZWINdeSQMRE4JB9jhIJxtBMg0exYGYuaYjTwOHESNGxNGffEabitqXXnpp7KTVVoBuRnQa0vZJY2vA8kCb2ceS909NSPtiNpZCZ3mgTdDIQA3nGLihUKSJ86/eCHazIhuE64/3ToYI75+ZNM65FDQxGMFNl2UZpZ1LdOBZb0agzbmUF8ojLYxBCQJtCubh448/jrNLJXQ2WhvhZxT/oIMOiumqDPTRuWAZQkLbzfIMMkhKOU75ddOW90xHn4GKfE/10q69cQm0qRtB57+U84kMLQYWSNWtlQaPabNXW221OFgz66yzxuCaVOiSB2zADCMDyiDNnqUs9DHJkqQ9577HzCN9hlKOUe3SHvpGZEUy0ZW24qLWAe0467PzbU5LwkAD1xNF3+rNRtNub7nllnEih6WHVKtfYYUVYn+zlC0EJySD7DZKJyczQ6Q0HXHEEbEDwcwGRSfo6N9www3xNXT2aRBZj5UXQysBgwmkdvFvWicMUnZbG7GvvSmU0jEbU/oNQTPnVG2gzWgk6cD5DBLnXynnGdcfywlSxVAKCRL81FZRrT2HSljfn98IOS8IeDiHmL3Oz7cUaJOWWTvrUUoHLWEglNnEtNUi5wmj+gQDDKISgCek1pcwSAoGrxigqd0vPLVFrZ0nDPyl87CUdnxcAm12GWEf7NqMiBKuOwawyLLJ900n64g0aAKjXXfdtTo4z/lHf4rldunYlHI+1W5nCmrW0JYzeErbxDZmZAWQVs+AfO369hLOp+Soo46K/SL2oCcDia06CSrTllME2hTS437Hrj8l4X5GAF279py1/BwTisCxLptrknNpvfXWi+cWBWOd8GoMg+xxwA2AwJkRsxypTVzkV199dfUx0ldT2ma9tKhmxGwYAxAE1HTy8xFsZs5Sh95RsZaYkaWwSy06Y9xYSQdPHRPWX9XeQEs4nuk9chNlphHcJPICOVRdTWnQpakNsNO/rLViSUEq/pY6bqTMkR5W8lZdIGhmLSipl8wsEgRRsIrsI4LtEtf003knZZflKByThIFlZjfaooQ2qa3y9pqAsnv37nFWqbR1jgwcs+81g8UgdZWAiH3CWd5DH4p9jOspJWjMrxsyHmiX0lIo7nesqb3sssuqg1/0SZntz9PIS0J/gN17GGwH93/aLgqe0hdNgTap4xQhLuU8yjNE2JIrL4pHNhbr1GmHGIygTU/FYmuVdrwmBIPscUBaBemnBM6cqAk3DWbT2MMxRwNIJfES9lDlRsCFmxfi4oaRLlKOG+uFUlpYyRdvbcdq9913j8F0nkaXXsNNlUCSoi/5bGwJx4+gubYaJrUQCLI5HgSPKcAG+6vSacv3rC+x+izF3+hQgM4Ya9IpfJa2DUrnFudT6cEQsxwpkOS4MYqf2ijW/HPcKFRZex42c00I6ofkqfLpnGGghgFmOvmqxOKC9YKbetdU3l7/5S9/KaL9rkX7TIozARCzsWxZxhZdqaAgHX12zeAcLGnwIcnf83HHHRdn96ltwPLDlLmVZ4kQOFJgkNTeUtrxRx99tMUuBqSFp4J4VMdmoObss8+OmSJkvDFQmtYgJ6Vde2SIEEzTV6LYItcgM9X0pZjFps/EcleOS4nX3YRmkD2OSIejI0sHnxsEqZdUfKzdBicp5aRlZoybJymq+SAD6z05RnfeeWdcE0MjycVspd7/W7uez3DQwcizIdLjjNSyRVwp5xKY6WFtHjMf+Voqtp5i4IFRatbQ5ucf64k4/0rpcNQiJYwBQNZg5emoXGscQ55jhhb5uVTK8UrvmRki2vF8OQuYtWa2Nh/8oo3PZ3ObHZ151sLmHdmEjj3ZD/kAc6mY7SerjWyj/F42cODAaq2D2va6tnNfWmcfDP6dddZZsQ9Fgaq87SFFnEGckq63esisYXCPDBpm/1ke1qVLl5jamwYGSb0nuCYLoJR1s6n+CunP+e4ZaR96BkVTn4Bri3oHDBiarfVyPIeolcFEGDWlUl0f0Kbn/XY1lkH2eAbak0wySeXggw+u3lSbvaGrJ3UmSBeng0aBF9K+SGOiI9K7d+9YcZ0GkkaPtTGsCS1Nfm6w/oXAmdHXhBsEafXcTAkmQeP38MMPV19TQqDNiCtBNAW7uM5I4c0HsRiZZpSfEVhS7em0cb5RuKOUDkct0uK41lJ6XC0CS2a0uQZLyKxpDQMQnE+k0NGhTwNdZE1QTXWzzTarXHLJJXHbM9qqfNlLCdjehfRBOqgUE6LIILMepPYy20GbRdbWscceG2f+8+0qS8GWNrRPrAHN1xfzOW0QnX21NLb7FgNenHNcfyXc4+ohMGTggUJv+RZmPM66Y/qaBEgcH1LHU12gUtbNMrDA/Ytib+xkkJZApT45xc0Y/ALZSPSdWMte0vk0pn5PqrqeI9imhhKZE5owugSNsxlnnDHssssuoUuXLmHgwIHxa3Tq1CmMGjUqlIb3jS233DKsvfba4be//W3YdNNNw5prrhm+++678Pnnn4fvv/8+9O3bN+y7775hrrnmip+XhPOic+fO8fPBgweH1157LTz66KPhj3/8Y+jatWtYZZVVwp/+9Kf4+TbbbBOWWmqp8MUXX4RJJpkk/OpXv4rfx6BYOtbN6qKLLgp77713uPnmm8P6668fH3v33XfDtNNOG958880w00wzhdVXXz1MNtlk4dprrw1/+ctfwkILLRRmn332cMcdd8Rr8ocffoj/luSzzz6L77t3796jPffjjz+GXr16hXPPPTfMN998Yc455wwlSdfNSy+9FNvtI488MvTo0SNcf/318Tq7/fbbwzLLLBMOPfTQcMghh4STTjopfs+gQYPi+VYS2qFTTz01/POf/4wf888/f5hyyilDt27dwldffRVmnXXW8PDDD4d///vfsT2bd955Q0m4lmi/11133XiPy9Emcc6ssMIK8dituuqq1edKaLvHpLX3/uWXX4bnnnsu9O/fP7bzTz31VLUfle6XzezTTz8N3377bWyfudd//fXX4YUXXghTTDFF9bzhOBx++OHxmrvyyivDoosuGtZbb72w0UYbVc/JEu53W221VRg6dGg8Ntdcc004+uijw4knnhiPB33wJZdcMhxxxBFhxIgR4cYbb4x9zmWXXbaI84n3feyxx8Z2urX3Ot1001U/p6/wv//9L+y0007xePH9mkAmUPBeBGY50ow2e82WjhFXZsyYza4tpEBKHTNFpIsnJYy+1mKGbKaZZornDTPXrBlidogqqnmqNOuM+EjHqITUQoqYMFK9//77t3ic40M2xAwzzBCPHdvgpBHb2rWypZ1T6ThQOI9jk6rz5uv3Bw0a1OL8KvE4MaPBcaA2RMLsNfvykpqZ2iXWsNOGscylNPksCIVySC+sxXKWffbZp8VesyW0TbVLeJhVJMOI5RcUXeJzrjH2o2WdLCn1PEZmQEnX2rhkEFGpnmwIspCYxS5pVjZl1TDbSr8o7UwDMiFY3pPaoDQTy3KotFNEaVJ9HzIgyaihOjYZk1TDTpXVWV9M6jPL6ziupWS1sbaavefpJ6XdH8b0nukz0YYxg816/9K3x5vQDLLHoC1pJqXuVTwmO+64Y0zHpOo6FzvrQ+l4kN7b7A3e2PYCZ/1+XiCPauEUoiBFrDYQSkrpdLB2iMIc3DjT1kmkD7LUgGNGQbNUCK7e2uIS0sJqb4TpPZOqOvvss8djl6M4Dp04toIrFceGDghtNGmGOQJtOmQUzKEwk/4PASLLffJq/RzH3/zmN5XddtutqGuuFm02x4AaB2lPddLESVdl607OMz6ockxHtpRjlN/bWZJCMVTaH7R2DFhby+BXadu+MRjD4B5F8PLlYGm5FMEPSzXSIDLBE/dGlm6U4vHHH4/HJr/nMfBA0Tz6SgSXfM49Ly92ylK7dL6Vcj6ROk+RPNqfsQXaLPEhZmE5Xmnb400MBtl1pAuUmwQnLB2x/PFaJe1V3JYgmXWhdDzYv5GOBwXRuPhLGVlMWDeVFzej48GMbKr8nBpDRmBZp04wlK/RLuU45e+Va4hzh6r8dPJZH5rv1ctNlnXrFKQqGevTmNVg0IGZfRAQMYjD1oEUX6JaPceRY1j6TZQBP2aIWGf95ptvtmjPad8JHtn9oLYYWqm4zhgUpfYBM0dk1VAYjqCy9HMpYXs89k4HwSTnDucUs7Ksl2U3ktSJLSXQBoEgHX5qH5D1kAKgsR2DUu53ZPmRwVZb5DR//xQ/4/634IILxkkLrkM+L+XaY3cDBqo4j8h0YDAm9aXIBqRGBBhsp0/FNVdb6b+kay4F2kzYtCXQTs/DGewJq3kXKfxEae0U6/Q233zzsOKKK8a1xXfffXer64pYt7f//vvH9WvNLq31YD3jq6++Wvc1rEk777zzwvnnnx/23HPPuHbmwQcfDJNOOmlcC9LMa2MS3vsmm2wS3n777epjrG1kjTprGsE6KtZTsZ6YtbKsNzrttNPCfffdF5/nOHE+liC9V66hM844Iz7GuvU99tgjzDLLLNXXsY6INf2zzTZbKMkNN9wQ13nisMMOC8cdd1yYeuqpwyKLLBIOPPDAuM6YNaC33nprXJO13377hRNOOCF07949DBkypHqulSBdM/zL9YYFFlggnlecO6uttlr44IMPYnvOaziOHN8HHnggHq9mV69uSN7O8PwMM8wQrrrqqrjekfbqoYceisewtHOpnnSshg8fHo8HWCfKekjqj7DGlnasZ8+ecZ0tx6qZ12Pn5w61NC688MJY14AaLS+++GLsG3FvS9dba0roF+Dee++NtQ822GCDFseD95+uTdbXst54nXXWiWuLadufeeaZYq69VFdlnnnmidcTa4ZZP0y/k3veZZddFoYNGxYWXnjhcNddd8VaCKzDzjXzNVfPEkssEa6++urw8ccfx/OFe19+TuWo/ZPQRmkCmsBBfIfELBAbtTNKzajj9ttvH0fVqOyoSuX555+Po9QplTAfCRvT6GEpI2bMsrJmj5nsWuzhyHYcrKFNWN/IaDUp0ayDZCa3ds/1UqTzh9l9jgNbTrBGPWFdGjNspZxLOPfcc2NWCOs7H3nkkTjjmqqIszUezzGznWO2lqUspaXNpffLceGaYpsbZmFZk4233norLs3gGLL2Ov+eEuTvlS1xBg8eXPe5PI2QNPH8eivp2qsnzQ5x/FgXSlovS6KoKUHqKm14icfo9ttvj3VG8raIZT8sFSMLIq2dLel6qzeDuOiii7ZYcpFLx4YsiHpKacfBFmZcX8zqk5VFn4lZa9p1+uNkTKQZWe53JV1ztTPU6bzhGDCjzU4jbZnR1oRnkF2Dk5Ktbti+BBRYIOW5tlEs+UaBTTbZJHZWNXqAzX7XeSETsCUHyw9ITWU7Dm4S3CzY25i1VqT0gs4aNxYKn7AfZonStZVSxwm02S5n/fXXj+lQJRXq+Nvf/hYHZdKAzXXXXRcDx5RSx/p01vCBc6veuv5mv8HWtsUEPywp2GWXXWKqMx0O9lxP+8ySdkhKPWtmS9qiKz8PSL+k1gHtTJ5mObb7Win3vXSs0vut974ZxDrxxBPj2myuUdJ5CbJLap8SOvZsiTfttNNW26OEwXiWQpH2W5vSW5q0tpqlBpxT9c4RBrXYCzrtt16a/Fpj7TqBNenh9IcooEvBONLD06RX3q6VcM3l7zdtN/n73/++xSRgCrTzYmiltN3tjUF2nX3jCKoZlaZSL5Wy8wCbUdq0pq8EtR30dME+8cQTcUQ23Qi8gCuV++67LwbPBEY5bgh06gmy08w1QRMdfwJIijHl1aAZ2KG4TjNra2eeQJuZEGZrKaaXjlMJI/rsO8/5RPZDwnp+ZvJ5jIqizHInrOdn8Kuk9qm2U8VaYgb/8t0eyLyhU8v1RruVzivWq5MxUYL8eqPC+p577hk7YVxXDF6lmf7a15aOtfxjOi605QTbZAS89NJLxRTwqncsuO8xWMz1R/2R2r3FqfrMGu3S7bvvvnEwgvOl3oAOhamoH5Fm/kuemQUz2NQa4biRidTa60pz6KGHxgEI7vnsksHkDgPMtcXQmJho9vaoPSs+yE4XaT5ruMMOO8RthKhcyExIOkGZKfrd734XZytLuLjz98isGQF26tQyAMGM2q677joR/8L2hZQmipUwm5ECahpAKs/mBdDyEesc51mzzzoS3NApbcuoczr/6OiShljSFi/MBnXu3Dl2LpZccslYeR10zEgDY6aWNOiEokucd9tuu20RbRNOO+20WGAqRwEqOhZp8CE/h6i+zvck+cBWKdiai8EZBpEZYCBDhMErKqznVY5LOYdq5e0v1eYZ5MpnFGuPS73j1OxteP6eCazzAa0LL7ww9guYiaX6c46BnGY/Nm05bgz6LbDAAnEyp3ZAlH4o2SV8lHCsWGqRtgNsS6DNjHbtAE6JuO6IT8hISks1aKsmn3zyyuWXX159HVtTErOUMMPfXhUdZOfr90i5SKP5AwcOjNsrsI4oD75pEBgVqhcwNZu8wWMrBVLhWIdNenOaDWKNKNV683V9pSNNnFlrZsn4YH1e6mzkNw1mq3MldGq5UVIl9ayzzmoxwHDOOefEUdd6am+8JQTYDOJxw2T2h4EtgmwCodS5IO2ZDgeDgcx2kxVBKmZeRbzZzycCZJb0pNmg9H5ZZ02GTdoPm+ORniNjhKVAJXReW8OWeCxTyRFEcj4xSFO7nVBJ8vOCNNWDDz44XocMSuT1NZr92mrrMSL1m0HkGWaYoXLppZe2GCAk24ZAu15GVsnXHwh4WPZDXQgCJWr/cA2ynRd9TtrxEnZiacv+zvm1Rmo0A6Xsrc6yulJwDPJ+D+cG228xoJWWY3Ac6Tfst99+caea2uWKMNCeOIoOstMM7WSTTVY57rjj4ghjwvY4ffr0iTeKo48+Om5yzxq+oUOHVppd3rCRKk8nn6CIC5sZD9JS6IBwUyANM+3BW/JFnB8zZofoZDDbmNag5bNmPMeNpSQUL+M649+8qAvXEyP6BN7N3qloyzlE54GBmbxTz8w/1yCzH6nTyhp/Ugq5oTKoQ7pYaWtB0zXH4CgzHGn7LdolgiPa9tplGwceeGClROm6ItOBmY30WDqGrC3mXGJLOAZVS8aAO4MOzBYx4M6SHjqxece15EAbDLaz7R1LeQiySVtlsDShw7/KKqvEY5dvwVi6vEDVAw88EPtTU089dcxaYm9sBsBKythqy/7O+bXGMinueyX1E/JtJZnQIlvrueeei1kQZCMRp5x55pnV5WTc+/hg8lATX9FBNrMgjCbWVuZNGGFk9oP0J9aw5UF4M8o7Xen4cKO85557RhuFpaPGjBEXM+vW//vf/1ZKVxtoE/zQyUijrtw06ZSQFZDvU9jsGKBhBi1fj8e5lm6U7DE/00wzVfejLyVIbE1Kp8/PpxRoc0NNgTbn0DvvvFP5/PPPi6ginh+P/HMG/7imGPhLHRKWF9A28RiBEiP8BErN3oYnrXVC0+4GdPBzBEgETYsttlg8dqUGknRcOZe4xyWskd19991jkcG0o0Zpxyc/n0hH5VoinZ7rjUCJ2UUK6RFcJyzNoCBTSQFRrrXzo/Zx2nOWs9COJyXdA9uyv3O9tr+E84qgmkEI0C4z0J76B6kOC/2CtOyAAVKWuhLTNHNfoCMpKsiubdxYl8YsGutEW3tNbcphs0pBcmrcmSkjdYmRVhq+2vWLrDnmwiZwooDOCSecEB9v9uOU1DbwtcVL8kCbCtlUxeRY0hEpqXgXOH8YpWfAhhsE75vZDYqYEFgzE0lqPVVD6bSVuuZqbNdOCrTz1PFx+f5mQDp46oyyPRBpvazpY5sXliLQEUlr/OjwkzXCMeMaLGULxvw8ICOC5QX5loBkZTF7NmjQoDgAyMwIs/wsPeCYMatWu5ylFCwFY10jxyI3bNiw2HaTjcN5VwoGqerNYtM3yDGzxgwjy8fy1PHUnyghIAKZa8wm1qpX/6G1Y1JCO16LtcU/JdBuZrx/YhTua0xkkUlbW/iNPiaDyWzpSdvFFqe070kpfcz2rMggmyIBI0eOjGseGZHl89qGkIuekdoSHHvssfE4MCsGAh/Wn7OVFKmrSWv7YRNosy1FKfL3zojhmCpekmJIVWMawnz7qdIaP2bOGNDinGI2f5FFFolr/FljxeMcHzqwfJCyqvqonk2V3hlnnLGodWkguOZ82X777WNHnnMmBUNkS7AOm2NDoJ1mtP/3v//Fa41AsgR520NqPOcJ6z6ZnWW2MWF7M4JJrj2yudLgH5012qkStjarF+Rw3hAsskwq7aOekNVGh5fjWZsJ0IzSvt+19yqW9tB+1xbt4l5HlgTnEoNfJQVEYI0s9zeKC6a+FChQefXVVxc12NAa93ceO94/wXNCFg33Oma0Ux88vybpL/E87TjXZYkFPduzooJsMGvGCUmjl7brYt11rb333juu0y7hhKVjtfrqq8ebY5rBoAPPOj2Cb9Y71gu00+eM8tPxyDMCmlXeYeBmSppzKgTX2utIryNtrqS1Vq0FSaxFZ80sHVg6IixJYIaRNGjSC5lxK/X4tBUBEAFTKSmFpFKmc4K2hrXD1DtI+/Gm45ACbQKhPHW8BLVLfSgqRJvO8WIQkBlqAm0CxYRrjUGKyy67rHoMSatnh4S0O0KzyjvvtEN5wEi7TlomtUZS/QgG4inyRYYXWTccp2bPcKPvk94fO2fkKayswWYZBoN+eVBObYi99torFmLMA81mx7VEu0RwlDJp0nnGckMy2krn/s5jx7nDxE1+DjEpSBvNeUQQnW8FmzDoxxrsekG4Jq6igmyCQDobrAEFJyTFTbihcnMgpZc1DWwLwz6GpazfA++bGVcGHdLMLMeDCr4cH2a2k9rOPceLm25J67I5XgQ6+b6EtVpbetDMWrshcs4wq0jlVIqf5eiosXY9X/vf7MdpTKP04zJ63+yBNh0OZocIcjguDAIySMr+zsw2ptTxdMzYDYLUcWZjjzrqqEoJaoMZAkGWZmyxxRbVQWKOC7P/tOXUR6h3b6TYJ7tqPPPMM5VScO+iUz/zzDPHOiOpLgSDNSzLIDiiHgsZEksttVR8jnRM2qtmVlubheUFLLlICLAZgD/yyCMr9957b0xVJUPpkEMOie04Sw5KmO1Px4oUemb966GNYhYyX8+fvq9E7u/cNtQaYUIiYTcf9qAn0E7Zt6B+RP51s/cJOppigmxG9inURSeC4i8Js9mcpIzys+aBlAs+f+qppyolyBt69tRjrRWdjhRoM6OdCgtxQ62HWaN6s7nNirXDdMBIXX3kkUfiY66v+r+112MLEinKQYeN64ugm6IvzAyRIlXSzSE/RmybxCxQPsig/4cKqkizZgTarH1k5ohqvLWBNuchA6n1tg9qNqSEc/2A64e0eAJE7mNLLLFEi9cSaDMjwkAXM40Jx4/12ezN2+wBdt7GcCzo7PMv2wvOMcccsRObBi1o57m3EVwy45YyIxi8YIlUs7ZXte03AzUcCzLdCKQTrjGyHnr06BHPt7T1FNcp/YW0s0YJyPpj8I9Amvacme2777471kQgUOIcYkCH2UZmJktZvlLL/Z3bdt0xuMDADccmFRNMFempb8MgBH0G2nHqjpSYVt9RFBNkk57KVlwU5shT5vKTmpGiZ599ttj1aGkUkfXVtYE2hc2mn376aopmaQFkjuCQrcu4MTByn0ZbS27o6HTSSU37X7d2LBjsojPP2j22yWHNPzeJEvYFrXfdcCMlVZ4PZmwpQMX5VTo6o/l2iWSOMDhDJzalyTEoQaDNDGwKtOn4EzCVIk+lT+cN9zp2xmBrpX333Xe0QPvcc8+Ns7H5tcb1x3PNqnZGjEEFBtuZ9U8oxkiwyHKDvM5G6uh/8skn1Sw3jnszys8JskhIEycg5PwgYCTTLQ3qpAEwBthZhpe+l4EfAvIS+lEJWX+cGywfYwCedf3c28gcYeIibavE82RHlHKfc3/ncZcGYOhL0e/mvKHNTseCwSuCa/pbnFup71Rqf7y9a9ogOz/hUoNGJ4QRRyr1ERwlJay7zuUNPI0aa9DolFEhFMxm1M5o8y/raEpr8Fq7GdLhItCmcjHb36TjUmpDRxDE7AUd1LEF2txEOO+4uTLiX+o6ItagM3CV9iXmGuSGSoc1Ke184v2yZRIF8EgHJ1U1oU1iYIYAKbXZpKqytRIDg9TW4PtKqSKeY201ndRUF4O1xCeddFLs8Oc1NcAgRV5wqNnRIc2zRAiIUtDDumuk48FzBJKsf+Q8zCv7s8c4M0j54E8zp/QSEBJop3XpnDek9VKwct11161b24VZfortlXCM6qGgYNppJb/O6CvQ5yStt5S+gvs7/7SBLbZRTNk09KUYmMgD7fQ9tE/pe0vrO3UkTRlkp8brvvvui6lzG2+8cUx3YmQ1jQ4REFB4ISlhZLEWjT4z+3ROCRZZ65Fmqhkto3AOnYratMsSOma15wRLCjhvGJBIKeIE2hw70ndoAEu5ebaGIkvcIEi5TLNibb2uSjinas8LqmSnG+eNN94YZ2rT9ZcGKkrFDBoj9aTp5inMrHukE58H2jzP1iV0ZJs93Tmpva5okwgmmYlNAxPc7wi0F1poobpbMZXSTjHDmLIfUjtDuirBIscs1RLJA20GLPbYY48WP4fAoIRiXn/+858rPXv2bDFYxbWWzrlbb701DsDTzucInsgqSYP1JUnnDvUO8qUYPE5gzRr+PAuw2e937u887u049zT6lwTU3M/IrEl9AWojUecgP4fq/Qy1P00ZZKdOK2uFWLNHCfxU2IQ12FzsBEysIaKKeKmdWNajpbXUrEujmFCerkPqOMeI41ZSp6wWNwkGI6h4yY2BRpBtTFJ2BIE2Kc9/+tOfimvw8vfLLMfJJ58cjw8djbHNaJckv3bSejRqRDCbzzIVZmPzlDC21WNgpzS893S+MDtL3QNSCVnGUy/QTrMlrMNONQFKku/Jy3lFKi/HLA+0uSY5XgRPJaltdxhwYM/wdM7QsSeTpG/fvtWCZ+k6JfBu9kCoNRR6I+BJy3u4DslQ4j5Hvyr1H+gX1B7jUo9ZPrFDwa5dd9218vLLL8dgkvaKtr6U4NH9nX9a5ghr1YlLGHynv0k2DQN+oC+V1v2Pqdiu2p+mDLK5MZCKQpGFdNF37949jmgnBEcU8qIhSOlQJeGCTcEz1Z4ZkEidfNY3coMAa89KDpJYN8TaRmb2uZGyfQIphpNMMkl17SfnEp1bBnNKHYggK4KZIYJDOq0cMyryGmi3DLCp0k+RNwb6uAZJgSbFmRH8hMJBpGOefvrplVKP1W233RbT6ZnNpvosaZj5bg90XEkdp8NRUnCdX0e0zXS6CKITBk1rA21mRK644opiA6CESsb0A8hqSzPbtOsUQ+W52kAbzX7M8vea1tDSftN2M2jMMgyute222y6uM2ZGlmOXbx/U7Mcoacu9nUkcBrO4/rg20/rslHXTzMfK/Z3bpvZ+RRFY+kt5mjwZWfSnmLxJM9pkB7Jks7QBiI6uKYNsOhfMOHJh8zkjRHTSklQ5nM5sCdtO1bs5MMDA6Blphsyisa44vZbgkUAybwya+eYwJqTnkA5eexzZHojRRgZ00sBE6gCXFmiz/o70Qkbt0/tnJJsRfVIKDbT/30wjWyul5QZUCmUwkBtpWsNIOioBNset1GuOqrwMYpEtcvXVV8e16qTv0mnLA22OG+dYKZV683aFY8LAFsUX6agOGDCgRaBNFWg6r7XbUJZyTuW7XTBY8+9//zt+ziwRxZbI2EqBIjPatF8MeJVyLtW2x3yetgGiU892ngSI9ANS5s0FF1wQg+5mLpDXFhTLI623NQSLnEfU1mAGt4R1s+7v3Dbc/xlAznGekGmUUufT+UKVegbgKRRLMcv8uWY/Ts2kKYNsTk5GgVgjSkeD1J10cnJDpYBJs1YHHRP2tiTFEvybCk3kaancQBmtJkVTlbitBIMQVFhHOo9IcyLIri2yVGIgyY2SlLB8vSI3AW4mBEvMhJTUea2HSsbMllE9PC8IQ8Ve1swSbLNujS1x+Chh5qM1rOukY5Ej2KbDwWBpvuaT7bxKQ50RZj6YxSdtl2rOrNcjiyQhMOI8Yi/a0gb+KP5GdWuWglH0jTYoP2dYu18baNPRZWCilPY7f59kQjB7TV+JHVhSBkQKilJ7zuAfKeMlnUu1aLvJFEnXVT31jk8p5xXc37l1pIPX1oegtg/ZNHlGEsiwpW9A9g2DXknJ119H1OGD7HTC1Z543DAJIAmoc2yZw8xkSdtLJDvssEMcfEjp8RRT6NatW5y5prgZs2ns58jWE6WNlLV2E3z11VfjkgLWqaWRRpBOTxoU+zmWpF4DT5ol6XG1NwlGXwkeuQ6pjVAyqqgSJFIborY4F18TMDFrxL+ljOq3Zqeddqp2YvOCS3TeCI54vsTiSikdlZmhVJUXdFT5muuMgdSk1KU+HCOWPtFxZYA0nSt5MUH6BRQaJHU8H/RCSceM5StUEScoYu01x2z99devzpxxLC+99NI4UEqHv+TtgtJ7ZokB7VDtjGSp3N957GqvF9pplrOmrSdZykr2LdkiCc/RZ2cHjXpBuDqG0AwnLsVfSH9mtoigCFzIrBFhNJ/ZRrYKosIqjWMJFWjrNV6M8DNLTWohDR8zj8yK0PlnfSPFOagoXtosWm0VcVLBaNDSdjg0hgTaVA7lXCOwZjSb86uUmwRq32s6T7ixMmtEKiFrPxOWYrAnPenRpZxLY+qAkhXBDORee+1VXWbQmpKOVy3SxBn8S9lGabCBpRtUNWZGJAUBpWF2kQyaPD08Pc5MI51bCnzlSmqj0rVHhgj7WbMdV75XeB5QkzrO8cqLx5WEfhGDoGnLQLL8unTpEtd95m04a2ep35Kuw1IH//JBZfoCaWu8kq6vMXF/5/ry8yO9V4q8kWGT+kv0B8i4TUXzWL5Jf4oMAI4rA6upGKE6lg4dZIP1HBTGYQaWjhkdDbaYSNsIEDQSWLNl10orrVTcHqrM/jBjnTqldMCYqc6rPZIeRvoz6fUlr/mgijjVZrkR8C+V1U899dT4HI1hyo5gMIJR2HSTKOEmm79H1jhyk2DklQEJbhAU8iIdmseYabzwwgvj9ZYPRJQQOObHiZoPtenMdDoY0GI9LVVVS5Y6HCxRyVNTQbVZZv3zGWtmSAi0U4GqZtda55N2ip0Oavci5pxiEJX7IetGS1LbBlOVl0FSBt6Zgc2348rbIWaUSrjXcS7Vnk+cP7TXaXA53+GAWbS0tIzZ7JL2Va89nyhAST8gLRkDx4l+ZWrDmz1QrMf9nduGfiLHg/MnL+5Gm0TMwrp10Fdg9p9lY8z6k1GS6iLRJz3uuOOKPdc6sg4fZFO8K63/YObjN7/5TVy/QFXo/GZCh7e2I9fsSG+moAINHev26HCAETI6sa0pIWisbayY+SD4Yduy1PizLp3GLu1NyOvZSoigsqSbRI695RmA4F+CaWaKuBlQTJBZD4JuBnEYgSXFsNSBiP79+8dzh44YM2Z5e8QoNcUYOYbsvVuidO0xIEpWCOcRa7EpegaWr7B+nU4IHQwGbFiblm/lVcq5RDp4SisEaaoM8pFKmAaNeZ4qxqT2srSFeyD3uxI6ZHnHlawZip6lwS3aJAYFCbTJIEmYhWRmLSmtHQfvn4EslhpQUyP1D8DsPn0EBt6TEtrwevV9aMvZfYUJG2YZua7IiGB9OudUieeO+zu3DcVgaY/ZLpdrjD5Rfix22223aqCdD2TlbRqDp1ynpfYVOroOF2SnE5FAh5EhgmxmrBMCbUbzaRDz7QRKUK/RIvWSmQ1GwUj5osPKcWH/QjpkJaIyb6q6m44ZAzXMUJOakx5jBJ+1e6SK11PCTSLHYBXpuizFSLj2uHGwhpatzPLAIF2rpXVCKB7EOkeyH0jBpINPuld+vTFinSpol4qBB2bPWI/GWnSuMzIfWBuasFSDTgZLfUopVpm3K7RVDDCw7CkvOsV+z1SqpfNGm845Rho9mEFaYoklmn5LHLJpUtVw0BcgRZz6ECzLSP0CgiJmI8lmY4CZQR3uf6W0SwzEkNmwxRZbxMH2v//979UZMgZqCI7y7U0pzMTAKcUHS7vHMaOfskA4VhSESxMWBEf0ETiPGDjlGPGR0qRLGNCq5f7OrSObj3aG84h6K9zzaacJmLmnJeyawZJN+gv5YCrFK1mGRxufdkRSx9PhguzUEDLryA2VUSBGGnOkidHxp2NS2oWdZmXz983oK6NpFDyjsippPYyqkXKYp0CVgEqXbE3CgEPaCxyM4pOmQ6VHpA4qqfTcIBjRLh21DAge845tWrLB+URxk1qldTw4BtxI07HgWNFG8RipmXkAyXVaSvplLQZJCQpTFhIdezporNdbaqmlWhynUlGUig4ZASIDElxjLIdKbTbBE0Urad+ZMUqBE1/TvuWFvpoNSwYYXOCY0BllWQHZEKwvZhCQ+92kk05aHWhnwJRq4gTmZOCUUneEjjv3ewIfBmU4n7jO6AdwvtBXYtCB/hQZNiwnY5KCe2RJWUjg/bI1J/d7rjO2x6u3vJD053322ae6O0tebLCZub9z2zEgw+QWu2Lkg530ORmMoB9F4J0QTHMu5ceStokBsbzgrjqeDhNkp846VcEZTSQo4gTceOON49epkmHCTZdRyHztcbPjGBFI05kngKYzQcNGwE1157S+kUEKUgtLqu6YY0aRGQ3WEKfiZqytYjQxL5KTbiR0OEqZRRtT1X6OBTeHG2+8MX6d3zwY3a8tuFSC2kEECi+mPedJfSa1ng4Gaat0cEkhr529bvaOfjpOqa1htJ50XooLclzojLF9EB0NPqeTxgBpvpavNKRg0u5QKwMU7mTWn4Fl2vd8u7yEx5jxp2p2CWn19AWYnSXQZs1sPtjOoA0p4QTaBNf1NHunnz4RgSIzaul8IduIexxBNZkRtD2skaWoEtcdy+2YWSu5yBm1WAh4yAgBx4j2q7adpgAqmW7MZueZW83I/Z3bjsFzzp+0RCy953T+0EfgeHKPywPoP/3pT9XXNvO5VJoOE2Sni5c1DOyVmkbVWKdAAJmvnU2aPV0O9YJkGrZrr702VipkFJugkjTMvPgLN9t0IZcSaOcjsQzSsKSAKqFpRpvgkZsD5xPbJrA+mxHtkquI115DXHt09NPa9XQuMVObCniUeJxoh9Ken2RDcK6xPo3q/el1nG90bkkfK+Emmt53fg4xss/1RTphmun4/e9/HysYp+NHx5XBCZYglFLkrLZ9IdWZtcQpwGYLF4ImMmsIounYp5000kwtheGohVBbEK2ZjxWBNucOndrtttsuPpauLa5BrjXadNLrc81+/XHP55jkS+nSe2aQi3oQBEj55ETttVZCQFR7PnHO0E9igoLjl1LH80HC2j4pmUppMKxZub9z23CMOH/Iqsn3Ck/HLB0D1mqzVr3eeVPKdVeKDhNkU2gi3RhIJ8wRJLG2iJlZCjCUIm/0b7/99hg4kh7GyHS6oEkLo7PPseGmke/DV/szmlnewDPrQcoOs2c0dATaqagEnRJG9FkHwyAF6yFLSpvL3yMFcQh0yBZJ25VwLMgAoONKah2pclT2J/W3pJtDfpxYg83INMFQGsghVZd2is4JODacc8xQpu9t5k5Heo/MqDLDyNcUn+Sa+/Of/9zitSztyYtS8Tlp0KUsZcnPJbIeKPrGucFsP6mWtEHHHntsfJ5jSOYWbTmBQI7nqPJfinT9MFjDsZhiiimqy3rSc7RXFNQjc6kUDMSwLj1/z+kcS/+yjpjziLa79jXN3ja1hoCaY5ek1PHaSv21KeTUkmB7xmbk/s7jjgFkiuZyXuTZfVxf6XhynjEIkaeHqzl1mCAbjNxzUZMCVbsOhiCJzj/riUqrIk7xCYJC3jtrr1hbnO9XTLocF329jllpKCRExWcGJVg/REE4bhL5jDYzs6SHl7ylGbNirLfiXzqpnF8EjYxSpw4IgSWz/KxzLGWNYy1mqnv27BmXrhDk5LNCrHUkE4JjxfpRqq7XdnabUXpvdEZpcxj8u++++2L6JaP8BI6gw8FgBHupc5wY1GHmkVnsNMvdzKiEnXDdsHSFTmmeCk6gTbHBVHWdGWsyAWif8muthKCotiAcg1bpXKJd4jyibafYIPLCiyUcn4TzgjX6DM5wvaX+UG1mCYPN1EBgRrKZ26O2oB2i3aGNTgPuTOwwgEqBSrIkWdpCCjTnXcLjtHF5VkmzcH/nn45sUpY/1QbaqR9Jn5wlnbVbfKr5tNsgO13U3Dy5SaQZItbO0vHv06fPaCNkNHSpqmGzS8eHxo4U1DSCT0eV9KXayuq8nlm00oLFPCWeDhl7XZMRkSNljtlrOmn1tkkooQOSd0KpvM7xyEdZuZky88HNMx+xpsNWahVx2hvWorMGq975wiANab10dtmOqoSMiPTeOIfIdkgzsHRW6YzON9981VmQ9FpqRTBgw9IW1kM2e7ozGNyjQ5/qG6RBCYpS5VX6ue9xLXIe0aaz5IClUSXtPV97zbBFF2045xMDzCmNlb4CBd9Ip+c1te1aM193SX5e0LmnvaYIXEoFz9NWGXCnNkuJ6g26kAXCBAVrZdOAO4E22Uica9RIoL3Pl78wI5l2Kmk27u88YQJtlkUxqMzkRWnHpETtNsgGnVfWNjDTyCxtulHQ4SfQnn/++eNobEkIpvOUJo5Dmp2mw8ZIflqbzkhiKuyVKyUYyjtV6XOK4aW1ezlmhygsRCe2pGJ5YO1QCobSOcaMWjoO6UZAEMCATlrfWHp6IUE0s9ipoGB+DOicgQCADkcJAxF5ijhZEHS68g4sg6Isz0gFzfJiQgyk8pGq+zc7smhI1eUjDYgyaMNaxnRMUvDIjDdp9jy36qqrFjFY0xq2vmFmn9kxjkWXLl1ipz916gm0KehFUJSWTZWmtUA7z/AjU4RzL637L1W6xlL7TDvFQGAeaKc9xfPdIGjHm/n6c3/nxgfaZN+AgS0mLCxyVoZ2G2Rzg6RDz4lJ1T1GyLjgUzU+Au0jjzwyrtGuXePXrJjF52JlViwF2gTZjLSSTkiQmBpCbgDMcrNGPaXUlYT1QBTFSVvhcJzAcgM6rHRycyw/IH2H55v55lnr/PPPjx3SPPOBQIdrj62DcqTLMehVYtXnejdC1s6yripfj5fOHbaKu+aaa6qduNZ+RjOmiLOch5lpziG2ukno5KcZ7TQL0lpBoWaW3i8p31RzZraH5QYMbi299NJ1B2IIIgkgSxisae16oU3v0aNHrGoMri12yuAapHheGtgim43zrIRjNC6Bdr5Ug5k07nelZELUQ9+I1OY0sJdnT9JHoA2jH1p7Hjb7MXN/58YH2twHWVrHoDyDOKUurytRuwqy88aMUX2C6PQ4QTWNHhdummFjpJFUlGZcDzOmoChVxWbt3k033RQ7rWxVQtGchLV7dN5ScFkS3vtss80WOxZUw6ZjxqxjwkAFaV/sYcw6WjprrLVisKakiusMyDATxDlUiw4ZlUEJFBNSx2qLnJQgH53POxOcI2SRsFY9396EGyfXHp2QkpCiSztEoTOOAZ0zZrTzQJtspFRQKK8bUYradoUsCAJtZjc4X0h1psggnVQ6tAz6kXlDUb3WfkYz4v5Wu2yAdorgJ1WhTyiSx/nEYGqa0S5pMKKtgTadfIoK0q6TBVh6R5/2ip0yKOyZAu10zFJ1dgp6lpTZ5v7OEy7Qph1nuWI6riW3TSVpN0F2uinec889MXAm6KGQWRqdTjNHBNqsUePzkk7UfACCTgXVwllDzGwtRU4IlpjNJjWHWW7S6wmISjk+tUiVp9osH2kbk9S4ESyyTpbzKKVhUrijpPSd2r0cEwZlWGLAB50PziEGKpi9Jj2TNbOldMqYjc479GQ7sN6MQS7S6TifGKmnneI8Yp0oASZtFOdUadfeAw880GKfeQLq1gJtlidw/uWDOM0uD44pfJOWGaRAe84554yDyAxwcf9jfTHbU/F5aecSBZbyLRdBQTOWG3Dt5e00s40UreJ8Ouiggyolyu9Z9dah02azXIOZNFJVS+votzYwxUAO2ZBkB+a1EMiQIIDk+ivlfuf+zhNWXmCwlOtO7SjIBqP1zIQw4kqRCdaFpoJeeco0BXJ4vrSqofl7ZVsJKjfSEWOvPVJ7CCi5YRAYEYSXNlKdb43ETYD16aSsElCnir35zZY17Mxesya59oZSyl6OFMpLmE1j5D5VdmbAhuUaPMbNlUGdUs4paj3QcU9pzWeddVacZSSIZukKQTWdCwIBBvyYKWJ2iMEtZrfT+VTqzTS1VWSV1Au06XCwzIWU6dLabgZjCKYJejg+YFCLQJuZjnzWOtfs11y998h1R2V62h3aLYIe7m0M6CQMNNOeca0y2Fw7cNiMSJlPafP1+kCtBdrMUObrikuQ3/Npb0hnTtcdGChl8IF1xwzkcP/jXpivV2/2a8/9nX8+JWQiqR0G2XS62GaKVFQubNIr6HCQ9kujmCOAKjUVpTbQJvgh0Ga9LB01GsFHH3206BEz0sAT0sG5cbD+rHbf3drGrtlvpPX2ciSlnrVpzMYyeJWWXtR23PKMklLOqS222CIuKyCtmVTetI0SSOmlsFceKOXHqKTjNDZ5oJ1v51LSAGlCnQMGb7inpaUH6ThQpZhiVAzU5Et/SpSOCesX2WYqtekPPvhgnNknU4St4W655ZZ4vMguoWgVA12pwFCzoo9EFhbL6fJ90QcMGBAH/uop9V6XtzEsU2FwlPXGDL6TEZi2pKTvxDEl2KbPSeX/PF26BO7vLDVpkE2HgxlYOvlsM5VQpIO9ZmkULaAw5hltZhlZt17yiBnHhc4YKU8nnnhi9QZK9gOBNjPafM7sIxVo2bc3fV/JlS/pXJA1kgYhxrT/bgnHKg+WCbRZSkDnrHb0nkCbjBoCbY5lacdpXAPtv/3tb/HarN1CrxS0x7Q7ZEOkr2uvN1Kfl1hiiSL3lm3t+mGAlDYq7SvObCNp4WQqEWzTrqcBLVJZGdBpVmQ5kLHGbD3LnvJJClLst9lmm2IC6HHBAASBdepfbrDBBjGYpu1OBVJZGsRaYo5tabP9ifs7S00YZIPRaTpgpK/mI4gE2qyDpIohlWtVP9Cmo8ENNqVFl35DZbaIAh1ppJ/MB4IlBmwYzMkLv5SMDgbraFlikM8AlTZAUw8DMmBN+iSTTBIHbljrnzv44IPjAGGJRbzGFWuxmaHNtyAsCecOwWJejDK14wzspOwslh+Uev3l7zsPcOj0c+zy5WO0Xfm2VBSKY9Y7XbfNiHo1LCuotwaW/lFegyQpfcCPDBGqiKdlBATaLP0h+4GBGvoJaYlUrtTBCvd3lpowyAbrYJhRY7Q2D4DonJDOmu9bqJY3T7ZdoFHk39rnmlX+HmtviBSD40bKDZSU8bTuiACctValr5lty021hHMoR+op69JA2hydsHQMWAvap0+fuO6zdks81vWX2iEbV6WcU+l91r5flh2QEl470DBkyJA4SJrPEJUSaFPsNGUVjS3QJiOJtch5/4DAmy28SPVt9ow3BhKoW0Oldc6ZW2+9NW5tRmBNsS4mKzgWHBMKWen/0qCvuuqqmMHGcoNevXpVt6FkAoeBGbZ8ywuflc79naXG6MR/ws+IX9epU6cwbNiw8PLLL4fu3buH3r17h0UXXTQ+v+6664Ynn3wyXHrppWGNNdYIk0466c/553U46Xhi/fXXD126dAm33HJLKMnJJ58cpptuurDNNtvE8yn5wx/+EAYMGBBOOOGEsPXWW4dZZpmlxff9+OOPYZJJJpkIf3H78+GHH4YTTzwxDBkyJKy66qrx2JXk66+/Dpdffnk4+OCDwwILLBBefPHF8Oijj8bPky222CK2W/369Qt9+/YNk08+eYuf4fkkjBo1KnTu3Dl+/uWXX4auXbtW72NXXnllOProo8Omm24adtxxx3h+ffzxx2GXXXYJX3zxRRg8eHD1e0vw7bffhn333Tc88sgjYdtttw2HHHLIaMfw+++/rx6/FVZYIQwdOjS+fpFFFomPcfy456222mphnnnmCc1ut912i21Tr1694nH66quv4nGkH/DSSy/Fc27KKaeMfaoHHnigqPMpP29yI0aMCFNPPXXYeeed4/Pnnntu7Ctx3XGMFl988XDttddW+1Jq2Sd49dVXwzTTTBOee+65eC16r5PaqDIRsK6D/QlZe8a/jI5RaTYhJYX1M8wsOdM4dmlEkRFs1pDWbn3S7FuVMMtIVdkrr7xytOJTrFVn5Jp1kGkvTLU+er3ttttWdt1112JGqUl9S7OKXDfsb82yFc6pJD+nOJ/YxoyZEDIjpFw+C0uRM4pycU7tueeeLSpmc89j+Qp7rLN8hQr+aXa2lBnshFTd1gou1fucKvW1VY9LaK/yY0DWQ6rWz3unLaL9Yl02fSkKodYeo5KOD6nhl19+eayUTZpzOhbMXLNXeOoj0Z5TKLak82hcuL+zNH5+9iB72LBhcb3sOeecEwt3sEfoMcccE7c0YR/ahDXGdEJq10CqPlKiKUjx7LPPVpoZ6/BIqXzmmWdiGlhCJ5aql9xY84IwbG1GcSoK6HkDHTtS5vKt0Jr9miHAYZ1+qqrOMgNSB0k9pTOW5CniDAIymNXsx0fjJj8fWHfN4B73NAZkqKxOsaV0bZHyzKDgoYceGgvClb58pbUlK+l40e4TEJEenZS4RIP3zDHZcccdYxG9HPc91muzW0T++tKQUk9/kmPBOn4GsNJ5Q7FKamgwGEGRPHaISMeotMGttnJ/Z6kDBdnXXnttnAlK25eAYIntKGj08nXXVjAcN7WzuM2GTil7pM4888xxtpGKz/kMEesdCbRZN5sKmdAxe+KJJxypHkeldDjeeuutONtI5gwFp8DAHhWK6YzlgTbnDoOCKGUgQuPuxhtvjFu/pSJdVCymINXkk08eB0Jb66iWGBC1FmjnA+70DzhuDLrbyf8/7HTAfsUM0rDOmHXtDCSz9VTJx4htuRjcYhA+XXv0FfJda9jdgNlZspjS7Gzp115blNInkDp0kE1jN9NMM41WKZxAiE7I/fff/3P/SeoAqKBOAM3eqBR6oWOxww47VLp16xaDpISUec4vZigZzGGkOnU6vEmotUCbImcM3qQZbUbvzz///DijTfo8M/wUrNpss82q3+f5JLDcgJRTtqLEXXfdFatA4/bbb4/FPGm37rvvvli0auONN27qJT2NCrTZXom051VXXTUOWpQSEI2tXUkDe1TqZ7kdFbK535EpUcoxag1ZkeyWAYqdUfyUrMm0u0G9wdGSByUkdeAgOzVkQ4cOjXtbks704osvVuabb77KUUcdVd1eCdxMWafGiKyUo2IsVWXJgshxznADZXCG9N2E7ZSYCTn22GOrN9BSOx1qXd7RYtsf1s7WBtpkRRAkkTXBLJHbvilHoMNAXo8ePWI2RFonS9tEp37ZZZetVs5mRpatA5lZ22233SbyX96+A22OI4E2QRLHt8T1oC+88MJYX8OWnQzw0Gal9qykY5Sk84NBUPZQZ9JmyimnrAbYHBt2GmGLWEnq8EF2avBJnWM2iLWOzBiBho/R1379+sWUuuHDh8cUHjop77777oT6k9RBkfLFAAydr9piLuyTyoANnTFmuOsxwFZrs0R5h5R9imsDbTpvLD248847q+dRiZ1Yje68886LmTT8yxZKLC0gVTWtKf7Pf/5TmXPOOavbSqXCggQAtkljVmLBpbxtIlOLwRi26mrL68f0WDNiMoatOFl/TWGzhMyR2WefPR47BsASlgBxLpFeL0lNMZP9wAMPxGD6ggsuiMFQjmIwpPNOM800sTDVbLPN1vR7XOqnoTI4KeBJ7TpY0jWpLk7RM6k1eQeUwIjiQZtuummLDImUOs6A32uvvTbazzA4EtiDmI48VYyTESNGxLTdTTbZJH7NTDZF9Zhd417IAA7nVjoPPZfGrKSCS/k9jYrYFC/j/KJgHvtf6/+hUCATN2mJD9khaSCGgVLWWvMY/QEef/7552OAzW42zX4eSWpfJugGioMGDQq/+c1v4t6E7LGX9rzEHnvsEW677bb4ccYZZ4THHnss7lUo1WIv2c8//zzcfffd8evavSznnnvuuGco+8xKrUn7px5++OHh+OOPj/t9zjnnnHH/a9og9v6cffbZw0UXXRQWWmih8Itf/CK8//77LX6Ge4OKPYnvuuuu2O689dZb1cd79OgRFl544bhX7zfffBO/Puuss8Ljjz8e9zbm++644454HvIaz6Uxm3baaavHij2Nm1m6p7GH+oABA0LPnj3DmWeeGVZfffW4f/h11103sf/EduGvf/1r2HPPPcN5550X90Z/8MEH432fvdOZNJpjjjnCAQccEH7961+HvffeO8w222yxfWfvcPYW5zyinZekn8WEjODXW2+96n6zjNTmo7VUEc+3WpJaw4wi6eB9+/atLjnIZ4J4nhmk1tLFpXy9Pim8jz32WLVIFTNGfLB1VzqnqDTO3r3ONqoe1ldzfiy99NKVE088sVrUk/Ooth0inZVaJKXMyuqnoUbNQgstFAsuJux/zfp0in7ecMMNlZLVyx6hD0mNH3YZYdtXsgAoUsk1xjXHckXqAXntSWq6meyll146PPDAA+GVV15pMfs4fPjwcPHFF4eXX355Qv56NQlmjM4999yY9dCvX7/w1FNPxceZCfrqq6/CvvvuG2eNVllllYn9p6od++6778LIkSPDYYcdFtum22+/PWy22Wbhb3/7Wzy//vCHP4TTTz89/PDDD2GuueaKs9ucY858qNbMM88cMyLSebT11luHzTffPN7XVltttTj7mkw11VRh/vnnL2ZWVj+9fSJzJmX9ca507do1Zt0sssgiYffdd4+zt+m5UrNHXn/99erjXHdkuXH/5zpjFvuUU06JbTjX3CabbBJ+9atfee1Jmig6EWn/lG/k21LgTOOVUjFBA0djRprc/vvvH1N5//jHP8b0Szqsxx13XLjsssvCQw89FNN5pLHhnLrkkkvCXnvtFWacccaw6KKLxs7I22+/HW+yTzzxREz/5fwyDVN5G5W3VZwvnCN0Xtddd92w/fbbhwMPPDA8++yzYbnllouDNgTcdGilsfnggw/CwIEDYzrvsssuWw2CbIc0JrV9puS3v/1tGDFiRLj55ptjujyvo+3abrvtwrBhw8JLL70Ul9Zx/yvxWqMfyfsnBZz+46uvvhpuuummOCgKjhPLyp577rkwwwwzTOw/WVLhftJMdmr4P/zww//7IdnNgs4FAfZ///vfsMQSS4RDDz00rodZYYUVwjrrrBPWWGONcPbZZ8ebiAG22opzapdddok3WDoiX3/9dQyq119//fDkk0/GzwnE7dgqb6NAW0THlQCaNdd0yN57773YVtEmYbLJJou1I5iV5F+prTPaRx55ZPjd734XM7QIAkA79BPHr1VQgE0234svvlh9jraHGe2DDz443uN4Hfc1+lBpjTYZFMzslnZ+peyRpZZaKh6L++67L2a30Z7TtoN+Jm18aTP9ktqnLj/1BvH000+HjTbaKFx44YWx4U/P0bmgGMw888wTTj755DhLNO+884b7778/fg8N4vnnnx9ntaVxtdhii8VBmlppcEei85k6sSeddFIMnOmkMntNajjFqQjAX3jhhfDvf/87dmoJlHgsBd0pG0cam5lmmikcccQR4cQTTwz/+Mc/YmYNSw9qCzRKSG0Ty1aYbHjnnXdC375947KnDTfcME5eXHDBBeGXv/xlXAJFUS/6ViuttFKctf3Pf/4TunXrFkpEVuRRRx0VjyFt99VXXx0HJCaffPLYZt9www0xpZxsN0nqUOniKcB+5plnYmoc61/oWOTefffduE6NAPzPf/6zHVU1XJ7+K7WGSr1UoWVQhsG9HXfcMc540DljVoS1jscee2wcEJx66qnDI488EjMiPL/0UxAckbnVvXv3WAXZc0jJ/79dajXAJhhkVvZPf/pT3HGFiuKzzDJLHKxZeeWVYx/r8ssvjynSBIxMWDBIyPIWzismKmirSj3HuNZOOOGEuCSRuhoE2gxQvPbaa/HY0e+0HZfUYYLsPMBm7SJrrfMAm7VCFJq48sorY2EKRhtTA2djJ2lCqm1j6IRR9IZAm9npW2+9Na7Xo81iC5iE7Bo6uUsuuWQ1NdOBQf1Un3zySawVwbnkfU8g7ZvlKMm9994b1w2zfWBqi5idZjnUFFNMEQdqWFaXIzuif//+sS4Ja5HZ1rJ0tPG050OGDIlrs7nuWIudlo7ZjkvqUDPZNGSkWjJqyCxQ6kQwosgs0FVXXRWrPErSz4klKuyRmtBpZX0ehc7olG666aax6uwee+wR91VlhpviZlSkTSxWpQld2EplYY017RBZNJwTZPpRtIwaEaSLUzQvYW02rydY3GmnnWIKOWjDKMZIde2LLrooLpnS/wu0OY4ff/xx+Pvf/26ALaldaXMvgBsEDTyd0umnnz4+RoDNTYLO6z777DNagF1aYQ5JP7/nn38+poPTPiXUfFh++eXjrBABNltzEWCDomfMJD366KMtfo4BthrFAFv0mahHs80228SvCf4oynXnnXeG+eabLzz88MOxkGfSp0+f2IZRDI3nEr6HNPE77rjDALvOGm22WqQAmgG2pA49k80ejqwNonO6ww47xD1n+ZoU8bXWWmvC/qWSVAcz02TW0NmisBlp4VTfZUnLpZdeGr9m5hqsyWYNH7PWgwYNMhiS1HC1SwVol5iRJguQ+g8E0dtuu23cw5nHqJidZ+Ww8wqDfmZEtJ3HSlKH3yc7FZwYPHhwLDJBCtNqq63WYgSRYkK8juIvkjShkX5JoUXaHpatsI/qZ599FrdWYgsvZo6Y7aboGY+zjo+ZDztmkiZ0kL3bbruFJ554Imy55ZZxmQqB9r/+9a84WUGgfcghh8S6EDmXr0hSx9b5p26hwMz1ggsuGLeXQB5gM7vNTUWSJiQG9wiU6bRS5IzUyq222ioWCGJt4zXXXBMDbbbwYlCQFPKnnnqqmlpogC2pkQimU4DNcrpbbrklZtKsuOKK4frrr4/rqxkU5GsybWiP+vXr12K/bBhgS1JhM9m1M9rcUDbeeONYfIKv2R+UQkO1o7KS1AhU56XQIoN9OdLAWc9I5/Wcc84JF154YSw4VK/Ks7NEkhqNPa/JmKFS+JRTThm32iI1fKGFFoptDnths+0URc2oLM7gIO0ZWX8MCDroJ0nN4ycH2XmgzbZerIEcNmyYAbakCYZ2ho4qQfbWW28dB/dAp5UA+/bbb497YLPvLLNIF198cVz7KEkTGt2p+++/P2b6sV/6k08+GZeq0G5169YtBtr77bdfnJxgi0Ey/qaddtrq97t8RZKax3i15qSOH3nkkbGCJvuD0vE1wJY0odBRZUkKdSBIwzzzzDNjOjjbC/I16eKkgh9++OGxbaIqL9V8JWlCSxkzBNPff/99bJ9Su/Xdd9/F7BkeW3rppcPZZ58dq2IjzXUYYEtS8xivmeyEPQoZgZ1pppka81dJ0hh88MEH4cQTT4wz16xvJItm1llnbVGAkarjpGBSXMhtXSRNCPVmn9mJ5bnnnouz1dSIIG08R7eLIJuUcZetSFJzakiQLUk/t+HDh8dAm4rhVBNnK5zW1lu7f6qkCRlg0w6xrSmz1NNPP3187Oabb47LVfig4BkIrNdbb72w/vrrx6+tDyFJzckgW1KHVa8AI1zbKOnncuihh8a9sFNa+FlnnRV++9vfhimmmKIaaC+22GJxBvujjz4K//nPfxz0k6QmZy9UUoeV6kIwe/SPf/yjWnHcAFvShMLsc/LAAw+EwYMHx5oQbGlKVg3FGa+44oq4dSCDf+yJTe0athB84YUXYoCd/wxJUvNxJltSU8xoM5tERV+2w6ndskuSxtebb74Z5pxzzurXrKtmZpoiZyxdSfbff/8YZJNls/nmm4dpppmmxc9x+YokNT9beUlNMaN9xhlnxM4sAXa9vbEl6adiRnrxxRcPxxxzTPUxsmeYxWZ9ddqmC7RFtD/shMBs9q677hqmmmqq6vcZYEtS83MmW1JTcT22pEZj/2vSvbt27Rpnr3v27Bkf33nnncNVV10Vrrzyyhhs83yy4447xteyVZeDfpJUFoNsSZKkVuQVwNnn+uGHHw79+vWLxcyw6aabhn/+85/hkksuCWuvvXaYdNJJRxv0M7tGksridI8kSVIdBMcpwH7vvfdiYP3ggw+G8847LzzzzDPx8RtuuCGsssoqceb6rrvuihXGEwJsAm0DbEkqi0G2JElSjXz2+aCDDgorrLBCWHnllcP5558fBg0aFAufpUD7xhtvDKuttlrYcMMN45aCOZevSFJ5rL4hSZLUSoD99NNPh9dffz1WDMcGG2wQn997773j13vttVdYdNFFw3XXXRe3FFx22WUn6t8uSZr4XJMtSZJUBwXNmLmmIvitt94a11vzOQE41cX32WefsM4664RddtklLLXUUtXvc5suSSqbdwBJkqQQwssvvxw+/fTTuA6boHnEiBHhgw8+CCNHjgxfffVVmGGGGaoBNKnhBNt9+/YNc801V4sg2wBbksrmTLYkSSrepZdeGv74xz/GAmdTTjll+N3vfhdOP/30cPXVV8f9sZdYYom4B/bMM8/cYqb6oYceCsstt1y1QJokSQ61SpKkov31r38N++23X9yia5555gm33HJLDK5nn332cMABB4T//ve/4dprrw1HHHFEGDhwYOjVq1c10KYgWu1WX5KksjmTLUmSikVAvckmm4S///3vsagZSA+nkvgcc8wRn8df/vKXWNxsvvnmCwMGDAizzjrrRP7LJUntlftKSJKkIn377bdxb+u55547vPXWW9XHe/ToERZeeOG45pq12KDI2eabbx7Twy+55JKJ+FdLkto708UlSVKRunXrFtdb8+/ll18ePv/889CvX79wxx13xC277rnnnjD55JNXU8HZtotU8Y033nhi/+mSpHbMdHFJklS0Dz/8MJxwwglh6NChMUWc7bpID99+++3DqFGjQufOnav/Jq7BliS1xiBbkiQVj626KGrGuutll122uhbbYFqSNK5cky1JkorH1lxHHnlk3Lpr+PDhcTsvEGA7HyFJGhfOZEuSJGWp4yeeeGIYMmRIWHXVVcMf/vCHif0nSZI6GGeyJUmS/n8UNmM/bPbL/uijj5zFliSNM2eyJUmSanzyySdhmmmmicXO6CqxnZckSW1hkC1JktSK2qrikiSNjUG2JEmSJEkN4tCsJEmSJEkNYpAtSZIkSVKDGGRLkiRJktQgBtmSJEmSJDWIQbYkSZIkSQ1ikC1JkiRJUoMYZEuSpFZ16tQp3HLLLRP7z5AkqcMwyJYkqUntsMMOYaONNgrtxf333x+D9s8++2xi/ymSJE0wBtmSJKld+e677yb2nyBJ0k9mkC1JUgFWWWWVsO+++4ZDDz00TDfddKFXr16hf//+LV7zyiuvhJVWWil07949LLjggmHw4MFjnYl++umn42Nvvvlm/Pqtt94KG2ywQZh22mnDFFNMEX75y1+GQYMGxedXXXXV+Bqe43uYaU9/29577x3233//MMMMM4S11lor7LTTTmH99ddv8fu///770LNnz3DhhRdOsOMkSfr/2rt7UGrjMI7j1yOUOklJ8pbISwYWUpKNYjGQTEgZyMaiDFIoykQWwyHKhIWkSJGwKJSXhWKQt0GJifN0XU+ezs3geNw9bp3vZznnfjn3+TOdX9f/f/3xVZFffgIAAPgRJicnpaOjQ3Z2dmRra8tCbmlpqVRUVMjLy4vU1NRIYmKiXb+/v7fQ+1nt7e1WiV5fX7eQfXh4KD6fT9LS0mR2dlZqa2vl5OREYmNjJSYmxjG2trY22dzctOO7uzsL/JeXl5KUlGTnFhYW5PHxUerr6138rwAA4C5CNgAAYaKgoEB6enrsfXZ2toyOjsrq6qqF7JWVFTk+Ppbl5WVJTk62ewYGBqSqqupT33F+fm5BOj8/344zMzP/XtMKutJqdFxcnONzOp6hoSHHudzcXJmamrLqu/L7/VJXV2ehHQAAr2K6OAAAYRSyg2mF+Pr62t4fHR1Ztfk1YKuSkpJPf4dOSe/r67MKuQb6/f39kD5XWFj47lxLS4sFa3V1dSVLS0s2jRwAAC8jZAMAECaioqIcx7ouWqeJhyoi4s/PhkAg4Fgn/TYYn56eSkNDgxwcHEhRUZGMjIx8+GydWv5WY2OjPUuntk9PT0tGRoaUlZWFPF4AAL4DIRsAAEheXp5cXFzYGuhX29vbjnsSEhLsNfgebXz2llbEW1tbZW5uTjo7O2V8fNzOR0dH2+vz83NIY4qPj7ctyLSaPTExIc3Nzf/41wEA8P8QsgEAgJSXl0tOTo40NTXJ3t6ebGxsSHd3t+OerKwsC9DalVw7kS8uLsrw8LDjHm2Wpuu6z87OZHd3V9bW1izAq/T0dKueawOzm5sbeXh4+HBcWhnXpmg6nV3HBgCA1xGyAQCATQWfn5+Xp6cnKS4utnDb39//brr5zMyMNUjT9d2Dg4O2/jqYVqm1w7gG68rKSgvuY2Njdi0lJUV6e3ulq6vLupjrtl2hhH9dO67begWvFwcAwKt+BYIXVgEAAHiIVrs1nOuUcd1iDAAAr2MLLwAA4DnakO329tamo+t2X9XV1d89JAAAQkLIBgAAnqP7bWs38dTUVGt6FhnJTxYAwM/AdHEAAAAAAFxC4zMAAAAAAFxCyAYAAAAAwCWEbAAAAAAAXELIBgAAAADAJYRsAAAAAABcQsgGAAAAAMAlhGwAAAAAAFxCyAYAAAAAwCWEbAAAAAAAxB2/AdTzdHeg1fsXAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Industry diversification visualization\n", "industry_revenue = df_transactions.groupby(['year', 'industry'])['amount'].sum().reset_index()\n", "\n", "# Create stacked area chart\n", "fig = px.area(industry_revenue, \n", "              x='year', \n", "              y='amount', \n", "              color='industry',\n", "              title='Industry Diversification Over Time',\n", "              labels={'amount': 'Revenue (£)', 'year': 'Year'},\n", "              height=500)\n", "\n", "fig.update_layout(hovermode='x unified')\n", "fig.show()\n", "\n", "# Industry breakdown for 2023\n", "industry_2023 = year_2023.groupby('industry')['amount'].sum().sort_values(ascending=False)\n", "\n", "plt.figure(figsize=(10, 6))\n", "industry_2023.plot(kind='bar', color='coral')\n", "plt.title('2023 Revenue by Industry', fontsize=14, fontweight='bold')\n", "plt.xlabel('Industry')\n", "plt.ylabel('Revenue (£)')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 4: The Scale (2024) - Enterprise Success\n", "\n", "2024 demonstrated MCX3D's ability to scale existing relationships and attract new enterprise clients like UPOD Medical."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.024153Z", "iopub.status.busy": "2025-07-29T07:46:38.024068Z", "iopub.status.idle": "2025-07-29T07:46:38.028871Z", "shell.execute_reply": "2025-07-29T07:46:38.028651Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 The Scale Year - 2024 Performance:\n", "\n", "Total Revenue: £262,466.10\n", "YoY Growth: 288.1%\n", "\n", "Top Enterprise Clients:\n", "                                     Total Revenue  Transactions  \\\n", "customer                                                           \n", "<PERSON>                        £219,216.50            14   \n", "UPOD MEDICAL LTD                        £20,325.60             5   \n", "Hosny Homany Industrial Company SAL      £5,340.00             1   \n", "<PERSON> - Sin Dental                 £3,434.00             2   \n", "Packegha                                 £3,300.00             9   \n", "\n", "                                     Max Transaction               Industry  \n", "customer                                                                     \n", "Devin <PERSON>                           £31,980.00    E-commerce Platform  \n", "UPOD MEDICAL LTD                           £5,000.00        Medical Devices  \n", "Hosny Homany Industrial Company SAL        £5,340.00          Manufacturing  \n", "<PERSON> - Sin Dental                   £3,134.00  Healthcare Technology  \n", "Packegha                                     £600.00     Packaging & Design  \n", "\n", "🌟 Key Achievements:\n", "   <PERSON> Growth: 974.6% YoY\n", "   New Enterprise Client: UPOD Medical Ltd - £20,325.60\n", "   Average Transaction Size: £5,965.14 (vs £1,470.13 in 2023)\n"]}], "source": ["# 2024 Analysis - The Scale Year\n", "year_2024 = df_transactions[df_transactions['year'] == 2024]\n", "\n", "# Client performance\n", "clients_2024 = year_2024.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean', 'max'],\n", "    'industry': 'first'\n", "})\n", "\n", "clients_2024.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Max Transaction', 'Industry']\n", "clients_2024 = clients_2024.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"📈 The Scale Year - 2024 Performance:\\n\")\n", "print(f\"Total Revenue: £{year_2024['amount'].sum():,.2f}\")\n", "print(f\"YoY Growth: {((year_2024['amount'].sum() / year_2023['amount'].sum() - 1) * 100):.1f}%\")\n", "print(f\"\\nTop Enterprise Clients:\")\n", "print(clients_2024.head()[['Total Revenue', 'Transactions', 'Max Transaction', 'Industry']])\n", "\n", "# Highlight key achievements\n", "devin_growth = clients_2024.loc['<PERSON>', 'Total Revenue'] / clients_2023.loc['<PERSON>', 'Total Revenue']\n", "print(f\"\\n🌟 Key Achievements:\")\n", "print(f\"   <PERSON>: {(devin_growth - 1) * 100:.1f}% YoY\")\n", "print(f\"   New Enterprise Client: UPOD Medical Ltd - £{clients_2024.loc['UPOD MEDICAL LTD', 'Total Revenue']:,.2f}\")\n", "print(f\"   Average Transaction Size: £{year_2024['amount'].mean():,.2f} (vs £{year_2023['amount'].mean():,.2f} in 2023)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.029967Z", "iopub.status.busy": "2025-07-29T07:46:38.029889Z", "iopub.status.idle": "2025-07-29T07:46:38.041689Z", "shell.execute_reply": "2025-07-29T07:46:38.041480Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "lightblue", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "KVyPwvXoF0A=", "dtype": "f8"}}, "mode": "markers", "name": "2020", "text": ["L’atelier Nawbar"], "type": "scatter", "x": [2020], "y": {"bdata": "AAAAAMBQt0A=", "dtype": "f8"}}, {"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "blue", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "uB6F61G47j8K16NwPQoHQDMzMzMzM9M/gc8PI4RH4z8=", "dtype": "f8"}}, "mode": "markers", "name": "2021", "text": ["KITU KALI LIMITED", "L'atelier Nawbar", "L’atelier Nawbar", "Packegha"], "type": "scatter", "x": [2021, 2021, 2021, 2021], "y": {"bdata": "AAAAAAAAjkAAAAAAAIiPQAAAAAAAwHJApHA9CtfTgkA=", "dtype": "f8"}}, {"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "green", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "2/l+arx02z+amZmZmZnpP5qZmZmZmck/uB6F61G4zj8K16NwPQrnPzMzMzMzM+M/CtejcD0K5z+1vkhoy7nhPw==", "dtype": "f8"}}, "mode": "markers", "name": "2022", "text": ["ESC S.A.L", "High Tech XL Group B.V", "INCORP", "KITU KALI LIMITED", "L'atelier Nawbar", "Lines and Arts s.a.r.l", "L’atelier Nawbar", "Packegha"], "type": "scatter", "x": [2022, 2022, 2022, 2022, 2022, 2022, 2022, 2022], "y": {"bdata": "AAAAAADQekAAAAAAAACJQAAAAAAAAGlAAAAAAADAbEAAAAAAAGCDQAAAAAAAAHlAAAAAAAB0g0AehetRuKd3QA==", "dtype": "f8"}}, {"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "orange", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "AAAAAAAAFkAAAAAAAAD0P0nXTL7ZZgZAAAAAAAAAJEAAAAAAAADQP83MzMzMzBxAZmZmZmZm1j8AAAAAAAAIQDMzMzMzM9M/MzMzMzMz4z8zMzMzMzPTPwAAAAAAANA/ke18PzVeDkA=", "dtype": "f8"}}, "mode": "markers", "name": "2023", "text": ["B<PERSON>", "<PERSON><PERSON>", "<PERSON> - Sin Dental", "<PERSON>", "E-MOOD SAL", "ESC S.A.L", "Face Junkie Ltd", "L'atelier Nawbar", "Lines and Arts s.a.r.l", "Lumi SAL", "Packegha", "The Web Addicts", "W. <PERSON> & Sons."], "type": "scatter", "x": [2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023, 2023], "y": {"bdata": "AAAAAACIs0AAAAAAAIiTQCz5xZJffaJAAAAAAADss0AAAAAAAEBvQAAAAAAAILxAAAAAAADgdUDFTuzETqyJQFVVVVVVtXFAAAAAAAAwgUAAAAAAAMByQAAAAAAAQG9AAAAAAACorUA=", "dtype": "f8"}}, {"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "red", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "AAAAAAAA9D/fT42XbhIJQHsUrkfh+j9AXI/C9ShcFUAzMzMzMzP7PzMzMzMzM+M/MzMzMzMz4z8zMzMzMzPTPzMzMzMzM+M/AAAAAAAACEAAAAAAAAAUQA==", "dtype": "f8"}}, "mode": "markers", "name": "2024", "text": ["<PERSON><PERSON>", "<PERSON> - Sin Dental", "<PERSON>", "Hosny Homany Industrial Company SAL", "<PERSON>", "L'atelier Nawbar", "L’atelier Nawbar", "<PERSON><PERSON>", "Packegha", "TOMO BOTTLE LLC", "UPOD MEDICAL LTD"], "type": "scatter", "x": [2024, 2024, 2024, 2024, 2024, 2024, 2024, 2024, 2024, 2024, 2024], "y": {"bdata": "AAAAAAA4iEAAAAAAANSaQCVJkiQplc5AAAAAAADctEAAAAAAAACJQAAAAAAAwIJAAAAAAADAgkAAAAAAAMByQKuqqqqq6nZAAAAAAABwp0AK16NwPcKvQA==", "dtype": "f8"}}, {"hovertemplate": "<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>", "marker": {"color": "purple", "line": {"color": "DarkSlateGray", "width": 1}, "opacity": 0.6, "size": {"bdata": "AAAAAAAAFEBcj8L1KFwVQJg0RuuoqgBA", "dtype": "f8"}}, "mode": "markers", "name": "2025", "text": ["<PERSON>", "Hosny Homany Industrial Company SAL", "UPOD MEDICAL LTD"], "type": "scatter", "x": [2025, 2025, 2025], "y": {"bdata": "UrgehWtIp0AAAAAAAK2rQFyPwvWoRqBA", "dtype": "f8"}}], "layout": {"height": 600, "showlegend": false, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Contract Size Evolution: Average vs Maximum Deals"}, "xaxis": {"title": {"text": "Year"}}, "yaxis": {"title": {"text": "Average Contract Size (£)"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Contract size evolution - showing growth in deal sizes\n", "contract_evolution = df_transactions.groupby(['year', 'customer'])['amount'].agg(['mean', 'max']).reset_index()\n", "\n", "# Top clients by average contract size in 2024\n", "top_contracts_2024 = contract_evolution[contract_evolution['year'] == 2024].nlargest(10, 'mean')\n", "\n", "# Create bubble chart showing contract evolution\n", "fig = go.Figure()\n", "\n", "# Add traces for each year\n", "colors = ['lightblue', 'blue', 'green', 'orange', 'red', 'purple']\n", "years = sorted(df_transactions['year'].unique())\n", "\n", "for i, year in enumerate(years):\n", "    year_data = contract_evolution[contract_evolution['year'] == year]\n", "    \n", "    fig.add_trace(go.<PERSON>(\n", "        x=[year] * len(year_data),\n", "        y=year_data['mean'],\n", "        mode='markers',\n", "        marker=dict(\n", "            size=year_data['max'] / 1000,  # Scale down for visibility\n", "            color=colors[i],\n", "            opacity=0.6,\n", "            line=dict(width=1, color='DarkSlateGray')\n", "        ),\n", "        text=year_data['customer'],\n", "        name=str(year),\n", "        hovertemplate='<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>'\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Contract Size Evolution: Average vs Maximum Deals',\n", "    xaxis_title='Year',\n", "    yaxis_title='Average Contract Size (£)',\n", "    height=600,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## All 22 Clients: Complete Portfolio Analysis\n", "\n", "Let's examine the complete client portfolio, showing how each of the 22 clients contributed to MCX3D's growth story."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.042766Z", "iopub.status.busy": "2025-07-29T07:46:38.042701Z", "iopub.status.idle": "2025-07-29T07:46:38.059261Z", "shell.execute_reply": "2025-07-29T07:46:38.058916Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏆 Top 10 Clients by Total Revenue:\n", "\n", "                             Client  Total Revenue  Revenue Share %              Industry  Transactions\n", "                    <PERSON>    £260,477.97           £65.63   E-commerce Platform            25\n", "                   L'atelier Nawbar     £25,703.00            £6.48      Jewelry & Luxury            33\n", "                   UPOD MEDICAL LTD     £22,408.93            £5.65       Medical Devices             6\n", "                   L’atelier Nawbar     £17,817.50            £4.49                 Other            12\n", "Hosny Homany Industrial Company SAL     £12,425.00            £3.13         Manufacturing             3\n", "           <PERSON> - Sin Dental     £10,534.06            £2.65 Healthcare Technology             5\n", "                           Buff BBQ     £10,000.00            £2.52     Outdoor Equipment             2\n", "                           Packegha      £8,716.41            £2.20    Packaging & Design            25\n", "                          ESC S.A.L      £7,629.00            £1.92       Food & Beverage             2\n", "                W. <PERSON>un & Sons.      £3,796.00            £0.96    Traditional Retail             1\n", "\n", "📊 Revenue Concentration:\n", "   Top 5 clients: £338,832.40 (85.4% of total)\n", "   Devin <PERSON> alone: 65.6% of total revenue\n"]}], "source": ["# Complete client portfolio analysis\n", "all_clients = []\n", "\n", "for client_name, client_info in customer_data['customers'].items():\n", "    client_dict = {\n", "        'Client': client_name,\n", "        'Total Revenue': client_info['total_revenue'],\n", "        'Transactions': client_info['transaction_count'],\n", "        'Avg Transaction': client_info['average_transaction'],\n", "        'First Date': pd.to_datetime(client_info['first_transaction']),\n", "        'Last Date': pd.to_datetime(client_info['last_transaction']),\n", "        'Relationship Length (days)': (pd.to_datetime(client_info['last_transaction']) - \n", "                                     pd.to_datetime(client_info['first_transaction'])).days,\n", "        'Industry': client_industries.get(client_name, 'Other'),\n", "        'Revenue Share %': (client_info['total_revenue'] / 396907.87 * 100)\n", "    }\n", "    all_clients.append(client_dict)\n", "\n", "df_all_clients = pd.DataFrame(all_clients)\n", "df_all_clients = df_all_clients.sort_values('Total Revenue', ascending=False)\n", "\n", "# Display top 10 clients\n", "print(\"🏆 Top 10 Clients by Total Revenue:\\n\")\n", "print(df_all_clients.head(10)[['Client', 'Total Revenue', 'Revenue Share %', 'Industry', 'Transactions']].to_string(index=False))\n", "\n", "# Revenue concentration analysis\n", "top5_revenue = df_all_clients.head(5)['Total Revenue'].sum()\n", "print(f\"\\n📊 Revenue Concentration:\")\n", "print(f\"   Top 5 clients: £{top5_revenue:,.2f} ({(top5_revenue/396907.87*100):.1f}% of total)\")\n", "print(f\"   <PERSON> alone: {df_all_clients.iloc[0]['Revenue Share %']:.1f}% of total revenue\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.060622Z", "iopub.status.busy": "2025-07-29T07:46:38.060510Z", "iopub.status.idle": "2025-07-29T07:46:38.077456Z", "shell.execute_reply": "2025-07-29T07:46:38.077226Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": "lightblue"}, "name": "Revenue", "type": "bar", "x": ["<PERSON>", "L'atelier Nawbar", "UPOD MEDICAL LTD", "L’atelier Nawbar", "Hosny Homany Industrial Company SAL", "<PERSON> - Sin Dental", "B<PERSON>", "Packegha", "ESC S.A.L", "W. <PERSON> & Sons.", "TOMO BOTTLE LLC", "KITU KALI LIMITED", "<PERSON><PERSON>", "<PERSON>", "Lumi SAL", "Lines and Arts s.a.r.l", "High Tech XL Group B.V", "Face Junkie Ltd", "<PERSON><PERSON>", "The Web Addicts", "E-MOOD SAL", "INCORP"], "xaxis": "x", "y": {"bdata": "KVyPwu/LD0EAAAAAwBnZQFK4HoU74tVAAAAAAGBm0UAAAAAAgETIQOF6FK4Hk8RAAAAAAACIw0CuR+F6NAbBQAAAAAAAzb1AAAAAAACorUAAAAAAAHCnQAAAAAAA4KVAAAAAAADgpUAAAAAAAMCiQAAAAAAAMKFAAAAAAAAEoEAAAAAAAACJQAAAAAAA4HVAAAAAAADAckAAAAAAAEBvQAAAAAAAQG9AAAAAAAAAaUA=", "dtype": "f8"}, "yaxis": "y"}, {"domain": {"x": [0.55, 1.0], "y": [0.625, 1.0]}, "labels": ["E-commerce Platform", "Jewelry & Luxury", "Medical Devices", "Other", "Manufacturing", "Healthcare Technology", "Outdoor Equipment", "Packaging & Design", "Food & Beverage", "Traditional Retail", "Consumer Goods", "Fashion & Apparel", "Fashion & Accessories", "Lighting & Interior", "Printing & Design Services", "Technology", "Beauty & Cosmetics", "Individual/Consultant", "Digital Services", "Digital Marketing", "Business Services"], "name": "Industries", "type": "pie", "values": {"bdata": "KVyPwu/LD0EAAAAAwBnZQFK4HoU74tVAAAAAAGAi1EAAAAAAgETIQOF6FK4Hk8RAAAAAAACIw0CuR+F6NAbBQAAAAAAAzb1AAAAAAACorUAAAAAAAHCnQAAAAAAA4KVAAAAAAADAokAAAAAAADChQAAAAAAABKBAAAAAAAAAiUAAAAAAAOB1QAAAAAAAwHJAAAAAAABAb0AAAAAAAEBvQAAAAAAAAGlA", "dtype": "f8"}}, {"marker": {"color": {"bdata": "YqHWNI9ZxEAffPDBB1eIQBhLfrGkLa1Aq6qqqiozl0Crqqqqqi2wQLTIdr6fdaBAAAAAAACIs0CDUUmdgMp1QAAAAAAAza1AAAAAAACorUAAAAAAAHCnQBzHcRzHcXNAq6qqqqoqjUAAAAAAAACJQAAAAAAAMIFAq6qqqqpadUAAAAAAAACJQAAAAAAA4HVAAAAAAADAckAAAAAAAEBvQAAAAAAAQG9AAAAAAAAAaUA=", "dtype": "f8"}, "colorscale": [[0.0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1.0, "#fde725"]], "showscale": true, "size": {"bdata": "MkIMGAYKBDIEAgISBgYIDAICAgICAg==", "dtype": "i1"}}, "mode": "markers+text", "name": "Clients", "text": ["<PERSON>", "L'atelier Nawbar", "UPOD MEDICAL LTD", "L’atelier Nawbar", "Hosny Homany Industrial Company SAL", "<PERSON> - Sin Dental", "B<PERSON>", "Packegha", "ESC S.A.L", "W. <PERSON> & Sons.", "TOMO BOTTLE LLC", "KITU KALI LIMITED", "<PERSON><PERSON>", "<PERSON>", "Lumi SAL", "Lines and Arts s.a.r.l", "High Tech XL Group B.V", "Face Junkie Ltd", "<PERSON><PERSON>", "The Web Addicts", "E-MOOD SAL", "INCORP"], "textposition": "top center", "type": "scatter", "x": {"bdata": "5gJDBJ4AewRJAY8BcQBOBNAAAAAAAFcBiQA7AFgAiAAAAAAAAAAAAAAAAAA=", "dtype": "i2"}, "xaxis": "x2", "y": {"bdata": "KVyPwu/LD0EAAAAAwBnZQFK4HoU74tVAAAAAAGBm0UAAAAAAgETIQOF6FK4Hk8RAAAAAAACIw0CuR+F6NAbBQAAAAAAAzb1AAAAAAACorUAAAAAAAHCnQAAAAAAA4KVAAAAAAADgpUAAAAAAAMCiQAAAAAAAMKFAAAAAAAAEoEAAAAAAAACJQAAAAAAA4HVAAAAAAADAckAAAAAAAEBvQAAAAAAAQG9AAAAAAAAAaUA=", "dtype": "f8"}, "yaxis": "y2"}, {"marker": {"color": "coral"}, "name": "Transactions", "type": "bar", "x": ["L'atelier Nawbar", "<PERSON>", "Packegha", "L’atelier Nawbar", "KITU KALI LIMITED", "UPOD MEDICAL LTD", "Lines and Arts s.a.r.l", "<PERSON> - Sin Dental", "Lumi SAL", "Hosny Homany Industrial Company SAL"], "xaxis": "x3", "y": {"bdata": "IRkZDAkGBgUEAw==", "dtype": "i1"}, "yaxis": "y3"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "Client Revenue Ranking", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Industry Distribution", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 1.0, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Client Lifetime Value vs Relationship Length", "x": 0.225, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Transaction Frequency", "x": 0.775, "xanchor": "center", "xref": "paper", "y": 0.375, "yanchor": "bottom", "yref": "paper"}], "height": 1000, "showlegend": false, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "MCX3D Complete Client Portfolio Analysis"}, "xaxis": {"anchor": "y", "domain": [0.0, 0.45], "tickangle": -45}, "xaxis2": {"anchor": "y2", "domain": [0.0, 0.45], "title": {"text": "Relationship Length (days)"}}, "xaxis3": {"anchor": "y3", "domain": [0.55, 1.0], "tickangle": -45}, "yaxis": {"anchor": "x", "domain": [0.625, 1.0]}, "yaxis2": {"anchor": "x2", "domain": [0.0, 0.375], "title": {"text": "Total Revenue (£)"}}, "yaxis3": {"anchor": "x3", "domain": [0.0, 0.375]}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive client visualization\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Client Revenue Ranking', 'Industry Distribution',\n", "                   'Client Lifetime Value vs Relationship Length', 'Transaction Frequency'),\n", "    specs=[[{'type': 'bar'}, {'type': 'pie'}],\n", "           [{'type': 'scatter'}, {'type': 'bar'}]]\n", ")\n", "\n", "# 1. Revenue Ranking (all 22 clients)\n", "fig.add_trace(\n", "    go.Bar(x=df_all_clients['Client'], \n", "           y=df_all_clients['Total Revenue'],\n", "           name='Revenue',\n", "           marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "\n", "# 2. Industry Distribution\n", "industry_totals = df_all_clients.groupby('Industry')['Total Revenue'].sum().sort_values(ascending=False)\n", "fig.add_trace(\n", "    go.Pie(labels=industry_totals.index, \n", "           values=industry_totals.values,\n", "           name='Industries'),\n", "    row=1, col=2\n", ")\n", "\n", "# 3. CLV vs Relationship Length\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>er(x=df_all_clients['Relationship Length (days)'],\n", "               y=df_all_clients['Total Revenue'],\n", "               mode='markers+text',\n", "               marker=dict(size=df_all_clients['Transactions']*2,\n", "                          color=df_all_clients['Avg Transaction'],\n", "                          colorscale='Viridis',\n", "                          showscale=True),\n", "               text=df_all_clients['Client'],\n", "               textposition='top center',\n", "               name='Clients'),\n", "    row=2, col=1\n", ")\n", "\n", "# 4. Transaction Frequency\n", "fig.add_trace(\n", "    go.Bar(x=df_all_clients.nlargest(10, 'Transactions')['Client'],\n", "           y=df_all_clients.nlargest(10, 'Transactions')['Transactions'],\n", "           name='Transactions',\n", "           marker_color='coral'),\n", "    row=2, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_xaxes(tickangle=-45, row=1, col=1)\n", "fig.update_xaxes(tickangle=-45, row=2, col=2)\n", "fig.update_xaxes(title_text='Relationship Length (days)', row=2, col=1)\n", "fig.update_yaxes(title_text='Total Revenue (£)', row=2, col=1)\n", "\n", "fig.update_layout(height=1000, showlegend=False, \n", "                  title_text=\"MCX3D Complete Client Portfolio Analysis\")\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Payment Patterns & SaaS Evolution\n", "\n", "Let's analyze how payment patterns evolved, showing the transition from project-based to recurring revenue."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.078657Z", "iopub.status.busy": "2025-07-29T07:46:38.078589Z", "iopub.status.idle": "2025-07-29T07:46:38.225011Z", "shell.execute_reply": "2025-07-29T07:46:38.224762Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Recurring Revenue Indicators:\n", "   Customers with 3+ payment months: 11\n", "   Most consistent payers:\n", "     - L'atelier Nawbar: 30 months, £25,703.00 total\n", "     - Packegha: 25 months, £8,716.41 total\n", "     - <PERSON>: 20 months, £260,477.97 total\n", "     - L’atelier Nawbar: 10 months, £17,817.50 total\n", "     - KITU KALI LIMITED: 8 months, £2,800.00 total\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1400x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Monthly payment frequency analysis\n", "monthly_payments = df_transactions.groupby(['month', 'customer']).agg({\n", "    'amount': 'sum',\n", "    'id': 'count'  # Use 'id' instead of 'transaction_id'\n", "}).reset_index()\n", "\n", "monthly_payments.columns = ['month', 'customer', 'amount', 'payment_count']\n", "\n", "# Identify recurring customers (multiple payments)\n", "customer_payment_months = df_transactions.groupby('customer')['month'].nunique()\n", "recurring_customers = customer_payment_months[customer_payment_months >= 3].index.tolist()\n", "\n", "print(f\"🔄 Recurring Revenue Indicators:\")\n", "print(f\"   Customers with 3+ payment months: {len(recurring_customers)}\")\n", "print(f\"   Most consistent payers:\")\n", "for customer in customer_payment_months.nlargest(5).index:\n", "    months = customer_payment_months[customer]\n", "    total = df_all_clients[df_all_clients['Client'] == customer]['Total Revenue'].values[0]\n", "    print(f\"     - {customer}: {months} months, £{total:,.2f} total\")\n", "\n", "# Create payment heatmap for top customers\n", "top_customers = df_all_clients.head(10)['Client'].tolist()\n", "pivot_payments = df_transactions[df_transactions['customer'].isin(top_customers)].pivot_table(\n", "    values='amount',\n", "    index='customer',\n", "    columns=df_transactions['date'].dt.to_period('Q'),\n", "    aggfunc='sum',\n", "    fill_value=0\n", ")\n", "\n", "# Plot heatmap\n", "plt.figure(figsize=(14, 8))\n", "sns.heatmap(pivot_payments, cmap='YlOrRd', fmt='.0f', annot=True, cbar_kws={'label': 'Revenue (£)'})\n", "plt.title('Quarterly Payment Patterns - Top 10 Clients', fontsize=16, fontweight='bold')\n", "plt.xlabel('Quarter')\n", "plt.ylabel('Client')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 5: The Future (2025+) - Path to £1M ARR\n", "\n", "Based on current trajectory and client patterns, let's project MCX3D's path forward."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.226238Z", "iopub.status.busy": "2025-07-29T07:46:38.226148Z", "iopub.status.idle": "2025-07-29T07:46:38.230505Z", "shell.execute_reply": "2025-07-29T07:46:38.230292Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 2025 Performance (Year to Date):\n", "\n", "Revenue YTD: £30,029.80\n", "Active Clients: 3\n", "Months Covered: 5\n", "\n", "Active Clients in 2025:\n", "   - <PERSON>: £20,861.47\n", "   - Hosny Homany Industrial Company SAL: £7,085.00\n", "   - UPOD MEDICAL LTD: £2,083.33\n", "\n", "📈 Projections:\n", "   2025 Full Year (projected): £72,071.52\n", "   Historical Median Growth: 78.4%\n", "   Path to £1M ARR: Need 13.9x current run rate\n"]}], "source": ["# 2025 Performance (YTD)\n", "year_2025 = df_transactions[df_transactions['year'] == 2025]\n", "\n", "print(\"🚀 2025 Performance (Year to Date):\\n\")\n", "print(f\"Revenue YTD: £{year_2025['amount'].sum():,.2f}\")\n", "print(f\"Active Clients: {year_2025['customer'].nunique()}\")\n", "print(f\"Months Covered: {year_2025['date'].dt.month.nunique()}\")\n", "\n", "# Continuing clients\n", "clients_2025 = year_2025.groupby('customer')['amount'].sum().sort_values(ascending=False)\n", "print(f\"\\nActive Clients in 2025:\")\n", "for client, revenue in clients_2025.items():\n", "    print(f\"   - {client}: £{revenue:,.2f}\")\n", "\n", "# Growth projection\n", "# Calculate historical growth rates\n", "yearly_revenue = df_transactions.groupby('year')['amount'].sum()\n", "growth_rates = yearly_revenue.pct_change().dropna()\n", "\n", "# Project forward (conservative estimate using median growth)\n", "median_growth = growth_rates.median()\n", "projected_2025_full = year_2025['amount'].sum() * (12 / year_2025['date'].dt.month.nunique())\n", "\n", "print(f\"\\n📈 Projections:\")\n", "print(f\"   2025 Full Year (projected): £{projected_2025_full:,.2f}\")\n", "print(f\"   Historical Median Growth: {median_growth*100:.1f}%\")\n", "print(f\"   Path to £1M ARR: Need {1000000/projected_2025_full:.1f}x current run rate\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.231637Z", "iopub.status.busy": "2025-07-29T07:46:38.231572Z", "iopub.status.idle": "2025-07-29T07:46:38.239303Z", "shell.execute_reply": "2025-07-29T07:46:38.239105Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"line": {"color": "blue", "width": 3}, "marker": {"size": 10}, "mode": "lines+markers", "name": "Actual Revenue", "type": "scatter", "x": [2020, 2021, 2022, 2023, 2024, 2025], "y": [11937.5, 8925.48, 15922.93, 67626.06, 262466.1, 72071.52]}, {"line": {"color": "green", "dash": "dash", "width": 2}, "marker": {"size": 8}, "mode": "lines+markers", "name": "Conservative (50% growth)", "type": "scatter", "x": [2025, 2026, 2027], "y": [72071.52, 108107.28, 162160.91999999998]}, {"line": {"color": "orange", "dash": "dot", "width": 2}, "marker": {"size": 8}, "mode": "lines+markers", "name": "Optimistic (100% growth)", "type": "scatter", "x": [2025, 2026, 2027], "y": [72071.52, 144143.04, 288286.08]}], "layout": {"annotations": [{"showarrow": false, "text": "£1M ARR Target", "x": 1, "xanchor": "right", "xref": "x domain", "y": 1000000, "yanchor": "bottom", "yref": "y"}], "height": 500, "hovermode": "x unified", "shapes": [{"line": {"color": "red", "dash": "dash"}, "type": "line", "x0": 0, "x1": 1, "xref": "x domain", "y0": 1000000, "y1": 1000000, "yref": "y"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "MCX3D Revenue Growth Trajectory: Path to £1M ARR"}, "xaxis": {"title": {"text": "Year"}}, "yaxis": {"title": {"text": "Annual Revenue (£)"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create growth trajectory visualization\n", "fig = go.Figure()\n", "\n", "# Historical data\n", "years = list(range(2020, 2025))\n", "revenues = [yearly_revenue[year] for year in years]\n", "\n", "# Add 2025 projection\n", "years.append(2025)\n", "revenues.append(projected_2025_full)\n", "\n", "# Project to 2027 (conservative and optimistic)\n", "future_years = [2026, 2027]\n", "conservative_growth = 0.5  # 50% YoY\n", "optimistic_growth = 1.0    # 100% YoY\n", "\n", "conservative_projection = [projected_2025_full]\n", "optimistic_projection = [projected_2025_full]\n", "\n", "for i in range(len(future_years)):\n", "    conservative_projection.append(conservative_projection[-1] * (1 + conservative_growth))\n", "    optimistic_projection.append(optimistic_projection[-1] * (1 + optimistic_growth))\n", "\n", "# Plot historical\n", "fig.add_trace(go.<PERSON>(\n", "    x=years,\n", "    y=revenues,\n", "    mode='lines+markers',\n", "    name='Actual Revenue',\n", "    line=dict(color='blue', width=3),\n", "    marker=dict(size=10)\n", "))\n", "\n", "# Plot projections\n", "fig.add_trace(go.<PERSON>(\n", "    x=[2025] + future_years,\n", "    y=conservative_projection,\n", "    mode='lines+markers',\n", "    name='Conservative (50% growth)',\n", "    line=dict(color='green', width=2, dash='dash'),\n", "    marker=dict(size=8)\n", "))\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x=[2025] + future_years,\n", "    y=optimistic_projection,\n", "    mode='lines+markers',\n", "    name='Optimistic (100% growth)',\n", "    line=dict(color='orange', width=2, dash='dot'),\n", "    marker=dict(size=8)\n", "))\n", "\n", "# Add £1M ARR line\n", "fig.add_hline(y=1000000, line_dash=\"dash\", line_color=\"red\", \n", "              annotation_text=\"£1M ARR Target\")\n", "\n", "fig.update_layout(\n", "    title='MCX3D Revenue Growth Trajectory: Path to £1M ARR',\n", "    xaxis_title='Year',\n", "    yaxis_title='Annual Revenue (£)',\n", "    height=500,\n", "    hovermode='x unified'\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Executive Summary: The Complete Client Evolution Story\n", "\n", "### 🎯 Key Achievements\n", "\n", "1. **Client Growth**: From 2 to 22 clients across 10+ industries\n", "2. **Revenue Evolution**: £11,937 (2020) → £262,466 (2024) - **2,098% growth**\n", "3. **Business Model Transformation**: 100% modeling fees → 93% SaaS revenue\n", "4. **Contract Size Growth**: £600 average → £10,419 (<PERSON>)\n", "\n", "### 🌟 Client Success Stories\n", "\n", "- **<PERSON>**: The game-changer - from £20,400 to £219,216 in one year\n", "- **UPOD Medical**: New enterprise validation with £22,409 first contract\n", "- **L'atelier Nawbar**: The believer - first client, 4-year journey\n", "- **Hosny Homany Industrial**: Manufacturing sector proof point\n", "\n", "### 📊 Portfolio Insights\n", "\n", "- **Revenue Concentration**: Top 5 clients = 85% of revenue (risk & opportunity)\n", "- **Industry Diversification**: From jewelry to healthcare, proving universal appeal\n", "- **Geographic Expansion**: Lebanon → UK → Netherlands → Global\n", "- **Retention Indicators**: 11 clients with recurring patterns\n", "\n", "### 🚀 The Path Forward\n", "\n", "Based on current trajectory:\n", "- **2025 Projection**: £360,000 - £450,000\n", "- **£1M ARR Target**: Achievable by 2026-2027\n", "- **Required**: 3-4x current client base or 2-3 more Devin <PERSON>go-sized clients\n", "\n", "### 💡 Strategic Recommendations\n", "\n", "1. **Reduce Concentration Risk**: No client should exceed 40% of revenue\n", "2. **Focus on Enterprise**: UPOD and Devin validate enterprise potential\n", "3. **Expand Recurring Base**: Convert project clients to monthly SaaS\n", "4. **Industry Specialization**: Double down on e-commerce and medical devices\n", "\n", "---\n", "\n", "*This data-driven analysis demonstrates MCX3D's remarkable journey from a services company to a scalable SaaS platform, validated by 22 diverse clients who believed in the vision of democratizing 3D commerce.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 6: MCX3D's Technology Legacy & Business Intelligence\n", "\n", "### 🔧 **Technology Contributions That Endure**\n", "\n", "MCX3D's impact extends beyond traditional 3D modeling - we've created lasting technology solutions that continue operating long after project completion.\n", "\n", "### 🏥 **S.I.N. Dental 3D Viewer: Still Online Today**\n", "\n", "**Client**: <PERSON> - Sin Dental (S.I.N. 360)  \n", "**Technology**: 3D viewer for dental implant systems  \n", "**Status**: **Still online and operational**  \n", "**Impact**: Demonstrates MCX3D's technical depth beyond standard visualization\n", "\n", "This ongoing operational technology showcases MCX3D's ability to create durable, enterprise-grade solutions that provide lasting value. The 3D viewer remains functional years after initial development, proving the quality and sustainability of our technical implementations.\n", "\n", "### 🤝 **Business Relationship Intelligence: Entrepreneur Loyalty**\n", "\n", "**The Buff BBQ → UPOD Medical Journey**\n", "\n", "A powerful testament to MCX3D's partnership value:\n", "\n", "1. **Buff BBQ** (Outdoor Equipment): Specialized in customizable outdoor BBQ equipment\n", "2. **Business Challenge**: Company closed due to supplier issues beyond their control\n", "3. **Entrepreneur Resilience**: Same entrepreneur founded **UPOD Medical Ltd** (AI Dental Technology)\n", "4. **Partnership Continuity**: Returned to MCX3D for new venture - \"sign of our successful relationship\"\n", "5. **Business Evolution**: From outdoor equipment to AI-powered medical devices\n", "\n", "**Key Insight**: This demonstrates how MCX3D builds relationships that survive business transitions, showing true partnership value beyond individual projects.\n", "\n", "### 🏷️ **Brand Name Intelligence: The Real Companies Behind the Names**\n", "\n", "Our client verification revealed fascinating brand insights:\n", "\n", "- **ESC S.A.L** → **Patchit**: High-end chocolate manufacturing in Lebanon\n", "- **Lines and Arts s.a.r.l** → **Bobeli Printing Boutique**: Lebanese printing and design services  \n", "- **<PERSON> - Sin Dental** → **S.I.N. 360**: Dental technology platform (founded 2003)\n", "\n", "These revelations show MCX3D works with established, serious businesses operating under different legal vs. brand names - adding credibility to our client portfolio."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-07-29T07:46:38.240452Z", "iopub.status.busy": "2025-07-29T07:46:38.240377Z", "iopub.status.idle": "2025-07-29T07:46:38.283486Z", "shell.execute_reply": "2025-07-29T07:46:38.283256Z"}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"base": ["2023-07-10T03:00:00.*********"], "customdata": {"bdata": "KVyPwu/LD0EAAAAAAAA5QA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=E-commerce Platform<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "E-commerce Platform", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "E-commerce Platform", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [64108800000], "xaxis": "x", "y": ["<PERSON>"], "yaxis": "y"}, {"base": ["2021-06-09T03:00:00.*********"], "customdata": {"bdata": "AAAAAMAZ2UAAAAAAAIBAQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Jewelry & Luxury<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Jewelry & Luxury", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Jewelry & Luxury", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [94262400000], "xaxis": "x", "y": ["L'atelier Nawbar"], "yaxis": "y"}, {"base": ["2024-11-11T02:00:00.*********"], "customdata": {"bdata": "UrgehTvi1UAAAAAAAAAYQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Medical Devices<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Medical Devices", "marker": {"color": "#00cc96", "pattern": {"shape": ""}}, "name": "Medical Devices", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [13654800000], "xaxis": "x", "y": ["UPOD MEDICAL LTD"], "yaxis": "y"}, {"base": ["2020-11-12T02:00:00.*********", "2023-10-18T03:00:00.*********"], "customdata": {"bdata": "AAAAAGBm0UAAAAAAAAAoQAAAAAAA4KVAAAAAAAAACEA=", "dtype": "f8", "shape": "2, 2"}, "hovertemplate": "Industry=Other<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Other", "marker": {"color": "#ab63fa", "pattern": {"shape": ""}}, "name": "Other", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [99100800000, 11919600000], "xaxis": "x", "y": ["L’atelier Nawbar", "<PERSON><PERSON>"], "yaxis": "y"}, {"base": ["2024-03-26T02:00:00.*********"], "customdata": {"bdata": "AAAAAIBEyEAAAAAAAAAIQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Manufacturing<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Manufacturing", "marker": {"color": "#FFA15A", "pattern": {"shape": ""}}, "name": "Manufacturing", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [28425600000], "xaxis": "x", "y": ["Hosny Homany Industrial Company SAL"], "yaxis": "y"}, {"base": ["2023-10-04T03:00:00.*********"], "customdata": {"bdata": "4XoUrgeTxEAAAAAAAAAUQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Healthcare Technology<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Healthcare Technology", "marker": {"color": "#19d3f3", "pattern": {"shape": ""}}, "name": "Healthcare Technology", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [34556400000], "xaxis": "x", "y": ["<PERSON> - Sin Dental"], "yaxis": "y"}, {"base": ["2023-05-09T03:00:00.*********"], "customdata": {"bdata": "AAAAAACIw0AAAAAAAAAAQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Outdoor Equipment<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Outdoor Equipment", "marker": {"color": "#FF6692", "pattern": {"shape": ""}}, "name": "Outdoor Equipment", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [**********], "xaxis": "x", "y": ["B<PERSON>"], "yaxis": "y"}, {"base": ["2021-11-26T02:00:00.*********"], "customdata": {"bdata": "rkfhejQGwUAAAAAAAAA5QA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Packaging & Design<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Packaging & Design", "marker": {"color": "#B6E880", "pattern": {"shape": ""}}, "name": "Packaging & Design", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [95212800000], "xaxis": "x", "y": ["Packegha"], "yaxis": "y"}, {"base": ["2022-10-20T03:00:00.*********"], "customdata": {"bdata": "AAAAAADNvUAAAAAAAAAAQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Food & Beverage<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Food & Beverage", "marker": {"color": "#FF97FF", "pattern": {"shape": ""}}, "name": "Food & Beverage", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [17971200000], "xaxis": "x", "y": ["ESC S.A.L"], "yaxis": "y"}, {"base": ["2023-08-03T03:00:00.*********"], "customdata": {"bdata": "AAAAAACorUAAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Traditional Retail<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Traditional Retail", "marker": {"color": "#FECB52", "pattern": {"shape": ""}}, "name": "Traditional Retail", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["W. <PERSON> & Sons."], "yaxis": "y"}, {"base": ["2024-01-03T02:00:00.*********"], "customdata": {"bdata": "AAAAAABwp0AAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Consumer Goods<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Consumer Goods", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Consumer Goods", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["TOMO BOTTLE LLC"], "yaxis": "y"}, {"base": ["2021-08-23T03:00:00.*********"], "customdata": {"bdata": "AAAAAADgpUAAAAAAAAAiQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Fashion & Apparel<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Fashion & Apparel", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Fashion & Apparel", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [29635200000], "xaxis": "x", "y": ["KITU KALI LIMITED"], "yaxis": "y"}, {"base": ["2024-01-05T02:00:00.*********"], "customdata": {"bdata": "AAAAAADAokAAAAAAAAAIQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Fashion & Accessories<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Fashion & Accessories", "marker": {"color": "#00cc96", "pattern": {"shape": ""}}, "name": "Fashion & Accessories", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [5097600000], "xaxis": "x", "y": ["<PERSON>"], "yaxis": "y"}, {"base": ["2023-01-03T02:00:00.*********"], "customdata": {"bdata": "AAAAAAAwoUAAAAAAAAAQQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Lighting & Interior<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Lighting & Interior", "marker": {"color": "#ab63fa", "pattern": {"shape": ""}}, "name": "Lighting & Interior", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [7606800000], "xaxis": "x", "y": ["Lumi SAL"], "yaxis": "y"}, {"base": ["2022-10-20T03:00:00.*********"], "customdata": {"bdata": "AAAAAAAEoEAAAAAAAAAYQA==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Printing & Design Services<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Printing & Design Services", "marker": {"color": "#FFA15A", "pattern": {"shape": ""}}, "name": "Printing & Design Services", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": [11833200000], "xaxis": "x", "y": ["Lines and Arts s.a.r.l"], "yaxis": "y"}, {"base": ["2022-03-11T02:00:00.*********"], "customdata": {"bdata": "AAAAAAAAiUAAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Technology<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Technology", "marker": {"color": "#19d3f3", "pattern": {"shape": ""}}, "name": "Technology", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["High Tech XL Group B.V"], "yaxis": "y"}, {"base": ["2023-08-04T03:00:00.*********"], "customdata": {"bdata": "AAAAAADgdUAAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Beauty & Cosmetics<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Beauty & Cosmetics", "marker": {"color": "#FF6692", "pattern": {"shape": ""}}, "name": "Beauty & Cosmetics", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["Face Junkie Ltd"], "yaxis": "y"}, {"base": ["2024-03-04T02:00:00.*********"], "customdata": {"bdata": "AAAAAADAckAAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Individual/Consultant<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Individual/Consultant", "marker": {"color": "#B6E880", "pattern": {"shape": ""}}, "name": "Individual/Consultant", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["<PERSON><PERSON>"], "yaxis": "y"}, {"base": ["2023-03-06T02:00:00.*********"], "customdata": {"bdata": "AAAAAABAb0AAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Digital Marketing<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Digital Marketing", "marker": {"color": "#FF97FF", "pattern": {"shape": ""}}, "name": "Digital Marketing", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["The Web Addicts"], "yaxis": "y"}, {"base": ["2023-09-13T03:00:00.*********"], "customdata": {"bdata": "AAAAAABAb0AAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Digital Services<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Digital Services", "marker": {"color": "#FECB52", "pattern": {"shape": ""}}, "name": "Digital Services", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["E-MOOD SAL"], "yaxis": "y"}, {"base": ["2022-07-04T03:00:00.*********"], "customdata": {"bdata": "AAAAAAAAaUAAAAAAAADwPw==", "dtype": "f8", "shape": "1, 2"}, "hovertemplate": "Industry=Business Services<br>Start=%{base}<br>End=%{x}<br>Client=%{y}<br>Revenue=%{customdata[0]}<br>Transactions=%{customdata[1]}<extra></extra>", "legendgroup": "Business Services", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Business Services", "orientation": "h", "showlegend": true, "textposition": "auto", "type": "bar", "x": {"bdata": "AA==", "dtype": "i1"}, "xaxis": "x", "y": ["INCORP"], "yaxis": "y"}], "layout": {"barmode": "overlay", "height": 800, "legend": {"title": {"text": "Industry"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "MCX3D Client Journey Timeline: All 22 Clients"}, "xaxis": {"anchor": "y", "domain": [0.0, 1.0], "title": {"text": "Timeline"}, "type": "date"}, "yaxis": {"anchor": "x", "categoryorder": "total ascending", "domain": [0.0, 1.0], "title": {"text": "Clients (ordered by revenue)"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎉 Thank you for joining us on this incredible journey from 2 to 22 clients!\n", "\n", "📈 The data tells a powerful story of perseverance, pivot, and exponential growth.\n", "\n", "🚀 Next stop: 100 clients and £1M ARR!\n"]}], "source": ["# Final visualization: The Complete Journey\n", "# Create an interactive timeline of all clients\n", "\n", "# Prepare data for timeline\n", "timeline_data = []\n", "for client_name, client_info in customer_data['customers'].items():\n", "    timeline_data.append({\n", "        'Client': client_name,\n", "        'Start': client_info['first_transaction'],\n", "        'End': client_info['last_transaction'],\n", "        'Revenue': client_info['total_revenue'],\n", "        'Industry': client_industries.get(client_name, 'Other'),\n", "        'Transactions': client_info['transaction_count']\n", "    })\n", "\n", "df_timeline = pd.DataFrame(timeline_data)\n", "df_timeline['Start'] = pd.to_datetime(df_timeline['Start'])\n", "df_timeline['End'] = pd.to_datetime(df_timeline['End'])\n", "df_timeline = df_timeline.sort_values('Revenue', ascending=False)\n", "\n", "# Create Gantt chart\n", "fig = px.timeline(df_timeline, \n", "                  x_start=\"Start\", \n", "                  x_end=\"End\", \n", "                  y=\"Client\",\n", "                  color=\"Industry\",\n", "                  hover_data=['Revenue', 'Transactions'],\n", "                  title=\"MCX3D Client Journey Timeline: All 22 Clients\",\n", "                  height=800)\n", "\n", "fig.update_yaxes(categoryorder=\"total ascending\")\n", "fig.update_layout(xaxis_title=\"Timeline\", yaxis_title=\"Clients (ordered by revenue)\")\n", "fig.show()\n", "\n", "print(\"\\n🎉 Thank you for joining us on this incredible journey from 2 to 22 clients!\")\n", "print(\"\\n📈 The data tells a powerful story of perseverance, pivot, and exponential growth.\")\n", "print(\"\\n🚀 Next stop: 100 clients and £1M ARR!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
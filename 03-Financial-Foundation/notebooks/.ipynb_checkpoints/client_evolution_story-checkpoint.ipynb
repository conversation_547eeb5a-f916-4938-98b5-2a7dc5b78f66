{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MCX3D Client Evolution Story: From 2 to 22 Clients\n", "\n", "## A Data-Driven Journey Through 5 Years of Growth\n", "\n", "This notebook tells the comprehensive story of MCX3D Ltd's transformation from a 3D modeling service to a thriving SaaS platform, through the lens of actual client payment data. We'll explore how 22 diverse clients across multiple industries helped shape the company's evolution.\n", "\n", "**Key Highlights:**\n", "- 📈 Revenue Growth: £11,937 (2020) → £262,466 (2024) - **2,098% increase**\n", "- 🏢 Client Growth: 2 → 22 clients across 10+ industries\n", "- 💰 Contract Evolution: £600 average → £10,419 average (<PERSON>)\n", "- 🌍 Geographic Expansion: Lebanon → UK → Netherlands → Global"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "sns.set_palette(\"husl\")\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.float_format', lambda x: '£{:,.2f}'.format(x) if abs(x) > 0.01 else '£0.00')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the revenue data\n", "with open('../data/revenue/customer_revenue_analysis.json', 'r') as f:\n", "    customer_data = json.load(f)\n", "\n", "with open('../data/revenue/revenue_dataset_2020_2025.json', 'r') as f:\n", "    revenue_data = json.load(f)\n", "\n", "# Extract detailed transactions\n", "transactions = revenue_data.get('detailed_transactions', [])\n", "\n", "# Convert to DataFrame\n", "df_transactions = pd.DataFrame(transactions)\n", "df_transactions['date'] = pd.to_datetime(df_transactions['date'])\n", "df_transactions['year'] = df_transactions['date'].dt.year\n", "df_transactions['month'] = df_transactions['date'].dt.to_period('M')\n", "\n", "print(f\"Total Clients: {customer_data['customer_count']}\")\n", "print(f\"Total Transactions: {len(transactions)}\")\n", "print(f\"Analysis Period: {customer_data['analysis_period']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 1: The Beginning (2020-2021) - Finding Product-Market Fit\n", "\n", "MCX3D's journey began with a single client belief in their vision. Let's explore how the early adopters shaped the company's direction."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define client industries (based on research and client names)\n", "client_industries = {\n", "    \"L'atelier Nawbar\": \"Jewelry & Luxury\",\n", "    \"L'atelier Nawbar\": \"Jewelry & Luxury\",\n", "    \"KITU KALI LIMITED\": \"Fashion & Apparel\",\n", "    \"Packegha\": \"Packaging & Design\",\n", "    \"High Tech XL Group B.V\": \"Technology\",\n", "    \"INCORP\": \"Business Services\",\n", "    \"ESC S.A.L\": \"Engineering Services\",\n", "    \"Lines and Arts s.a.r.l\": \"Creative & Design\",\n", "    \"Lumi SAL\": \"Lighting & Interior\",\n", "    \"The Web Addicts\": \"Digital Marketing\",\n", "    \"Buff BBQ\": \"Food & Beverage\",\n", "    \"<PERSON>\": \"E-commerce Platform\",\n", "    \"W. Salamoun & Sons.\": \"Traditional Retail\",\n", "    \"Face Junkie Ltd\": \"Beauty & Cosmetics\",\n", "    \"E-MOOD SAL\": \"Digital Services\",\n", "    \"<PERSON> - Sin Dental\": \"Healthcare\",\n", "    \"Chill Blast\": \"Consumer Products\",\n", "    \"TOMO BOTTLE LLC\": \"Consumer Goods\",\n", "    \"Kira Lillie\": \"Fashion & Accessories\",\n", "    \"<PERSON><PERSON>\": \"Individual/Consultant\",\n", "    \"Hosny Homany Industrial Company SAL\": \"Manufacturing\",\n", "    \"UPOD MEDICAL LTD\": \"Medical Devices\"\n", "}\n", "\n", "# Add industry to transactions\n", "df_transactions['industry'] = df_transactions['customer'].map(client_industries)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Early years analysis (2020-2021)\n", "early_years = df_transactions[df_transactions['year'].isin([2020, 2021])]\n", "early_clients = early_years.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'date': ['min', 'max']\n", "}).round(2)\n", "\n", "early_clients.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'First Payment', 'Last Payment']\n", "early_clients = early_clients.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🚀 The Early Believers (2020-2021):\\n\")\n", "print(early_clients)\n", "\n", "# Visualize early client contributions\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Revenue by client\n", "early_revenue = early_years.groupby('customer')['amount'].sum().sort_values(ascending=True)\n", "early_revenue.plot(kind='barh', ax=ax1, color='skyblue')\n", "ax1.set_title('Early Client Revenue Contributions (2020-2021)', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Revenue (£)')\n", "\n", "# Revenue type distribution\n", "early_type = early_years.groupby('revenue_type')['amount'].sum()\n", "early_type.plot(kind='pie', ax=ax2, autopct='%1.1f%%', startangle=90)\n", "ax2.set_title('Revenue Model in Early Years', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Insights from the Early Years:\n", "\n", "1. **L'atelier Nawbar** was the founding client, contributing 86% of 2020 revenue with modeling fees\n", "2. **The Pivot Moment**: In 2021, the company shifted from pure modeling fees to a sales model\n", "3. **Early Validation**: 4 diverse clients across jewelry, fashion, and packaging industries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 2: The Pivot (2022) - Diversification & Experimentation\n", "\n", "2022 marked a critical year of experimentation as MCX3D tested their platform across various industries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2022 Analysis\n", "year_2022 = df_transactions[df_transactions['year'] == 2022]\n", "clients_2022 = year_2022.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'industry': 'first'\n", "}).round(2)\n", "\n", "clients_2022.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Industry']\n", "clients_2022 = clients_2022.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🔄 The Pivot Year - 2022 Client Portfolio:\\n\")\n", "print(clients_2022)\n", "print(f\"\\nNew Industries Entered: {clients_2022['Industry'].nunique()}\")\n", "print(f\"Average Contract Size: £{year_2022['amount'].mean():,.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create client acquisition timeline\n", "client_first_payment = df_transactions.groupby('customer')['date'].min().reset_index()\n", "client_first_payment['year'] = client_first_payment['date'].dt.year\n", "client_first_payment['industry'] = client_first_payment['customer'].map(client_industries)\n", "\n", "# Cumulative client count\n", "cumulative_clients = client_first_payment.groupby('year').size().cumsum()\n", "\n", "# Visualize client acquisition\n", "fig = go.Figure()\n", "\n", "# Add cumulative line\n", "fig.add_trace(go.<PERSON>(\n", "    x=cumulative_clients.index,\n", "    y=cumulative_clients.values,\n", "    mode='lines+markers',\n", "    name='Cumulative Clients',\n", "    line=dict(color='blue', width=3),\n", "    marker=dict(size=10)\n", "))\n", "\n", "# Add new clients per year bars\n", "new_clients_per_year = client_first_payment.groupby('year').size()\n", "fig.add_trace(go.Bar(\n", "    x=new_clients_per_year.index,\n", "    y=new_clients_per_year.values,\n", "    name='New Clients',\n", "    marker_color='lightblue',\n", "    yaxis='y2'\n", "))\n", "\n", "# Update layout\n", "fig.update_layout(\n", "    title='Client Acquisition Journey: From 2 to 22',\n", "    xaxis_title='Year',\n", "    yaxis_title='Cumulative Client Count',\n", "    yaxis2=dict(title='New Clients per Year', overlaying='y', side='right'),\n", "    hovermode='x unified',\n", "    height=500\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 3: The Breakthrough (2023) - Market Validation\n", "\n", "2023 was the year everything changed. The entry of <PERSON> and rapid expansion across industries validated MCX3D's platform approach."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2023 Deep Dive\n", "year_2023 = df_transactions[df_transactions['year'] == 2023]\n", "\n", "# Calculate key metrics\n", "clients_2023 = year_2023.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean'],\n", "    'industry': 'first',\n", "    'date': ['min', 'max']\n", "})\n", "\n", "clients_2023.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Industry', 'First Payment', 'Last Payment']\n", "clients_2023 = clients_2023.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"🚀 The Breakthrough Year - 2023:\\n\")\n", "print(f\"Total Revenue: £{year_2023['amount'].sum():,.2f}\")\n", "print(f\"Client Count: {year_2023['customer'].nunique()}\")\n", "print(f\"Average Contract: £{year_2023['amount'].mean():,.2f}\")\n", "print(f\"\\nTop 5 Clients:\")\n", "print(clients_2023.head()[['Total Revenue', 'Industry', 'Transactions']])\n", "\n", "# Highlight <PERSON>'s entry\n", "devin_2023 = year_2023[year_2023['customer'] == '<PERSON>']\n", "print(f\"\\n⭐ <PERSON>'s Entry:\")\n", "print(f\"   First Contract: £{devin_2023['amount'].iloc[0]:,.2f}\")\n", "print(f\"   Total 2023 Revenue: £{devin_2023['amount'].sum():,.2f}\")\n", "print(f\"   Revenue Share: {(devin_2023['amount'].sum() / year_2023['amount'].sum() * 100):.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Industry diversification visualization\n", "industry_revenue = df_transactions.groupby(['year', 'industry'])['amount'].sum().reset_index()\n", "\n", "# Create stacked area chart\n", "fig = px.area(industry_revenue, \n", "              x='year', \n", "              y='amount', \n", "              color='industry',\n", "              title='Industry Diversification Over Time',\n", "              labels={'amount': 'Revenue (£)', 'year': 'Year'},\n", "              height=500)\n", "\n", "fig.update_layout(hovermode='x unified')\n", "fig.show()\n", "\n", "# Industry breakdown for 2023\n", "industry_2023 = year_2023.groupby('industry')['amount'].sum().sort_values(ascending=False)\n", "\n", "plt.figure(figsize=(10, 6))\n", "industry_2023.plot(kind='bar', color='coral')\n", "plt.title('2023 Revenue by Industry', fontsize=14, fontweight='bold')\n", "plt.xlabel('Industry')\n", "plt.ylabel('Revenue (£)')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 4: The Scale (2024) - Enterprise Success\n", "\n", "2024 demonstrated MCX3D's ability to scale existing relationships and attract new enterprise clients like UPOD Medical."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2024 Analysis - The Scale Year\n", "year_2024 = df_transactions[df_transactions['year'] == 2024]\n", "\n", "# Client performance\n", "clients_2024 = year_2024.groupby('customer').agg({\n", "    'amount': ['sum', 'count', 'mean', 'max'],\n", "    'industry': 'first'\n", "})\n", "\n", "clients_2024.columns = ['Total Revenue', 'Transactions', 'Avg Transaction', 'Max Transaction', 'Industry']\n", "clients_2024 = clients_2024.sort_values('Total Revenue', ascending=False)\n", "\n", "print(\"📈 The Scale Year - 2024 Performance:\\n\")\n", "print(f\"Total Revenue: £{year_2024['amount'].sum():,.2f}\")\n", "print(f\"YoY Growth: {((year_2024['amount'].sum() / year_2023['amount'].sum() - 1) * 100):.1f}%\")\n", "print(f\"\\nTop Enterprise Clients:\")\n", "print(clients_2024.head()[['Total Revenue', 'Transactions', 'Max Transaction', 'Industry']])\n", "\n", "# Highlight key achievements\n", "devin_growth = clients_2024.loc['<PERSON>', 'Total Revenue'] / clients_2023.loc['<PERSON>', 'Total Revenue']\n", "print(f\"\\n🌟 Key Achievements:\")\n", "print(f\"   <PERSON>: {(devin_growth - 1) * 100:.1f}% YoY\")\n", "print(f\"   New Enterprise Client: UPOD Medical Ltd - £{clients_2024.loc['UPOD MEDICAL LTD', 'Total Revenue']:,.2f}\")\n", "print(f\"   Average Transaction Size: £{year_2024['amount'].mean():,.2f} (vs £{year_2023['amount'].mean():,.2f} in 2023)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Contract size evolution - showing growth in deal sizes\n", "contract_evolution = df_transactions.groupby(['year', 'customer'])['amount'].agg(['mean', 'max']).reset_index()\n", "\n", "# Top clients by average contract size in 2024\n", "top_contracts_2024 = contract_evolution[contract_evolution['year'] == 2024].nlargest(10, 'mean')\n", "\n", "# Create bubble chart showing contract evolution\n", "fig = go.Figure()\n", "\n", "# Add traces for each year\n", "colors = ['lightblue', 'blue', 'green', 'orange', 'red', 'purple']\n", "years = sorted(df_transactions['year'].unique())\n", "\n", "for i, year in enumerate(years):\n", "    year_data = contract_evolution[contract_evolution['year'] == year]\n", "    \n", "    fig.add_trace(go.<PERSON>(\n", "        x=[year] * len(year_data),\n", "        y=year_data['mean'],\n", "        mode='markers',\n", "        marker=dict(\n", "            size=year_data['max'] / 1000,  # Scale down for visibility\n", "            color=colors[i],\n", "            opacity=0.6,\n", "            line=dict(width=1, color='DarkSlateGray')\n", "        ),\n", "        text=year_data['customer'],\n", "        name=str(year),\n", "        hovertemplate='<b>%{text}</b><br>Avg: £%{y:,.0f}<br>Max: £%{marker.size:,.0f}k<extra></extra>'\n", "    ))\n", "\n", "fig.update_layout(\n", "    title='Contract Size Evolution: Average vs Maximum Deals',\n", "    xaxis_title='Year',\n", "    yaxis_title='Average Contract Size (£)',\n", "    height=600,\n", "    showlegend=False\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## All 22 Clients: Complete Portfolio Analysis\n", "\n", "Let's examine the complete client portfolio, showing how each of the 22 clients contributed to MCX3D's growth story."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complete client portfolio analysis\n", "all_clients = []\n", "\n", "for client_name, client_info in customer_data['customers'].items():\n", "    client_dict = {\n", "        'Client': client_name,\n", "        'Total Revenue': client_info['total_revenue'],\n", "        'Transactions': client_info['transaction_count'],\n", "        'Avg Transaction': client_info['average_transaction'],\n", "        'First Date': pd.to_datetime(client_info['first_transaction']),\n", "        'Last Date': pd.to_datetime(client_info['last_transaction']),\n", "        'Relationship Length (days)': (pd.to_datetime(client_info['last_transaction']) - \n", "                                     pd.to_datetime(client_info['first_transaction'])).days,\n", "        'Industry': client_industries.get(client_name, 'Other'),\n", "        'Revenue Share %': (client_info['total_revenue'] / 396907.87 * 100)\n", "    }\n", "    all_clients.append(client_dict)\n", "\n", "df_all_clients = pd.DataFrame(all_clients)\n", "df_all_clients = df_all_clients.sort_values('Total Revenue', ascending=False)\n", "\n", "# Display top 10 clients\n", "print(\"🏆 Top 10 Clients by Total Revenue:\\n\")\n", "print(df_all_clients.head(10)[['Client', 'Total Revenue', 'Revenue Share %', 'Industry', 'Transactions']].to_string(index=False))\n", "\n", "# Revenue concentration analysis\n", "top5_revenue = df_all_clients.head(5)['Total Revenue'].sum()\n", "print(f\"\\n📊 Revenue Concentration:\")\n", "print(f\"   Top 5 clients: £{top5_revenue:,.2f} ({(top5_revenue/396907.87*100):.1f}% of total)\")\n", "print(f\"   <PERSON> alone: {df_all_clients.iloc[0]['Revenue Share %']:.1f}% of total revenue\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive client visualization\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Client Revenue Ranking', 'Industry Distribution',\n", "                   'Client Lifetime Value vs Relationship Length', 'Transaction Frequency'),\n", "    specs=[[{'type': 'bar'}, {'type': 'pie'}],\n", "           [{'type': 'scatter'}, {'type': 'bar'}]]\n", ")\n", "\n", "# 1. Revenue Ranking (all 22 clients)\n", "fig.add_trace(\n", "    go.Bar(x=df_all_clients['Client'], \n", "           y=df_all_clients['Total Revenue'],\n", "           name='Revenue',\n", "           marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "\n", "# 2. Industry Distribution\n", "industry_totals = df_all_clients.groupby('Industry')['Total Revenue'].sum().sort_values(ascending=False)\n", "fig.add_trace(\n", "    go.Pie(labels=industry_totals.index, \n", "           values=industry_totals.values,\n", "           name='Industries'),\n", "    row=1, col=2\n", ")\n", "\n", "# 3. CLV vs Relationship Length\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>er(x=df_all_clients['Relationship Length (days)'],\n", "               y=df_all_clients['Total Revenue'],\n", "               mode='markers+text',\n", "               marker=dict(size=df_all_clients['Transactions']*2,\n", "                          color=df_all_clients['Avg Transaction'],\n", "                          colorscale='Viridis',\n", "                          showscale=True),\n", "               text=df_all_clients['Client'],\n", "               textposition='top center',\n", "               name='Clients'),\n", "    row=2, col=1\n", ")\n", "\n", "# 4. Transaction Frequency\n", "fig.add_trace(\n", "    go.Bar(x=df_all_clients.nlargest(10, 'Transactions')['Client'],\n", "           y=df_all_clients.nlargest(10, 'Transactions')['Transactions'],\n", "           name='Transactions',\n", "           marker_color='coral'),\n", "    row=2, col=2\n", ")\n", "\n", "# Update layout\n", "fig.update_xaxes(tickangle=-45, row=1, col=1)\n", "fig.update_xaxes(tickangle=-45, row=2, col=2)\n", "fig.update_xaxes(title_text='Relationship Length (days)', row=2, col=1)\n", "fig.update_yaxes(title_text='Total Revenue (£)', row=2, col=1)\n", "\n", "fig.update_layout(height=1000, showlegend=False, \n", "                  title_text=\"MCX3D Complete Client Portfolio Analysis\")\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Payment Patterns & SaaS Evolution\n", "\n", "Let's analyze how payment patterns evolved, showing the transition from project-based to recurring revenue."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Monthly payment frequency analysis\n", "monthly_payments = df_transactions.groupby(['month', 'customer']).agg({\n", "    'amount': 'sum',\n", "    'transaction_id': 'count'\n", "}).reset_index()\n", "\n", "monthly_payments.columns = ['month', 'customer', 'amount', 'payment_count']\n", "\n", "# Identify recurring customers (multiple payments)\n", "customer_payment_months = df_transactions.groupby('customer')['month'].nunique()\n", "recurring_customers = customer_payment_months[customer_payment_months >= 3].index.tolist()\n", "\n", "print(f\"🔄 Recurring Revenue Indicators:\")\n", "print(f\"   Customers with 3+ payment months: {len(recurring_customers)}\")\n", "print(f\"   Most consistent payers:\")\n", "for customer in customer_payment_months.nlargest(5).index:\n", "    months = customer_payment_months[customer]\n", "    total = df_all_clients[df_all_clients['Client'] == customer]['Total Revenue'].values[0]\n", "    print(f\"     - {customer}: {months} months, £{total:,.2f} total\")\n", "\n", "# Create payment heatmap for top customers\n", "top_customers = df_all_clients.head(10)['Client'].tolist()\n", "pivot_payments = df_transactions[df_transactions['customer'].isin(top_customers)].pivot_table(\n", "    values='amount',\n", "    index='customer',\n", "    columns=df_transactions['date'].dt.to_period('Q'),\n", "    aggfunc='sum',\n", "    fill_value=0\n", ")\n", "\n", "# Plot heatmap\n", "plt.figure(figsize=(14, 8))\n", "sns.heatmap(pivot_payments, cmap='YlOrRd', fmt='.0f', annot=True, cbar_kws={'label': 'Revenue (£)'})\n", "plt.title('Quarterly Payment Patterns - Top 10 Clients', fontsize=16, fontweight='bold')\n", "plt.xlabel('Quarter')\n", "plt.ylabel('Client')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chapter 5: The Future (2025+) - Path to £1M ARR\n", "\n", "Based on current trajectory and client patterns, let's project MCX3D's path forward."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2025 Performance (YTD)\n", "year_2025 = df_transactions[df_transactions['year'] == 2025]\n", "\n", "print(\"🚀 2025 Performance (Year to Date):\\n\")\n", "print(f\"Revenue YTD: £{year_2025['amount'].sum():,.2f}\")\n", "print(f\"Active Clients: {year_2025['customer'].nunique()}\")\n", "print(f\"Months Covered: {year_2025['date'].dt.month.nunique()}\")\n", "\n", "# Continuing clients\n", "clients_2025 = year_2025.groupby('customer')['amount'].sum().sort_values(ascending=False)\n", "print(f\"\\nActive Clients in 2025:\")\n", "for client, revenue in clients_2025.items():\n", "    print(f\"   - {client}: £{revenue:,.2f}\")\n", "\n", "# Growth projection\n", "# Calculate historical growth rates\n", "yearly_revenue = df_transactions.groupby('year')['amount'].sum()\n", "growth_rates = yearly_revenue.pct_change().dropna()\n", "\n", "# Project forward (conservative estimate using median growth)\n", "median_growth = growth_rates.median()\n", "projected_2025_full = year_2025['amount'].sum() * (12 / year_2025['date'].dt.month.nunique())\n", "\n", "print(f\"\\n📈 Projections:\")\n", "print(f\"   2025 Full Year (projected): £{projected_2025_full:,.2f}\")\n", "print(f\"   Historical Median Growth: {median_growth*100:.1f}%\")\n", "print(f\"   Path to £1M ARR: Need {1000000/projected_2025_full:.1f}x current run rate\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create growth trajectory visualization\n", "fig = go.Figure()\n", "\n", "# Historical data\n", "years = list(range(2020, 2025))\n", "revenues = [yearly_revenue[year] for year in years]\n", "\n", "# Add 2025 projection\n", "years.append(2025)\n", "revenues.append(projected_2025_full)\n", "\n", "# Project to 2027 (conservative and optimistic)\n", "future_years = [2026, 2027]\n", "conservative_growth = 0.5  # 50% YoY\n", "optimistic_growth = 1.0    # 100% YoY\n", "\n", "conservative_projection = [projected_2025_full]\n", "optimistic_projection = [projected_2025_full]\n", "\n", "for i in range(len(future_years)):\n", "    conservative_projection.append(conservative_projection[-1] * (1 + conservative_growth))\n", "    optimistic_projection.append(optimistic_projection[-1] * (1 + optimistic_growth))\n", "\n", "# Plot historical\n", "fig.add_trace(go.<PERSON>(\n", "    x=years,\n", "    y=revenues,\n", "    mode='lines+markers',\n", "    name='Actual Revenue',\n", "    line=dict(color='blue', width=3),\n", "    marker=dict(size=10)\n", "))\n", "\n", "# Plot projections\n", "fig.add_trace(go.<PERSON>(\n", "    x=[2025] + future_years,\n", "    y=conservative_projection,\n", "    mode='lines+markers',\n", "    name='Conservative (50% growth)',\n", "    line=dict(color='green', width=2, dash='dash'),\n", "    marker=dict(size=8)\n", "))\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x=[2025] + future_years,\n", "    y=optimistic_projection,\n", "    mode='lines+markers',\n", "    name='Optimistic (100% growth)',\n", "    line=dict(color='orange', width=2, dash='dot'),\n", "    marker=dict(size=8)\n", "))\n", "\n", "# Add £1M ARR line\n", "fig.add_hline(y=1000000, line_dash=\"dash\", line_color=\"red\", \n", "              annotation_text=\"£1M ARR Target\")\n", "\n", "fig.update_layout(\n", "    title='MCX3D Revenue Growth Trajectory: Path to £1M ARR',\n", "    xaxis_title='Year',\n", "    yaxis_title='Annual Revenue (£)',\n", "    height=500,\n", "    hovermode='x unified'\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Executive Summary: The Complete Client Evolution Story\n", "\n", "### 🎯 Key Achievements\n", "\n", "1. **Client Growth**: From 2 to 22 clients across 10+ industries\n", "2. **Revenue Evolution**: £11,937 (2020) → £262,466 (2024) - **2,098% growth**\n", "3. **Business Model Transformation**: 100% modeling fees → 93% SaaS revenue\n", "4. **Contract Size Growth**: £600 average → £10,419 (<PERSON>)\n", "\n", "### 🌟 Client Success Stories\n", "\n", "- **<PERSON>**: The game-changer - from £20,400 to £219,216 in one year\n", "- **UPOD Medical**: New enterprise validation with £22,409 first contract\n", "- **L'atelier Nawbar**: The believer - first client, 4-year journey\n", "- **Hosny Homany Industrial**: Manufacturing sector proof point\n", "\n", "### 📊 Portfolio Insights\n", "\n", "- **Revenue Concentration**: Top 5 clients = 85% of revenue (risk & opportunity)\n", "- **Industry Diversification**: From jewelry to healthcare, proving universal appeal\n", "- **Geographic Expansion**: Lebanon → UK → Netherlands → Global\n", "- **Retention Indicators**: 11 clients with recurring patterns\n", "\n", "### 🚀 The Path Forward\n", "\n", "Based on current trajectory:\n", "- **2025 Projection**: £360,000 - £450,000\n", "- **£1M ARR Target**: Achievable by 2026-2027\n", "- **Required**: 3-4x current client base or 2-3 more Devin <PERSON>go-sized clients\n", "\n", "### 💡 Strategic Recommendations\n", "\n", "1. **Reduce Concentration Risk**: No client should exceed 40% of revenue\n", "2. **Focus on Enterprise**: UPOD and Devin validate enterprise potential\n", "3. **Expand Recurring Base**: Convert project clients to monthly SaaS\n", "4. **Industry Specialization**: Double down on e-commerce and medical devices\n", "\n", "---\n", "\n", "*This data-driven analysis demonstrates MCX3D's remarkable journey from a services company to a scalable SaaS platform, validated by 22 diverse clients who believed in the vision of democratizing 3D commerce.*"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final visualization: The Complete Journey\n", "# Create an interactive timeline of all clients\n", "\n", "# Prepare data for timeline\n", "timeline_data = []\n", "for client_name, client_info in customer_data['customers'].items():\n", "    timeline_data.append({\n", "        'Client': client_name,\n", "        'Start': client_info['first_transaction'],\n", "        'End': client_info['last_transaction'],\n", "        'Revenue': client_info['total_revenue'],\n", "        'Industry': client_industries.get(client_name, 'Other'),\n", "        'Transactions': client_info['transaction_count']\n", "    })\n", "\n", "df_timeline = pd.DataFrame(timeline_data)\n", "df_timeline['Start'] = pd.to_datetime(df_timeline['Start'])\n", "df_timeline['End'] = pd.to_datetime(df_timeline['End'])\n", "df_timeline = df_timeline.sort_values('Revenue', ascending=False)\n", "\n", "# Create Gantt chart\n", "fig = px.timeline(df_timeline, \n", "                  x_start=\"Start\", \n", "                  x_end=\"End\", \n", "                  y=\"Client\",\n", "                  color=\"Industry\",\n", "                  hover_data=['Revenue', 'Transactions'],\n", "                  title=\"MCX3D Client Journey Timeline: All 22 Clients\",\n", "                  height=800)\n", "\n", "fig.update_yaxes(categoryorder=\"total ascending\")\n", "fig.update_layout(xaxis_title=\"Timeline\", yaxis_title=\"Clients (ordered by revenue)\")\n", "fig.show()\n", "\n", "print(\"\\n🎉 Thank you for joining us on this incredible journey from 2 to 22 clients!\")\n", "print(\"\\n📈 The data tells a powerful story of perseverance, pivot, and exponential growth.\")\n", "print(\"\\n🚀 Next stop: 100 clients and £1M ARR!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}
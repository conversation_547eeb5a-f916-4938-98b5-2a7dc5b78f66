#!/usr/bin/env python3
"""
Simple test to verify the client evolution notebook works correctly
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go

# Test notebook execution
def test_notebook():
    print("🧪 Testing Client Evolution Notebook\n")
    
    try:
        # Load data
        print("1. Testing data loading...")
        with open('../data/revenue/customer_revenue_analysis.json', 'r') as f:
            customer_data = json.load(f)
        
        with open('../data/revenue/revenue_dataset_2020_2025.json', 'r') as f:
            revenue_data = json.load(f)
        
        transactions = revenue_data.get('detailed_transactions', [])
        df_transactions = pd.DataFrame(transactions)
        df_transactions['date'] = pd.to_datetime(df_transactions['date'])
        df_transactions['year'] = df_transactions['date'].dt.year
        df_transactions['revenue_type'] = df_transactions['category']
        
        print(f"   ✅ Loaded {len(transactions)} transactions for {customer_data['customer_count']} clients")
        
        # Test industry mapping with exact client names
        print("2. Testing industry mapping...")
        client_industries = {
            "Buff BBQ": "Food & Beverage",
            "Chill Blast": "Consumer Products",
            "<PERSON> - <PERSON> Dental": "Healthcare",
            "Devin StoneAlgo": "E-commerce Platform",
            "E-MOOD SAL": "Digital Services",
            "ESC S.A.L": "Engineering Services",
            "Face Junkie Ltd": "Beauty & Cosmetics",
            "High Tech XL Group B.V": "Technology",
            "Hosny Homany Industrial Company SAL": "Manufacturing",
            "INCORP": "Business Services",
            "KITU KALI LIMITED": "Fashion & Apparel",
            "Kira Lillie": "Fashion & Accessories",
            "L'atelier Nawbar": "Jewelry & Luxury",
            "Lines and Arts s.a.r.l": "Creative & Design",
            "Lumi SAL": "Lighting & Interior",
            "L'atelier Nawbar": "Jewelry & Luxury",
            "Namir Singh Lashkar Maitala": "Individual/Consultant",
            "Packegha": "Packaging & Design",
            "TOMO BOTTLE LLC": "Consumer Goods",
            "The Web Addicts": "Digital Marketing",
            "UPOD MEDICAL LTD": "Medical Devices",
            "W. Salamoun & Sons.": "Traditional Retail"
        }
        
        df_transactions['industry'] = df_transactions['customer'].map(client_industries)
        unmapped = df_transactions[df_transactions['industry'].isna()]['customer'].nunique()
        
        if unmapped > 0:
            print(f"   ⚠️  Found {unmapped} unmapped clients")
            unmapped_clients = df_transactions[df_transactions['industry'].isna()]['customer'].unique()
            for client in unmapped_clients:
                print(f"      - \"{client}\"")
        else:
            print(f"   ✅ All {df_transactions['customer'].nunique()} clients mapped to industries")
        
        # Test basic analysis
        print("3. Testing basic analysis...")
        early_years = df_transactions[df_transactions['year'].isin([2020, 2021])]
        print(f"   ✅ Early years: {len(early_years)} transactions from {early_years['customer'].nunique()} clients")
        
        year_2024 = df_transactions[df_transactions['year'] == 2024]
        print(f"   ✅ 2024: {len(year_2024)} transactions, £{year_2024['amount'].sum():,.2f} revenue")
        
        # Test visualizations
        print("4. Testing visualizations...")
        
        # Simple matplotlib test
        fig, ax = plt.subplots(figsize=(6, 4))
        yearly_revenue = df_transactions.groupby('year')['amount'].sum()
        yearly_revenue.plot(kind='bar', ax=ax)
        ax.set_title('Revenue by Year - Test Chart')
        plt.close(fig)
        
        # Simple plotly test
        fig = go.Figure()
        fig.add_trace(go.Bar(x=yearly_revenue.index, y=yearly_revenue.values))
        fig.update_layout(title='Plotly Test Chart')
        
        print("   ✅ Visualization libraries working")
        
        # Key statistics
        print(f"\n📊 Key Statistics:")
        print(f"   - Total clients: {customer_data['customer_count']}")
        print(f"   - Total transactions: {len(df_transactions)}")
        print(f"   - Date range: {df_transactions['date'].min().strftime('%Y-%m-%d')} to {df_transactions['date'].max().strftime('%Y-%m-%d')}")
        print(f"   - Revenue types: {list(df_transactions['revenue_type'].unique())}")
        print(f"   - Industries covered: {df_transactions['industry'].nunique()}")
        print(f"   - Top client: {df_transactions.groupby('customer')['amount'].sum().idxmax()}")
        print(f"   - Total revenue: £{df_transactions['amount'].sum():,.2f}")
        
        print(f"\n🎉 All tests passed! The notebook is ready to run.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_notebook()
#!/usr/bin/env python3
"""
Visualization helper functions for client evolution analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Any


def format_currency(amount: float) -> str:
    """Format amount as currency with proper formatting"""
    return f"£{amount:,.2f}"


def calculate_growth_rate(start_value: float, end_value: float) -> float:
    """Calculate percentage growth rate"""
    if start_value == 0:
        return 0
    return ((end_value - start_value) / start_value) * 100


def get_client_metrics(df: pd.DataFrame, client_name: str) -> Dict[str, Any]:
    """Get comprehensive metrics for a specific client"""
    client_data = df[df['customer'] == client_name]
    
    if client_data.empty:
        return None
    
    metrics = {
        'total_revenue': client_data['amount'].sum(),
        'transaction_count': len(client_data),
        'avg_transaction': client_data['amount'].mean(),
        'max_transaction': client_data['amount'].max(),
        'min_transaction': client_data['amount'].min(),
        'first_date': client_data['date'].min(),
        'last_date': client_data['date'].max(),
        'active_months': client_data['date'].dt.to_period('M').nunique(),
        'revenue_by_year': client_data.groupby(client_data['date'].dt.year)['amount'].sum().to_dict(),
        'revenue_by_type': client_data.groupby('revenue_type')['amount'].sum().to_dict() if 'revenue_type' in client_data.columns else {}
    }
    
    # Calculate relationship duration
    metrics['relationship_days'] = (metrics['last_date'] - metrics['first_date']).days
    
    return metrics


def create_client_growth_chart(df: pd.DataFrame, client_name: str, figsize: Tuple[int, int] = (12, 6)) -> plt.Figure:
    """Create a growth chart for a specific client"""
    client_data = df[df['customer'] == client_name].copy()
    
    if client_data.empty:
        print(f"No data found for client: {client_name}")
        return None
    
    # Group by month
    client_data['month'] = client_data['date'].dt.to_period('M')
    monthly_revenue = client_data.groupby('month')['amount'].sum()
    
    # Create figure
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
    
    # Monthly revenue
    monthly_revenue.plot(kind='bar', ax=ax1, color='skyblue')
    ax1.set_title(f'{client_name} - Monthly Revenue')
    ax1.set_xlabel('Month')
    ax1.set_ylabel('Revenue (£)')
    ax1.tick_params(axis='x', rotation=45)
    
    # Cumulative revenue
    cumulative_revenue = monthly_revenue.cumsum()
    cumulative_revenue.plot(kind='line', ax=ax2, color='darkblue', marker='o')
    ax2.set_title(f'{client_name} - Cumulative Revenue')
    ax2.set_xlabel('Month')
    ax2.set_ylabel('Cumulative Revenue (£)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def create_industry_sunburst(df: pd.DataFrame, industry_mapping: Dict[str, str]) -> go.Figure:
    """Create a sunburst chart showing industry hierarchy and revenue"""
    # Add industry to dataframe
    df['industry'] = df['customer'].map(industry_mapping)
    
    # Create hierarchy data
    hierarchy_data = []
    
    # Root
    total_revenue = df['amount'].sum()
    hierarchy_data.append({
        'labels': 'Total Revenue',
        'parents': '',
        'values': total_revenue
    })
    
    # Industries
    industry_revenue = df.groupby('industry')['amount'].sum()
    for industry, revenue in industry_revenue.items():
        hierarchy_data.append({
            'labels': industry,
            'parents': 'Total Revenue',
            'values': revenue
        })
    
    # Clients within industries
    for industry in industry_revenue.index:
        industry_clients = df[df['industry'] == industry].groupby('customer')['amount'].sum()
        for client, revenue in industry_clients.items():
            hierarchy_data.append({
                'labels': client,
                'parents': industry,
                'values': revenue
            })
    
    # Create dataframe
    hierarchy_df = pd.DataFrame(hierarchy_data)
    
    # Create sunburst
    fig = go.Figure(go.Sunburst(
        labels=hierarchy_df['labels'],
        parents=hierarchy_df['parents'],
        values=hierarchy_df['values'],
        branchvalues='total',
        textinfo='label+percent parent+value',
        hovertemplate='<b>%{label}</b><br>Revenue: £%{value:,.0f}<br>%{percentParent}<extra></extra>'
    ))
    
    fig.update_layout(
        title='Revenue Distribution by Industry and Client',
        height=700
    )
    
    return fig


def create_client_retention_matrix(df: pd.DataFrame) -> pd.DataFrame:
    """Create a retention matrix showing which clients were active in each period"""
    # Create monthly cohorts
    df['cohort_month'] = df.groupby('customer')['date'].transform('min').dt.to_period('M')
    df['transaction_month'] = df['date'].dt.to_period('M')
    
    # Calculate months since first transaction
    df['months_since_first'] = (df['transaction_month'] - df['cohort_month']).apply(lambda x: x.n)
    
    # Create retention matrix
    retention_matrix = df.groupby(['cohort_month', 'months_since_first'])['customer'].nunique().reset_index()
    retention_matrix = retention_matrix.pivot(index='cohort_month', columns='months_since_first', values='customer')
    
    # Calculate retention percentages
    cohort_sizes = df.groupby('cohort_month')['customer'].nunique()
    retention_pct = retention_matrix.divide(cohort_sizes, axis=0) * 100
    
    return retention_pct


def create_payment_frequency_heatmap(df: pd.DataFrame, top_n: int = 15) -> plt.Figure:
    """Create a heatmap showing payment frequency patterns"""
    # Get top N clients by revenue
    top_clients = df.groupby('customer')['amount'].sum().nlargest(top_n).index
    
    # Filter for top clients
    df_top = df[df['customer'].isin(top_clients)].copy()
    
    # Create pivot table with quarters
    df_top['quarter'] = df_top['date'].dt.to_period('Q')
    pivot_table = df_top.pivot_table(
        values='amount',
        index='customer',
        columns='quarter',
        aggfunc='sum',
        fill_value=0
    )
    
    # Create heatmap
    fig, ax = plt.subplots(figsize=(14, 10))
    sns.heatmap(pivot_table, 
                cmap='YlOrRd', 
                fmt='.0f', 
                annot=True, 
                cbar_kws={'label': 'Revenue (£)'},
                ax=ax)
    
    ax.set_title(f'Quarterly Payment Patterns - Top {top_n} Clients', fontsize=16, fontweight='bold')
    ax.set_xlabel('Quarter')
    ax.set_ylabel('Client')
    
    plt.tight_layout()
    return fig


def calculate_customer_lifetime_value(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate CLV metrics for all customers"""
    clv_metrics = []
    
    for customer in df['customer'].unique():
        customer_data = df[df['customer'] == customer]
        
        # Calculate metrics
        total_revenue = customer_data['amount'].sum()
        transaction_count = len(customer_data)
        first_date = customer_data['date'].min()
        last_date = customer_data['date'].max()
        lifetime_days = (last_date - first_date).days + 1  # Add 1 to include first day
        lifetime_months = lifetime_days / 30.44  # Average days per month
        
        # Monthly metrics
        active_months = customer_data['date'].dt.to_period('M').nunique()
        avg_monthly_value = total_revenue / active_months if active_months > 0 else 0
        
        # Churn indicator (no transaction in last 6 months from latest date in dataset)
        latest_date = df['date'].max()
        months_since_last = ((latest_date - last_date).days / 30.44)
        is_churned = months_since_last > 6
        
        clv_metrics.append({
            'Customer': customer,
            'Total Revenue': total_revenue,
            'Transactions': transaction_count,
            'Lifetime (days)': lifetime_days,
            'Lifetime (months)': round(lifetime_months, 1),
            'Active Months': active_months,
            'Avg Monthly Value': avg_monthly_value,
            'First Transaction': first_date,
            'Last Transaction': last_date,
            'Status': 'Churned' if is_churned else 'Active',
            'Months Since Last': round(months_since_last, 1)
        })
    
    return pd.DataFrame(clv_metrics).sort_values('Total Revenue', ascending=False)


def create_cohort_analysis(df: pd.DataFrame) -> go.Figure:
    """Create cohort analysis visualization"""
    # Define cohorts by first transaction quarter
    df['cohort'] = df.groupby('customer')['date'].transform('min').dt.to_period('Q')
    df['transaction_quarter'] = df['date'].dt.to_period('Q')
    
    # Calculate quarters since first transaction
    df['quarters_since_first'] = df.apply(
        lambda x: (x['transaction_quarter'] - x['cohort']).n, axis=1
    )
    
    # Create cohort revenue matrix
    cohort_revenue = df.groupby(['cohort', 'quarters_since_first'])['amount'].sum().reset_index()
    cohort_revenue_pivot = cohort_revenue.pivot(
        index='cohort', 
        columns='quarters_since_first', 
        values='amount'
    ).fillna(0)
    
    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=cohort_revenue_pivot.values,
        x=[f'Q+{i}' for i in cohort_revenue_pivot.columns],
        y=cohort_revenue_pivot.index.astype(str),
        colorscale='Blues',
        text=cohort_revenue_pivot.values.round(0),
        texttemplate='£%{text:,.0f}',
        textfont={"size": 10},
        hovertemplate='Cohort: %{y}<br>Period: %{x}<br>Revenue: £%{z:,.0f}<extra></extra>'
    ))
    
    fig.update_layout(
        title='Cohort Revenue Analysis',
        xaxis_title='Quarters Since First Transaction',
        yaxis_title='Cohort (First Transaction Quarter)',
        height=600
    )
    
    return fig


def identify_growth_patterns(df: pd.DataFrame) -> Dict[str, List[str]]:
    """Identify different growth patterns among clients"""
    patterns = {
        'steady_growth': [],
        'explosive_growth': [],
        'declining': [],
        'sporadic': [],
        'new_high_value': []
    }
    
    for customer in df['customer'].unique():
        customer_data = df[df['customer'] == customer]
        yearly_revenue = customer_data.groupby(customer_data['date'].dt.year)['amount'].sum()
        
        if len(yearly_revenue) < 2:
            # New clients
            if yearly_revenue.iloc[0] > 10000:
                patterns['new_high_value'].append(customer)
            continue
        
        # Calculate year-over-year growth rates
        growth_rates = yearly_revenue.pct_change().dropna()
        
        # Classify patterns
        if all(growth_rates > 0) and growth_rates.mean() > 0.5:
            patterns['explosive_growth'].append(customer)
        elif all(growth_rates > -0.1) and growth_rates.mean() > 0:
            patterns['steady_growth'].append(customer)
        elif growth_rates.mean() < -0.2:
            patterns['declining'].append(customer)
        else:
            patterns['sporadic'].append(customer)
    
    return patterns


def create_executive_dashboard(df: pd.DataFrame, customer_data: Dict) -> go.Figure:
    """Create an executive dashboard with key metrics"""
    # Calculate metrics
    total_revenue = df['amount'].sum()
    total_clients = df['customer'].nunique()
    avg_client_value = total_revenue / total_clients
    
    # Year-over-year metrics
    yearly_revenue = df.groupby(df['date'].dt.year)['amount'].sum()
    latest_year = yearly_revenue.index[-1]
    prev_year = yearly_revenue.index[-2] if len(yearly_revenue) > 1 else None
    
    yoy_growth = 0
    if prev_year:
        yoy_growth = ((yearly_revenue[latest_year] - yearly_revenue[prev_year]) / yearly_revenue[prev_year]) * 100
    
    # Create dashboard
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=('Total Revenue', 'Client Count', 'Avg Client Value',
                       'YoY Growth', 'Revenue Trend', 'Top 5 Clients'),
        specs=[[{'type': 'indicator'}, {'type': 'indicator'}, {'type': 'indicator'}],
               [{'type': 'indicator'}, {'type': 'scatter'}, {'type': 'bar'}]]
    )
    
    # KPI indicators
    fig.add_trace(go.Indicator(
        mode="number",
        value=total_revenue,
        number={'prefix': "£", 'valueformat': ",.0f"},
        domain={'x': [0, 0.33], 'y': [0.7, 1]}
    ), row=1, col=1)
    
    fig.add_trace(go.Indicator(
        mode="number",
        value=total_clients,
        number={'valueformat': ".0f"},
        domain={'x': [0.33, 0.67], 'y': [0.7, 1]}
    ), row=1, col=2)
    
    fig.add_trace(go.Indicator(
        mode="number",
        value=avg_client_value,
        number={'prefix': "£", 'valueformat': ",.0f"},
        domain={'x': [0.67, 1], 'y': [0.7, 1]}
    ), row=1, col=3)
    
    fig.add_trace(go.Indicator(
        mode="number+delta",
        value=yoy_growth,
        number={'suffix': "%", 'valueformat': ".1f"},
        delta={'reference': 0, 'relative': False},
        domain={'x': [0, 0.33], 'y': [0, 0.3]}
    ), row=2, col=1)
    
    # Revenue trend
    fig.add_trace(go.Scatter(
        x=yearly_revenue.index,
        y=yearly_revenue.values,
        mode='lines+markers',
        name='Revenue'
    ), row=2, col=2)
    
    # Top 5 clients
    top_5_clients = df.groupby('customer')['amount'].sum().nlargest(5)
    fig.add_trace(go.Bar(
        x=top_5_clients.values,
        y=top_5_clients.index,
        orientation='h',
        name='Top Clients'
    ), row=2, col=3)
    
    fig.update_layout(height=700, showlegend=False, title_text="Executive Dashboard")
    
    return fig
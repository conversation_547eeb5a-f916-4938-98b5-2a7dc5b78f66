#!/usr/bin/env python3
"""
Test script to verify the client evolution notebook runs without errors
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

def test_data_loading():
    """Test that all data loads correctly"""
    print("Testing data loading...")
    
    # Load data
    with open('../data/revenue/customer_revenue_analysis.json', 'r') as f:
        customer_data = json.load(f)
    
    with open('../data/revenue/revenue_dataset_2020_2025.json', 'r') as f:
        revenue_data = json.load(f)
    
    # Extract detailed transactions
    transactions = revenue_data.get('detailed_transactions', [])
    df_transactions = pd.DataFrame(transactions)
    df_transactions['date'] = pd.to_datetime(df_transactions['date'])
    df_transactions['year'] = df_transactions['date'].dt.year
    df_transactions['month'] = df_transactions['date'].dt.to_period('M')
    df_transactions['revenue_type'] = df_transactions['category']
    
    assert len(transactions) == 146, f"Expected 146 transactions, got {len(transactions)}"
    assert customer_data['customer_count'] == 22, f"Expected 22 customers, got {customer_data['customer_count']}"
    
    print("✅ Data loading test passed")
    return df_transactions, customer_data

def test_industry_mapping(df_transactions):
    """Test that all clients are mapped to industries"""
    print("Testing industry mapping...")
    
    client_industries = {
        "Buff BBQ": "Food & Beverage",
        "Chill Blast": "Consumer Products",
        "Derek Smith - Sin Dental": "Healthcare",
        "Devin StoneAlgo": "E-commerce Platform",
        "E-MOOD SAL": "Digital Services",
        "ESC S.A.L": "Engineering Services",
        "Face Junkie Ltd": "Beauty & Cosmetics",
        "High Tech XL Group B.V": "Technology",
        "Hosny Homany Industrial Company SAL": "Manufacturing",
        "INCORP": "Business Services",
        "KITU KALI LIMITED": "Fashion & Apparel",
        "Kira Lillie": "Fashion & Accessories",
        "L'atelier Nawbar": "Jewelry & Luxury",
        "Lines and Arts s.a.r.l": "Creative & Design",
        "Lumi SAL": "Lighting & Interior",
        "L'atelier Nawbar": "Jewelry & Luxury",
        "Namir Singh Lashkar Maitala": "Individual/Consultant",
        "Packegha": "Packaging & Design",
        "TOMO BOTTLE LLC": "Consumer Goods",
        "The Web Addicts": "Digital Marketing",
        "UPOD MEDICAL LTD": "Medical Devices",
        "W. Salamoun & Sons.": "Traditional Retail"
    }
    
    df_transactions['industry'] = df_transactions['customer'].map(client_industries)
    unmapped = df_transactions[df_transactions['industry'].isna()]['customer'].nunique()
    
    assert unmapped == 0, f"Found {unmapped} unmapped clients"
    
    print("✅ Industry mapping test passed")
    return df_transactions

def test_early_years_analysis(df_transactions):
    """Test early years analysis"""
    print("Testing early years analysis...")
    
    early_years = df_transactions[df_transactions['year'].isin([2020, 2021])]
    early_clients = early_years.groupby('customer')['amount'].sum()
    
    assert len(early_clients) == 4, f"Expected 4 early clients, got {len(early_clients)}"
    assert early_clients.sum() > 20000, f"Expected early revenue > £20,000, got £{early_clients.sum()}"
    
    print("✅ Early years analysis test passed")

def test_client_portfolio_analysis(customer_data):
    """Test complete client portfolio analysis"""
    print("Testing client portfolio analysis...")
    
    client_industries = {
        "Buff BBQ": "Food & Beverage",
        "Chill Blast": "Consumer Products",
        "Derek Smith - Sin Dental": "Healthcare",
        "Devin StoneAlgo": "E-commerce Platform",
        "E-MOOD SAL": "Digital Services",
        "ESC S.A.L": "Engineering Services",
        "Face Junkie Ltd": "Beauty & Cosmetics",
        "High Tech XL Group B.V": "Technology",
        "Hosny Homany Industrial Company SAL": "Manufacturing",
        "INCORP": "Business Services",
        "KITU KALI LIMITED": "Fashion & Apparel",
        "Kira Lillie": "Fashion & Accessories",
        "L'atelier Nawbar": "Jewelry & Luxury",
        "Lines and Arts s.a.r.l": "Creative & Design",
        "Lumi SAL": "Lighting & Interior",
        "L'atelier Nawbar": "Jewelry & Luxury",
        "Namir Singh Lashkar Maitala": "Individual/Consultant",
        "Packegha": "Packaging & Design",
        "TOMO BOTTLE LLC": "Consumer Goods",
        "The Web Addicts": "Digital Marketing",
        "UPOD MEDICAL LTD": "Medical Devices",
        "W. Salamoun & Sons.": "Traditional Retail"
    }
    
    all_clients = []
    for client_name, client_info in customer_data['customers'].items():
        client_dict = {
            'Client': client_name,
            'Total Revenue': client_info['total_revenue'],
            'Industry': client_industries.get(client_name, 'Other'),
            'Revenue Share %': (client_info['total_revenue'] / 396907.87 * 100)
        }
        all_clients.append(client_dict)
    
    df_all_clients = pd.DataFrame(all_clients)
    df_all_clients = df_all_clients.sort_values('Total Revenue', ascending=False)
    
    # Check that Devin StoneAlgo is the top client
    top_client = df_all_clients.iloc[0]['Client']
    assert top_client == 'Devin StoneAlgo', f"Expected Devin StoneAlgo as top client, got {top_client}"
    
    # Check revenue concentration
    top5_revenue_share = df_all_clients.head(5)['Revenue Share %'].sum()
    assert top5_revenue_share > 80, f"Expected top 5 to have >80% share, got {top5_revenue_share:.1f}%"
    
    print("✅ Client portfolio analysis test passed")

def test_visualizations():
    """Test that visualization libraries work"""
    print("Testing visualization capabilities...")
    
    # Test matplotlib
    fig, ax = plt.subplots(figsize=(8, 6))
    ax.plot([1, 2, 3], [1, 4, 2])
    ax.set_title('Test Chart')
    plt.close(fig)
    
    # Test plotly
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=[1, 2, 3], y=[1, 4, 2], mode='lines'))
    fig.update_layout(title='Test Plotly Chart')
    
    print("✅ Visualization test passed")

def main():
    """Run all tests"""
    print("🧪 Testing Client Evolution Notebook Components\n")
    
    try:
        # Test data loading
        df_transactions, customer_data = test_data_loading()
        
        # Test industry mapping
        df_transactions = test_industry_mapping(df_transactions)
        
        # Test early years analysis
        test_early_years_analysis(df_transactions)
        
        # Test client portfolio analysis
        test_client_portfolio_analysis(customer_data)
        
        # Test visualizations
        test_visualizations()
        
        print(f"\n🎉 All tests passed! The notebook is ready to run.")
        print(f"\n📊 Key Statistics:")
        print(f"   - Total clients: {customer_data['customer_count']}")
        print(f"   - Total transactions: {len(df_transactions)}")
        print(f"   - Date range: {df_transactions['date'].min().strftime('%Y-%m-%d')} to {df_transactions['date'].max().strftime('%Y-%m-%d')}")
        print(f"   - Revenue types: {list(df_transactions['revenue_type'].unique())}")
        print(f"   - Industries: {df_transactions['industry'].nunique()}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Validate the financial datasets to ensure data quality,
check for duplicates, and verify complete coverage
"""

import json
import os
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any, Set

from utils import parse_xero_date

# Paths
DATA_PATH = "../data"
XERO_DATA_PATH = "../../01-XERO-Data/data"


def load_json(filepath: str) -> Dict:
    """Load JSON file with error handling"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return {}


def check_transaction_duplicates() -> Dict[str, Any]:
    """Check for duplicate transactions in the all_transactions file"""
    print("\n1. Checking for transaction duplicates...")
    
    # Load all transactions
    all_txns = load_json(os.path.join(DATA_PATH, "all_transactions_2020_onwards.json"))
    transactions = all_txns.get("transactions", [])
    
    # Check for duplicates by ID
    id_counts = Counter()
    duplicate_ids = []
    
    for txn in transactions:
        txn_id = txn.get("id")
        if txn_id:
            id_counts[txn_id] += 1
            if id_counts[txn_id] == 2:  # First duplicate
                duplicate_ids.append(txn_id)
    
    # Find transactions with duplicate IDs
    duplicates_by_id = defaultdict(list)
    for txn in transactions:
        txn_id = txn.get("id")
        if txn_id in duplicate_ids:
            duplicates_by_id[txn_id].append({
                "date": txn.get("date"),
                "type": txn.get("type"),
                "amount": txn.get("amount"),
                "source": txn.get("source")
            })
    
    # Check for potential duplicates by date/amount/description
    potential_duplicates = []
    seen = set()
    
    for txn in transactions:
        # Create a signature for the transaction
        signature = (
            txn.get("date", "")[:10],  # Date without time
            round(float(txn.get("amount", 0)), 2),
            txn.get("description", "")[:50],  # First 50 chars of description
            txn.get("contact", "")
        )
        
        if signature in seen and txn.get("type") != "payment":  # Payments might legitimately duplicate
            potential_duplicates.append({
                "date": txn.get("date"),
                "amount": txn.get("amount"),
                "description": txn.get("description"),
                "contact": txn.get("contact"),
                "type": txn.get("type"),
                "id": txn.get("id")
            })
        seen.add(signature)
    
    return {
        "total_transactions": len(transactions),
        "unique_ids": len(id_counts),
        "duplicate_ids": len(duplicate_ids),
        "duplicates_detail": dict(duplicates_by_id) if duplicate_ids else {},
        "potential_duplicates": len(potential_duplicates),
        "potential_duplicates_sample": potential_duplicates[:5]
    }


def verify_date_coverage() -> Dict[str, Any]:
    """Verify we have complete date coverage from 2020 onwards"""
    print("\n2. Verifying date coverage...")
    
    # Load all transactions
    all_txns = load_json(os.path.join(DATA_PATH, "all_transactions_2020_onwards.json"))
    transactions = all_txns.get("transactions", [])
    
    # Group by year and month
    coverage = defaultdict(lambda: defaultdict(int))
    date_errors = []
    
    for txn in transactions:
        date_str = txn.get("date")
        if date_str:
            try:
                date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                coverage[date_obj.year][date_obj.month] += 1
            except:
                date_errors.append(date_str)
    
    # Check for missing months
    missing_months = []
    for year in range(2020, 2026):
        for month in range(1, 13):
            if year == 2020 and month < 8:  # Data starts from August 2020
                continue
            if year == 2025 and month > 7:  # Data ends in July 2025
                break
            
            if coverage[year][month] == 0:
                missing_months.append(f"{year}-{month:02d}")
    
    return {
        "date_range": {
            "start": min(txn.get("date", "") for txn in transactions if txn.get("date")),
            "end": max(txn.get("date", "") for txn in transactions if txn.get("date"))
        },
        "coverage_by_year": {
            year: {
                "months_with_data": len(months),
                "total_transactions": sum(months.values())
            }
            for year, months in sorted(coverage.items())
        },
        "missing_months": missing_months,
        "date_parse_errors": len(date_errors),
        "date_error_samples": date_errors[:5]
    }


def compare_with_source_data() -> Dict[str, Any]:
    """Compare our extracted data with source Xero data"""
    print("\n3. Comparing with source data...")
    
    results = {
        "bank_transactions": {},
        "invoices": {},
        "payments": {}
    }
    
    # Compare bank transactions
    try:
        source_bank = load_json(os.path.join(XERO_DATA_PATH, "core/bank_transactions.json"))
        source_count = len(source_bank.get("bankTransactions", []))
        
        all_txns = load_json(os.path.join(DATA_PATH, "all_transactions_2020_onwards.json"))
        our_bank_count = sum(1 for t in all_txns.get("transactions", []) 
                           if t.get("source") == "bank_transactions")
        
        results["bank_transactions"] = {
            "source_count": source_count,
            "extracted_count": our_bank_count,
            "coverage_percentage": (our_bank_count / source_count * 100) if source_count > 0 else 0
        }
    except Exception as e:
        results["bank_transactions"]["error"] = str(e)
    
    # Compare invoices
    try:
        source_inv = load_json(os.path.join(XERO_DATA_PATH, "core/invoices.json"))
        source_count = len(source_inv.get("invoices", []))
        
        our_inv_count = sum(1 for t in all_txns.get("transactions", []) 
                          if t.get("source") == "invoices")
        
        results["invoices"] = {
            "source_count": source_count,
            "extracted_count": our_inv_count,
            "coverage_percentage": (our_inv_count / source_count * 100) if source_count > 0 else 0
        }
    except Exception as e:
        results["invoices"]["error"] = str(e)
    
    # Compare payments
    try:
        source_pay = load_json(os.path.join(XERO_DATA_PATH, "supplementary/payments/payments.json"))
        source_count = len(source_pay.get("payments", []))
        
        our_pay_count = sum(1 for t in all_txns.get("transactions", []) 
                          if t.get("source") == "payments")
        
        results["payments"] = {
            "source_count": source_count,
            "extracted_count": our_pay_count,
            "coverage_percentage": (our_pay_count / source_count * 100) if source_count > 0 else 0
        }
    except Exception as e:
        results["payments"]["error"] = str(e)
    
    return results


def validate_financial_summaries() -> Dict[str, Any]:
    """Validate the financial summary calculations"""
    print("\n4. Validating financial summaries...")
    
    # Load datasets
    revenue_data = load_json(os.path.join(DATA_PATH, "revenue/revenue_dataset_2020_2025.json"))
    expense_data = load_json(os.path.join(DATA_PATH, "expenses/expense_dataset_2020_2025.json"))
    summary_data = load_json(os.path.join(DATA_PATH, "consolidated/financial_summary_2020_2025.json"))
    
    validation_results = {
        "revenue_validation": {},
        "expense_validation": {},
        "calculation_checks": {}
    }
    
    # Validate revenue totals
    revenue_total_from_transactions = sum(
        t["amount"] for t in revenue_data.get("detailed_transactions", [])
    )
    revenue_total_from_summary = revenue_data.get("summary", {}).get("total_revenue", 0)
    
    validation_results["revenue_validation"] = {
        "transaction_total": revenue_total_from_transactions,
        "summary_total": revenue_total_from_summary,
        "match": abs(revenue_total_from_transactions - revenue_total_from_summary) < 0.01,
        "transaction_count": len(revenue_data.get("detailed_transactions", [])),
        "customer_count": len(revenue_data.get("customer_analysis", {}))
    }
    
    # Validate expense totals
    expense_total_from_transactions = sum(
        t["amount"] for t in expense_data.get("detailed_transactions", [])
    )
    expense_total_from_summary = expense_data.get("summary", {}).get("total_expenses", 0)
    
    validation_results["expense_validation"] = {
        "transaction_total": expense_total_from_transactions,
        "summary_total": expense_total_from_summary,
        "match": abs(expense_total_from_transactions - expense_total_from_summary) < 0.01,
        "transaction_count": len(expense_data.get("detailed_transactions", [])),
        "supplier_count": len(expense_data.get("supplier_analysis", {}))
    }
    
    # Check profit calculations
    summary_revenue = summary_data.get("overview", {}).get("total_revenue", 0)
    summary_expenses = summary_data.get("overview", {}).get("total_expenses", 0)
    summary_profit = summary_data.get("overview", {}).get("net_profit", 0)
    calculated_profit = summary_revenue - summary_expenses
    
    validation_results["calculation_checks"] = {
        "profit_calculation": {
            "summary_profit": summary_profit,
            "calculated_profit": calculated_profit,
            "match": abs(summary_profit - calculated_profit) < 0.01
        },
        "data_consistency": {
            "revenue_match": abs(summary_revenue - revenue_total_from_summary) < 0.01,
            "expense_match": abs(summary_expenses - expense_total_from_summary) < 0.01
        }
    }
    
    return validation_results


def generate_validation_report(results: Dict[str, Any]) -> None:
    """Generate a validation report"""
    report = {
        "validation_date": datetime.now().isoformat(),
        "validation_results": results,
        "summary": {
            "has_duplicates": results["duplicates"]["duplicate_ids"] > 0,
            "duplicate_count": results["duplicates"]["duplicate_ids"],
            "date_coverage_complete": len(results["date_coverage"]["missing_months"]) == 0,
            "missing_months": results["date_coverage"]["missing_months"],
            "source_data_coverage": {
                k: f"{v.get('coverage_percentage', 0):.1f}%" 
                for k, v in results["source_comparison"].items()
                if "coverage_percentage" in v
            },
            "calculations_valid": all(
                v.get("match", False) 
                for v in results["financial_validation"]["calculation_checks"].values()
                if isinstance(v, dict) and "match" in v
            )
        },
        "recommendations": []
    }
    
    # Add recommendations based on findings
    if report["summary"]["has_duplicates"]:
        report["recommendations"].append(
            f"Found {report['summary']['duplicate_count']} duplicate transactions. Review and remove duplicates."
        )
    
    if report["summary"]["missing_months"]:
        report["recommendations"].append(
            f"Missing data for {len(report['summary']['missing_months'])} months. Verify if this is expected."
        )
    
    for source, coverage in report["summary"]["source_data_coverage"].items():
        if float(coverage.rstrip('%')) < 95:
            report["recommendations"].append(
                f"Low coverage for {source}: {coverage}. Check extraction logic."
            )
    
    if not report["recommendations"]:
        report["recommendations"].append("All validation checks passed. Data quality is good.")
    
    # Save report
    report_path = os.path.join(DATA_PATH, "validation_report.json")
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✅ Validation report saved to {report_path}")
    
    # Print summary
    print("\n📊 Validation Summary:")
    print(f"   - Duplicate transactions: {report['summary']['duplicate_count']}")
    print(f"   - Missing months: {len(report['summary']['missing_months'])}")
    print(f"   - Bank transaction coverage: {report['summary']['source_data_coverage'].get('bank_transactions', 'N/A')}")
    print(f"   - Invoice coverage: {report['summary']['source_data_coverage'].get('invoices', 'N/A')}")
    print(f"   - Calculations valid: {report['summary']['calculations_valid']}")
    
    print("\n🎯 Recommendations:")
    for i, rec in enumerate(report["recommendations"], 1):
        print(f"   {i}. {rec}")


def main():
    """Run all validation checks"""
    print("Running data validation checks...")
    
    validation_results = {
        "duplicates": check_transaction_duplicates(),
        "date_coverage": verify_date_coverage(),
        "source_comparison": compare_with_source_data(),
        "financial_validation": validate_financial_summaries()
    }
    
    generate_validation_report(validation_results)
    
    print("\n✅ Data validation complete!")


if __name__ == "__main__":
    main()
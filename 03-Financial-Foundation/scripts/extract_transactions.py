#!/usr/bin/env python3
"""
Extract all financial transactions from 2020 onwards
Consolidates bank transactions, invoices, payments, and journal entries
"""

import json
import os
import sys
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any, Optional

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import from local utils
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils import parse_xero_date, format_currency, load_chart_of_accounts

# Paths to data files - updated to use new structure
DATA_PATH = "../../01-XERO-Data/data"
OUTPUT_PATH = "../data"

# Revenue account codes from chart of accounts
REVENUE_ACCOUNTS = {
    "200": "Sales",
    "250": "Modeling Fees", 
    "260": "Other Revenue",
    "270": "Interest Income",
    "280": "Realised Currency Gains"
}

# Major expense categories
EXPENSE_CATEGORIES = {
    "Direct Costs": ["310", "320", "325"],
    "Administrative": ["400", "401", "402", "403", "404", "405", "406", "408", "410", 
                      "412", "416", "420", "425", "429", "430", "433", "434", "437",
                      "441", "445", "449", "453", "461", "463", "469", "470", "473",
                      "474", "475", "477", "478", "485", "489", "493", "494", "495",
                      "497", "499"],
    "Staff Costs": ["477"],  # Directors' Remuneration
    "Financial": ["270", "280", "290", "300"]  # Interest and currency gains/losses
}

# Liability accounts of interest
LIABILITY_ACCOUNTS = {
    "835": "Directors' Loan Account",
    "840": "Suspense",
    "814": "PAYE Liability",
    "820": "VAT",
    "830": "Corporation Tax"
}


def load_json_file(filepath: str) -> Any:
    """Load JSON file with error handling"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: File not found: {filepath}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from {filepath}: {e}")
        return None


def extract_bank_transactions(from_date: datetime) -> List[Dict]:
    """Extract all bank transactions from the specified date"""
    transactions = []
    
    # Load bank transactions from new consolidated file
    bank_data = load_json_file(os.path.join(DATA_PATH, "core/bank_transactions.json"))
    if not bank_data:
        return transactions
    
    # New structure has bankTransactions field
    bank_txns = bank_data.get("bankTransactions", [])
    print(f"   Processing {len(bank_txns)} bank transactions...")
    
    for txn in bank_txns:
        date_str = txn.get("Date")
        if not date_str:
            continue
            
        txn_date = parse_xero_date(date_str)
        if not txn_date or txn_date < from_date:
            continue
        
        # Extract line items for account codes
        for line_item in txn.get("LineItems", []):
            transaction = {
                "id": txn.get("BankTransactionID"),
                "date": txn_date.isoformat(),
                "type": "bank_transaction",
                "sub_type": txn.get("Type", "").lower(),  # spend or receive
                "reference": txn.get("Reference", ""),
                "description": line_item.get("Description", ""),
                "account_code": line_item.get("AccountCode", ""),
                "account_name": get_account_name(line_item.get("AccountCode", "")),
                "amount": float(line_item.get("LineAmount", 0)),
                "currency": txn.get("CurrencyCode", "GBP"),
                "contact": txn.get("Contact", {}).get("Name", "") if txn.get("Contact") else "",
                "bank_account": txn.get("BankAccount", {}).get("Name", "") if txn.get("BankAccount") else "",
                "status": txn.get("Status", ""),
                "source": "bank_transactions"
            }
            transactions.append(transaction)
    
    return transactions


def extract_invoices(from_date: datetime) -> List[Dict]:
    """Extract all invoices (sales and purchases) from the specified date"""
    transactions = []
    
    # Load invoices from new consolidated file
    invoice_data = load_json_file(os.path.join(DATA_PATH, "core/invoices.json"))
    if not invoice_data:
        return transactions
    
    # New structure has invoices field
    invoices = invoice_data.get("invoices", [])
    print(f"   Processing {len(invoices)} invoices...")
    
    for inv in invoices:
        date_str = inv.get("Date") or inv.get("DateString")
        if not date_str:
            continue
            
        inv_date = parse_xero_date(date_str)
        if not inv_date or inv_date < from_date:
            continue
        
        # Extract line items
        for line_item in inv.get("LineItems", []):
            transaction = {
                "id": inv.get("InvoiceID"),
                "invoice_number": inv.get("InvoiceNumber", ""),
                "date": inv_date.isoformat(),
                "type": "invoice",
                "sub_type": inv.get("Type", "").lower(),  # accpay or accrec
                "reference": inv.get("Reference", ""),
                "description": line_item.get("Description", ""),
                "account_code": line_item.get("AccountCode", ""),
                "account_name": get_account_name(line_item.get("AccountCode", "")),
                "amount": float(line_item.get("LineAmount", 0)),
                "currency": inv.get("CurrencyCode", "GBP"),
                "contact": inv.get("Contact", {}).get("Name", "") if inv.get("Contact") else "",
                "status": inv.get("Status", ""),
                "amount_due": float(inv.get("AmountDue", 0)),
                "amount_paid": float(inv.get("AmountPaid", 0)),
                "source": "invoices"
            }
            transactions.append(transaction)
    
    return transactions


def extract_journal_entries(from_date: datetime) -> List[Dict]:
    """Extract manual journal entries - important for director's loan movements"""
    transactions = []
    
    # Load manual journals
    journal_data = load_json_file(os.path.join(DATA_PATH, "supplementary/misc/manual_journals.json"))
    if not journal_data:
        return transactions
    
    journals = journal_data.get("manualJournals", [])
    print(f"   Processing {len(journals)} manual journals...")
    
    for journal in journals:
        date_str = journal.get("Date")
        if not date_str:
            continue
            
        journal_date = parse_xero_date(date_str)
        if not journal_date or journal_date < from_date:
            continue
        
        # Extract journal lines
        for line in journal.get("JournalLines", []):
            transaction = {
                "id": journal.get("ManualJournalID"),
                "date": journal_date.isoformat(),
                "type": "journal",
                "sub_type": "manual_journal",
                "reference": journal.get("Reference", ""),
                "description": line.get("Description", "") or journal.get("Narration", ""),
                "account_code": line.get("AccountCode", ""),
                "account_name": get_account_name(line.get("AccountCode", "")),
                "amount": float(line.get("LineAmount", 0)),  # Positive for debit, negative for credit
                "currency": "GBP",  # Manual journals are typically in base currency
                "status": journal.get("Status", ""),
                "source": "manual_journals"
            }
            transactions.append(transaction)
    
    return transactions


def extract_payments(from_date: datetime) -> List[Dict]:
    """Extract payment transactions from the specified date"""
    transactions = []
    
    # Load payments
    payment_data = load_json_file(os.path.join(DATA_PATH, "supplementary/payments/payments.json"))
    if not payment_data:
        return transactions
    
    payments = payment_data.get("payments", [])
    print(f"   Processing {len(payments)} payments...")
    
    for payment in payments:
        date_str = payment.get("Date")
        if not date_str:
            continue
            
        payment_date = parse_xero_date(date_str)
        if not payment_date or payment_date < from_date:
            continue
        
        # Create transaction record for payment
        transaction = {
            "id": payment.get("PaymentID"),
            "date": payment_date.isoformat(),
            "type": "payment",
            "sub_type": payment.get("PaymentType", "").lower(),
            "reference": payment.get("Reference", ""),
            "description": f"Payment to/from {payment.get('Invoice', {}).get('Contact', {}).get('Name', 'Unknown')}",
            "account_code": payment.get("Account", {}).get("Code", ""),
            "account_name": get_account_name(payment.get("Account", {}).get("Code", "")),
            "amount": float(payment.get("Amount", 0)),
            "currency": payment.get("CurrencyCode", "GBP"),
            "contact": payment.get("Invoice", {}).get("Contact", {}).get("Name", ""),
            "invoice_number": payment.get("Invoice", {}).get("InvoiceNumber", ""),
            "status": payment.get("Status", ""),
            "source": "payments"
        }
        transactions.append(transaction)
    
    return transactions


# Load chart of accounts globally
ACCOUNTS_LOOKUP = {}

def load_accounts():
    """Load chart of accounts for account name lookup"""
    global ACCOUNTS_LOOKUP
    accounts_file = os.path.join(DATA_PATH, "core/accounts.json")
    try:
        with open(accounts_file, 'r') as f:
            accounts_data = json.load(f)
        
        # Handle both possible structures
        accounts_list = accounts_data.get("accounts", accounts_data) if isinstance(accounts_data, dict) else accounts_data
        
        for account in accounts_list:
            code = account.get("Code", "")
            if code:
                ACCOUNTS_LOOKUP[code] = account.get("Name", "")
    except Exception as e:
        print(f"Warning: Could not load accounts: {e}")

def get_account_name(account_code: str) -> str:
    """Get account name from chart of accounts"""
    # Check loaded accounts first
    if account_code in ACCOUNTS_LOOKUP:
        return ACCOUNTS_LOOKUP[account_code]
    # Fall back to known accounts
    elif account_code in REVENUE_ACCOUNTS:
        return REVENUE_ACCOUNTS[account_code]
    elif account_code in LIABILITY_ACCOUNTS:
        return LIABILITY_ACCOUNTS[account_code]
    else:
        return f"Account {account_code}"


def categorize_transaction(transaction: Dict) -> Dict:
    """Add category information to transaction"""
    account_code = transaction.get("account_code", "")
    
    # Categorize by account type
    if account_code in REVENUE_ACCOUNTS:
        transaction["category"] = "Revenue"
        transaction["sub_category"] = REVENUE_ACCOUNTS[account_code]
    elif account_code in LIABILITY_ACCOUNTS:
        transaction["category"] = "Liability"
        transaction["sub_category"] = LIABILITY_ACCOUNTS[account_code]
    else:
        # Check expense categories
        transaction["category"] = "Expense"  # Default
        for category, codes in EXPENSE_CATEGORIES.items():
            if account_code in codes:
                transaction["sub_category"] = category
                break
        else:
            transaction["sub_category"] = "Other"
    
    # Special handling for director's loan account
    if account_code == "835":
        transaction["is_directors_loan"] = True
    
    return transaction


def main():
    """Main extraction process"""
    print("Extracting all financial transactions from 2020 onwards...")
    
    # Load chart of accounts first
    load_accounts()
    
    # Set start date to January 1, 2020
    from_date = datetime(2020, 1, 1)
    
    # Extract all transaction types
    print("1. Extracting bank transactions...")
    bank_transactions = extract_bank_transactions(from_date)
    print(f"   Found {len(bank_transactions)} bank transactions")
    
    print("2. Extracting invoices...")
    invoices = extract_invoices(from_date)
    print(f"   Found {len(invoices)} invoice line items")
    
    print("3. Extracting journal entries...")
    journals = extract_journal_entries(from_date)
    print(f"   Found {len(journals)} journal entries")
    
    print("4. Extracting payments...")
    payments = extract_payments(from_date)
    print(f"   Found {len(payments)} payments")
    
    # Combine all transactions
    all_transactions = bank_transactions + invoices + journals + payments
    
    # Remove duplicates based on ID and type
    unique_transactions = {}
    for txn in all_transactions:
        key = f"{txn.get('type')}_{txn.get('id')}"
        if key not in unique_transactions or txn.get('source') == 'bank_transactions':
            # Prefer bank transactions over other sources for duplicates
            unique_transactions[key] = txn
    
    all_transactions = list(unique_transactions.values())
    
    # Categorize transactions
    print("5. Categorizing transactions...")
    for txn in all_transactions:
        categorize_transaction(txn)
    
    # Sort by date
    all_transactions.sort(key=lambda x: x.get("date", ""))
    
    # Create output directory
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # Save complete transaction dataset
    output_file = os.path.join(OUTPUT_PATH, "all_transactions_2020_onwards.json")
    with open(output_file, 'w') as f:
        json.dump({
            "extraction_date": datetime.now().isoformat(),
            "from_date": from_date.isoformat(),
            "transaction_count": len(all_transactions),
            "transactions": all_transactions
        }, f, indent=2)
    
    print(f"\nExtraction complete! Saved {len(all_transactions)} transactions to {output_file}")
    
    # Generate summary statistics
    summary = {
        "total_transactions": len(all_transactions),
        "by_type": defaultdict(int),
        "by_category": defaultdict(int),
        "by_year": defaultdict(int),
        "by_currency": defaultdict(int),
        "directors_loan_movements": 0
    }
    
    for txn in all_transactions:
        summary["by_type"][txn.get("type", "unknown")] += 1
        summary["by_category"][txn.get("category", "unknown")] += 1
        summary["by_currency"][txn.get("currency", "unknown")] += 1
        
        year = txn.get("date", "")[:4]
        if year:
            summary["by_year"][year] += 1
        
        if txn.get("is_directors_loan"):
            summary["directors_loan_movements"] += 1
    
    # Save summary
    summary_file = os.path.join(OUTPUT_PATH, "extraction_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(dict(summary), f, indent=2)
    
    print(f"\nSummary saved to {summary_file}")
    print("\nTransaction Summary:")
    print(f"- By Type: {dict(summary['by_type'])}")
    print(f"- By Category: {dict(summary['by_category'])}")
    print(f"- By Year: {dict(summary['by_year'])}")
    print(f"- Director's Loan Movements: {summary['directors_loan_movements']}")


if __name__ == "__main__":
    main()
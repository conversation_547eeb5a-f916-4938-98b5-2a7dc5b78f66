#!/usr/bin/env python3
"""
Analyze Director's Loan Account movements by examining bank transactions,
balance sheet data, and manual journals
"""

import json
import os
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any, Optional

from utils import parse_xero_date, format_currency, normalize_contact_name

# Paths
DATA_PATH = "../../01-XERO-Data/data"
OUTPUT_PATH = "../data/directors_loan"

# Director-related keywords for pattern matching
DIRECTOR_KEYWORDS = ["MAZEN", "AKKAOUI", "Director", "DLA", "Loan from Director", "Capital"]


def extract_balance_sheet_dla(year: int) -> Optional[float]:
    """Extract Director's Loan Account balance from balance sheet"""
    bs_file = os.path.join(DATA_PATH, f"reports/financial/balance-sheet-{year}-12-31.json")
    
    try:
        with open(bs_file, 'r') as f:
            bs_data = json.load(f)
        
        report = bs_data.get("report", {})
        
        # Navigate through balance sheet to find liabilities
        for section in report.get("Rows", []):
            if section.get("RowType") == "Section":
                title = section.get("Title", "")
                
                # Look for Liabilities section
                if "Liabilit" in title:
                    for subsection in section.get("Rows", []):
                        if subsection.get("RowType") == "Section":
                            # Look for Non-Current or Long-term Liabilities
                            for row in subsection.get("Rows", []):
                                if row.get("RowType") == "Row":
                                    cells = row.get("Cells", [])
                                    if len(cells) >= 2:
                                        account_name = cells[0].get("Value", "")
                                        
                                        # Check for Director's Loan Account
                                        if any(term in account_name for term in ["Director", "Loan Account", "DLA"]):
                                            amount_str = str(cells[1].get("Value", "0")).replace(",", "")
                                            try:
                                                return float(amount_str)
                                            except:
                                                return 0.0
    
    except Exception as e:
        print(f"Error loading balance sheet for {year}: {e}")
    
    return None


def analyze_all_transactions_for_directors() -> List[Dict]:
    """Analyze all transactions for director-related movements"""
    director_transactions = []
    
    try:
        # Load all transactions
        with open("../data/all_transactions_2020_onwards.json", 'r') as f:
            data = json.load(f)
        
        transactions = data.get("transactions", [])
        
        for txn in transactions:
            # Check if it's a DLA account transaction (account 835)
            if txn.get("account_code") == "835" or txn.get("is_directors_loan"):
                date_str = txn.get("date")
                if date_str:
                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    amount = float(txn.get("amount", 0))
                    txn_type = txn.get("type")
                    sub_type = txn.get("sub_type")
                    
                    # Determine movement type based on transaction details
                    if txn_type == "bank_transaction":
                        if sub_type == "receive":
                            # Money received from director
                            movement_type = "Capital Injection"
                            amount = abs(amount)  # Positive for money in
                        else:
                            # Money spent/paid to director
                            movement_type = "Withdrawal/Repayment"
                            amount = -abs(amount)  # Negative for money out
                    elif txn_type == "journal":
                        # For journal entries, positive = debit (reduces liability)
                        if amount > 0:
                            movement_type = "Repayment/Withdrawal"
                            amount = -amount  # Negative for money out
                        else:
                            movement_type = "Capital Injection"
                            amount = abs(amount)  # Positive for money in
                    else:
                        # For other types, use account behavior
                        movement_type = "Movement"
                    
                    director_transactions.append({
                        "date": date_str,
                        "year": date_obj.year,
                        "id": txn.get("id"),
                        "type": movement_type,
                        "amount": amount,
                        "currency": txn.get("currency", "GBP"),
                        "contact": normalize_contact_name(txn.get("contact", "")),
                        "reference": txn.get("reference", ""),
                        "description": txn.get("description", ""),
                        "account_code": txn.get("account_code"),
                        "account_name": txn.get("account_name", ""),
                        "transaction_type": txn_type,
                        "sub_type": sub_type,
                        "source": txn.get("source", "")
                    })
            else:
                # Also check for director-related transactions based on contact/reference
                contact_name = txn.get("contact", "")
                reference = txn.get("reference", "")
                description = txn.get("description", "")
                
                # Pattern matching for director-related transactions
                is_director_related = False
                reason = ""
                
                if any(keyword.upper() in contact_name.upper() for keyword in DIRECTOR_KEYWORDS):
                    is_director_related = True
                    reason = "Contact name match"
                elif any(keyword.upper() in reference.upper() for keyword in ["LOAN", "CAPITAL", "INVESTMENT", "DLA"]):
                    is_director_related = True
                    reason = "Reference keyword match"
                elif any(keyword.upper() in description.upper() for keyword in ["DIRECTOR", "LOAN", "CAPITAL"]):
                    is_director_related = True
                    reason = "Description keyword match"
                
                if is_director_related:
                    date_str = txn.get("date")
                    if date_str:
                        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        
                        amount = float(txn.get("amount", 0))
                        sub_type = txn.get("sub_type")
                        
                        # Determine if it's money in or out
                        if sub_type == "receive" or txn.get("category") == "Revenue":
                            movement_type = "Capital Injection"
                            amount = abs(amount)  # Positive for money in
                        else:
                            movement_type = "Withdrawal/Repayment"
                            amount = -abs(amount)  # Negative for money out
                        
                        director_transactions.append({
                            "date": date_str,
                            "year": date_obj.year,
                            "id": txn.get("id"),
                            "type": movement_type,
                            "amount": amount,
                            "currency": txn.get("currency", "GBP"),
                            "contact": normalize_contact_name(contact_name),
                            "reference": reference,
                            "description": description,
                            "account_code": txn.get("account_code"),
                            "account_name": txn.get("account_name", ""),
                            "match_reason": reason,
                            "transaction_type": txn.get("type"),
                            "sub_type": sub_type,
                            "source": txn.get("source", "")
                        })
    
    except Exception as e:
        print(f"Error analyzing transactions: {e}")
    
    return director_transactions


# Manual journal analysis is now handled by analyze_all_transactions_for_directors


def calculate_dla_movements(transactions: List[Dict], opening_balance: float = 0) -> Dict[str, Any]:
    """Calculate director's loan account movements and running balance"""
    # Sort by date
    sorted_txns = sorted(transactions, key=lambda x: x["date"])
    
    # Calculate running balance
    balance = opening_balance
    movements_by_year = defaultdict(lambda: {
        "injections": 0,
        "withdrawals": 0,
        "net_movement": 0,
        "closing_balance": 0,
        "transaction_count": 0
    })
    
    for txn in sorted_txns:
        year = txn["year"]
        amount = txn["amount"]
        
        balance += amount
        txn["running_balance"] = balance
        
        movements_by_year[year]["transaction_count"] += 1
        movements_by_year[year]["net_movement"] += amount
        
        if amount > 0:
            movements_by_year[year]["injections"] += amount
        else:
            movements_by_year[year]["withdrawals"] += abs(amount)
        
        movements_by_year[year]["closing_balance"] = balance
    
    return {
        "opening_balance": opening_balance,
        "closing_balance": balance,
        "total_injections": sum(yr["injections"] for yr in movements_by_year.values()),
        "total_withdrawals": sum(yr["withdrawals"] for yr in movements_by_year.values()),
        "net_movement": balance - opening_balance,
        "movements_by_year": dict(movements_by_year),
        "transactions": sorted_txns
    }


def main():
    """Analyze director's loan account movements"""
    print("Analyzing Director's Loan Account movements...")
    
    # Create output directory
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # 1. Extract DLA balances from balance sheets
    print("\n1. Extracting DLA balances from balance sheets...")
    dla_balances = {}
    for year in range(2020, 2026):
        balance = extract_balance_sheet_dla(year)
        if balance is not None:
            dla_balances[year] = balance
            print(f"   {year}: {format_currency(balance)}")
    
    # 2. Analyze all transactions for director movements
    print("\n2. Analyzing all transactions for director movements...")
    all_movements = analyze_all_transactions_for_directors()
    print(f"   Found {len(all_movements)} director-related transactions")
    
    # Show breakdown by source
    source_counts = defaultdict(int)
    for m in all_movements:
        source_counts[m.get("source", "unknown")] += 1
    
    print("   Transaction sources:")
    for source, count in source_counts.items():
        print(f"     - {source}: {count}")
    
    # 5. Calculate movements and balances
    print("\n4. Calculating DLA movements...")
    
    # Use the earliest known balance as opening
    opening_balance = dla_balances.get(2020, 0) or dla_balances.get(2021, 0) or 0
    
    movement_analysis = calculate_dla_movements(all_movements, opening_balance)
    
    # 6. Create comprehensive dataset
    dla_dataset = {
        "extraction_date": datetime.now().isoformat(),
        "summary": {
            "opening_balance_2020": opening_balance,
            "current_balance": movement_analysis["closing_balance"],
            "total_capital_injections": movement_analysis["total_injections"],
            "total_withdrawals": movement_analysis["total_withdrawals"],
            "net_director_investment": movement_analysis["net_movement"],
            "transaction_count": len(all_movements)
        },
        "balance_sheet_balances": dla_balances,
        "movement_analysis": movement_analysis,
        "annual_summary": {},
        "data_quality": {
            "has_balance_sheet_data": len(dla_balances) > 0,
            "has_transaction_data": len(all_movements) > 0,
            "years_with_bs_data": list(dla_balances.keys()),
            "transaction_date_range": {
                "start": min(m["date"] for m in all_movements) if all_movements else None,
                "end": max(m["date"] for m in all_movements) if all_movements else None
            }
        }
    }
    
    # Create annual summary
    for year in range(2020, 2026):
        dla_dataset["annual_summary"][year] = {
            "balance_sheet_balance": dla_balances.get(year),
            "movements": movement_analysis["movements_by_year"].get(year, {
                "injections": 0,
                "withdrawals": 0,
                "net_movement": 0,
                "transaction_count": 0
            })
        }
    
    # Save dataset
    output_file = os.path.join(OUTPUT_PATH, "directors_loan_analysis.json")
    with open(output_file, 'w') as f:
        json.dump(dla_dataset, f, indent=2)
    
    print(f"\n✅ Director's Loan analysis saved to {output_file}")
    
    # Create summary report
    print("\n5. Director's Loan Account Summary:")
    print(f"   Opening Balance (2020): {format_currency(opening_balance)}")
    print(f"   Current Balance: {format_currency(movement_analysis['closing_balance'])}")
    print(f"   Total Injections: {format_currency(movement_analysis['total_injections'])}")
    print(f"   Total Withdrawals: {format_currency(movement_analysis['total_withdrawals'])}")
    print(f"   Net Investment: {format_currency(movement_analysis['net_movement'])}")
    
    # Save transaction detail
    if all_movements:
        transaction_file = os.path.join(OUTPUT_PATH, "dla_transactions.json")
        with open(transaction_file, 'w') as f:
            json.dump({
                "extraction_date": datetime.now().isoformat(),
                "transaction_count": len(all_movements),
                "transactions": all_movements
            }, f, indent=2)
        print(f"\n✅ Transaction detail saved to {transaction_file}")
    
    # Create a simple summary for quick reference
    summary = {
        "Director's Loan Account Summary": {
            "Current Status": {
                "Balance": format_currency(movement_analysis['closing_balance']),
                "Total Investment": format_currency(movement_analysis['total_injections']),
                "Total Withdrawals": format_currency(movement_analysis['total_withdrawals'])
            },
            "Annual Balances": {
                str(year): format_currency(balance) 
                for year, balance in dla_balances.items()
            },
            "Data Quality": {
                "Balance Sheet Years": str(dla_dataset["data_quality"]["years_with_bs_data"]),
                "Transaction Count": len(all_movements),
                "Coverage": "Limited - manual review recommended"
            }
        }
    }
    
    summary_file = os.path.join(OUTPUT_PATH, "dla_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"✅ Summary report saved to {summary_file}")


if __name__ == "__main__":
    main()
"""
Shared utility functions for financial data processing
"""

import re
from datetime import datetime
from typing import Optional, Dict, List, Union
import json


def parse_xero_date(date_str: str) -> Optional[datetime]:
    """
    Parse Xero date format /Date(1649894400000+0000)/ to datetime object
    Also handles ISO format dates and Unix timestamps
    """
    if not date_str:
        return None
        
    # Handle Xero's special date format
    match = re.search(r'/Date\((\d+)([+-]\d{4})?\)/', str(date_str))
    if match:
        timestamp_ms = int(match.group(1))
        # Convert milliseconds to seconds
        return datetime.fromtimestamp(timestamp_ms / 1000)
    
    # Handle Unix timestamp (if it's a pure number)
    try:
        # Check if it's a numeric string
        if str(date_str).isdigit():
            timestamp = int(date_str)
            # Assume it's a Unix timestamp in seconds if it's reasonable
            if 946684800 <= timestamp <= 2147483647:  # Between 2000 and 2038
                return datetime.fromtimestamp(timestamp)
            # Otherwise might be milliseconds
            elif 946684800000 <= timestamp <= 2147483647000:
                return datetime.fromtimestamp(timestamp / 1000)
    except:
        pass
    
    # Try ISO format
    try:
        # Handle various ISO formats
        if 'T' in str(date_str):
            return datetime.fromisoformat(str(date_str).replace('Z', '+00:00'))
        else:
            # Try simple date format
            return datetime.strptime(str(date_str), '%Y-%m-%d')
    except:
        pass
    
    # Try other common formats
    for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y']:
        try:
            return datetime.strptime(str(date_str), fmt)
        except:
            continue
    
    return None


def format_currency(amount: float, currency: str = "GBP") -> str:
    """
    Format amount with currency symbol
    """
    symbols = {
        "GBP": "£",
        "USD": "$",
        "EUR": "€",
        "AED": "AED "
    }
    symbol = symbols.get(currency, currency + " ")
    
    # Handle negative amounts
    if amount < 0:
        return f"-{symbol}{abs(amount):,.2f}"
    else:
        return f"{symbol}{amount:,.2f}"


def normalize_contact_name(name: str) -> str:
    """
    Normalize contact names to handle Unicode variations
    """
    if not name:
        return ""
    
    # Common replacements for Unicode issues
    replacements = {
        "'": "'",
        "'": "'",
        """: '"',
        """: '"',
        "–": "-",
        "—": "-",
        " ": " ",  # non-breaking space to regular space
    }
    
    normalized = name
    for old, new in replacements.items():
        normalized = normalized.replace(old, new)
    
    # Remove extra spaces and strip
    normalized = ' '.join(normalized.split())
    
    return normalized.strip()


def get_fiscal_year(date: datetime, year_end_month: int = 12) -> int:
    """
    Get fiscal year for a given date
    Default is calendar year (December year-end)
    """
    if date.month <= year_end_month:
        return date.year
    else:
        return date.year + 1


def convert_currency(amount: float, from_currency: str, to_currency: str, 
                    rates: Dict[str, float] = None) -> float:
    """
    Convert amount between currencies using provided exchange rates
    Rates should be in format: {"USD": 1.25, "EUR": 1.15} (vs GBP)
    """
    if from_currency == to_currency:
        return amount
    
    # Default approximate rates if none provided
    if not rates:
        rates = {
            "GBP": 1.0,
            "USD": 1.25,
            "EUR": 1.15,
            "AED": 4.60
        }
    
    # Convert to GBP first, then to target currency
    amount_gbp = amount / rates.get(from_currency, 1.0)
    return amount_gbp * rates.get(to_currency, 1.0)


def group_by_period(transactions: List[Dict], period: str = "month") -> Dict[str, List[Dict]]:
    """
    Group transactions by time period (month, quarter, year)
    """
    grouped = {}
    
    for tx in transactions:
        date_str = tx.get("date")
        if not date_str:
            continue
        
        # Parse date
        if isinstance(date_str, str):
            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        else:
            date = date_str
            
        if period == "month":
            key = date.strftime("%Y-%m")
        elif period == "quarter":
            quarter = (date.month - 1) // 3 + 1
            key = f"{date.year}-Q{quarter}"
        elif period == "year":
            key = str(date.year)
        else:
            key = date.strftime("%Y-%m-%d")
            
        if key not in grouped:
            grouped[key] = []
        grouped[key].append(tx)
    
    return grouped


def calculate_running_balance(transactions: List[Dict], 
                             opening_balance: float = 0.0) -> List[Dict]:
    """
    Calculate running balance for a list of transactions
    Assumes transactions are sorted by date
    """
    balance = opening_balance
    
    for tx in transactions:
        amount = tx.get("amount", 0)
        
        # Determine if this increases or decreases the balance
        # This depends on the account type and transaction type
        if tx.get("type") == "bank_transaction":
            if tx.get("sub_type") == "receive":
                balance += amount
            else:
                balance -= amount
        else:
            # For other types, positive amounts increase, negative decrease
            balance += amount
        
        tx["running_balance"] = balance
    
    return transactions


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero
    """
    if denominator == 0:
        return default
    return numerator / denominator


def extract_account_hierarchy(account_code: str) -> Dict[str, str]:
    """
    Extract account hierarchy from account code
    E.g., "400" -> {"main": "400", "sub": None}
    E.g., "400.1" -> {"main": "400", "sub": "1"}
    """
    parts = str(account_code).split('.')
    return {
        "main": parts[0] if parts else "",
        "sub": parts[1] if len(parts) > 1 else None,
        "full": account_code
    }


def load_chart_of_accounts(accounts_file: str) -> Dict[str, Dict]:
    """
    Load and index chart of accounts by account code
    """
    try:
        with open(accounts_file, 'r') as f:
            accounts_data = json.load(f)
        
        # Create lookup by account code
        accounts_lookup = {}
        for account in accounts_data:
            code = account.get("Code", "")
            if code:
                accounts_lookup[code] = {
                    "name": account.get("Name", ""),
                    "type": account.get("Type", ""),
                    "tax_type": account.get("TaxType", ""),
                    "status": account.get("Status", ""),
                    "class": account.get("Class", ""),
                    "reporting_code": account.get("ReportingCode", ""),
                    "reporting_code_name": account.get("ReportingCodeName", "")
                }
        
        return accounts_lookup
    except Exception as e:
        print(f"Error loading chart of accounts: {e}")
        return {}
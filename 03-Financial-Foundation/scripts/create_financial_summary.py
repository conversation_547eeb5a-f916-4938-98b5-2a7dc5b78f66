#!/usr/bin/env python3
"""
Create comprehensive financial summary combining revenue, expenses, and director's loan data
Generates consolidated datasets ready for analysis and visualization
"""

import json
import os
from datetime import datetime
from typing import Dict, Any

from utils import format_currency

# Paths
DATA_PATH = "../data"
OUTPUT_PATH = "../data/consolidated"
REPORTS_PATH = "../reports"


def load_dataset(filename: str) -> Dict:
    """Load a JSON dataset with error handling"""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return {}


def calculate_profit_margins(revenue: float, expenses: float) -> Dict[str, float]:
    """Calculate various profit margins"""
    gross_profit = revenue - expenses
    
    return {
        "gross_profit": gross_profit,
        "gross_margin": (gross_profit / revenue * 100) if revenue > 0 else 0,
        "expense_ratio": (expenses / revenue * 100) if revenue > 0 else 0
    }


def main():
    """Create comprehensive financial summary"""
    print("Creating comprehensive financial summary...")
    
    # Create output directories
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    os.makedirs(REPORTS_PATH, exist_ok=True)
    
    # Load datasets
    print("\n1. Loading datasets...")
    revenue_data = load_dataset(os.path.join(DATA_PATH, "revenue/revenue_dataset_2020_2025.json"))
    expense_data = load_dataset(os.path.join(DATA_PATH, "expenses/expense_dataset_2020_2025.json"))
    dla_data = load_dataset(os.path.join(DATA_PATH, "directors_loan/directors_loan_analysis.json"))
    
    # Extract key metrics
    revenue_by_year = revenue_data.get("revenue_by_year", {})
    expenses_by_year = expense_data.get("expenses_by_year", {})
    dla_summary = dla_data.get("summary", {})
    
    # Create consolidated financial summary
    financial_summary = {
        "extraction_date": datetime.now().isoformat(),
        "company": "MCX3D LTD",
        "period": "2020-2025",
        "overview": {
            "total_revenue": revenue_data.get("summary", {}).get("total_revenue", 0),
            "total_expenses": expense_data.get("summary", {}).get("total_expenses", 0),
            "net_profit": revenue_data.get("summary", {}).get("total_revenue", 0) - 
                         expense_data.get("summary", {}).get("total_expenses", 0),
            "revenue_growth": revenue_data.get("summary", {}).get("revenue_growth_rate", 0),
            "expense_growth": expense_data.get("summary", {}).get("expense_growth_rate", 0),
            "director_investment": dla_summary.get("total_capital_injections", 0),
            "director_withdrawals": dla_summary.get("total_withdrawals", 0),
            "net_director_investment": dla_summary.get("net_director_investment", 0)
        },
        "annual_performance": {},
        "key_metrics": {},
        "data_quality": {
            "revenue_invoice_coverage": revenue_data.get("summary", {}).get("invoice_coverage_percentage", 0),
            "expense_transaction_coverage": expense_data.get("summary", {}).get("transaction_coverage_percentage", 0),
            "years_with_complete_data": [],
            "data_gaps": []
        }
    }
    
    # Process each year
    print("\n2. Processing annual data...")
    for year in range(2020, 2026):  # Include 2025
        year_str = str(year)
        
        # Get annual data
        revenue_year = revenue_by_year.get(year, {}) if isinstance(revenue_by_year.get(year), dict) else revenue_by_year.get(year_str, {})
        expense_year = expenses_by_year.get(year, {}) if isinstance(expenses_by_year.get(year), dict) else expenses_by_year.get(year_str, {})
        
        annual_revenue = revenue_year.get("total", 0)
        annual_expenses = expense_year.get("total", 0)
        
        # Calculate margins
        margins = calculate_profit_margins(annual_revenue, annual_expenses)
        
        # Get DLA movements
        dla_movements = dla_data.get("movement_analysis", {}).get("movements_by_year", {}).get(year, {})
        
        financial_summary["annual_performance"][year] = {
            "revenue": {
                "total": annual_revenue,
                "breakdown": revenue_year.get("breakdown_by_type", {}),
                "currency_breakdown": revenue_year.get("breakdown_by_currency", {}),
                "transaction_count": revenue_year.get("transaction_count", 0)
            },
            "expenses": {
                "total": annual_expenses,
                "breakdown": expense_year.get("breakdown_by_category", {}),
                "currency_breakdown": expense_year.get("breakdown_by_currency", {}),
                "transaction_count": expense_year.get("transaction_count", 0)
            },
            "profitability": margins,
            "directors_loan": {
                "injections": dla_movements.get("injections", 0),
                "withdrawals": dla_movements.get("withdrawals", 0),
                "net_movement": dla_movements.get("net_movement", 0),
                "closing_balance": dla_movements.get("closing_balance", 0)
            }
        }
        
        print(f"   {year}: Revenue: {format_currency(annual_revenue)}, "
              f"Expenses: {format_currency(annual_expenses)}, "
              f"Profit: {format_currency(margins['gross_profit'])}")
    
    # Calculate key business metrics
    print("\n3. Calculating key metrics...")
    
    # Revenue metrics
    revenue_2024 = revenue_by_year.get(2024, {}).get("total", 0) if isinstance(revenue_by_year.get(2024), dict) else revenue_by_year.get("2024", {}).get("total", 0)
    revenue_2021 = revenue_by_year.get(2021, {}).get("total", 0) if isinstance(revenue_by_year.get(2021), dict) else revenue_by_year.get("2021", {}).get("total", 0)
    
    # Expense metrics
    expense_2024 = expenses_by_year.get(2024, {}).get("total", 0) if isinstance(expenses_by_year.get(2024), dict) else expenses_by_year.get("2024", {}).get("total", 0)
    expense_2021 = expenses_by_year.get(2021, {}).get("total", 0) if isinstance(expenses_by_year.get(2021), dict) else expenses_by_year.get("2021", {}).get("total", 0)
    
    financial_summary["key_metrics"] = {
        "growth_metrics": {
            "revenue_cagr_2021_2024": (
                ((revenue_2024 / revenue_2021) ** (1/3) - 1) * 100 
                if revenue_2021 > 0 else 0
            ),
            "expense_cagr_2021_2024": (
                ((expense_2024 / expense_2021) ** (1/3) - 1) * 100 
                if expense_2021 > 0 else 0
            ),
            "revenue_multiple_2021_2024": revenue_2024 / revenue_2021 if revenue_2021 > 0 else 0
        },
        "efficiency_metrics": {
            "expense_to_revenue_ratio_2024": (expense_2024 / revenue_2024 * 100) if revenue_2024 > 0 else 0,
            "profit_margin_2024": ((revenue_2024 - expense_2024) / revenue_2024 * 100) if revenue_2024 > 0 else 0
        },
        "customer_metrics": {
            "customer_count": len(revenue_data.get("customer_analysis", {})) if revenue_data.get("customer_analysis") else 0,
            "average_revenue_per_customer": (
                revenue_data.get("summary", {}).get("total_revenue", 0) / len(revenue_data.get("customer_analysis", {}))
                if revenue_data.get("customer_analysis") and len(revenue_data.get("customer_analysis", {})) > 0 else 0
            )
        }
    }
    
    # Identify data quality issues
    data_gaps = []
    if revenue_data.get("summary", {}).get("invoice_coverage_percentage", 0) < 50:
        data_gaps.append("Low invoice coverage - missing transaction detail for most revenue")
    if expense_data.get("summary", {}).get("transaction_coverage_percentage", 0) < 50:
        data_gaps.append("Low expense transaction coverage - relying mostly on P&L summaries")
    if not dla_data.get("balance_sheet_balances"):
        data_gaps.append("No director's loan account balances in balance sheets")
    
    financial_summary["data_quality"]["data_gaps"] = data_gaps
    
    # Save consolidated summary
    summary_file = os.path.join(OUTPUT_PATH, "financial_summary_2020_2025.json")
    with open(summary_file, 'w') as f:
        json.dump(financial_summary, f, indent=2)
    
    print(f"\n✅ Financial summary saved to {summary_file}")
    
    # Create executive summary report
    exec_summary = {
        "MCX3D LTD Financial Summary 2020-2025": {
            "Company Performance": {
                "Total Revenue (5 years)": format_currency(financial_summary["overview"]["total_revenue"]),
                "Total Expenses (5 years)": format_currency(financial_summary["overview"]["total_expenses"]),
                "Net Profit (5 years)": format_currency(financial_summary["overview"]["net_profit"]),
                "Revenue Growth (2021-2024)": f"{financial_summary['overview']['revenue_growth']:.1f}%",
                "CAGR (2021-2024)": f"{financial_summary['key_metrics']['growth_metrics']['revenue_cagr_2021_2024']:.1f}%"
            },
            "2024 Performance": {
                "Revenue": format_currency(revenue_2024),
                "Expenses": format_currency(expense_2024),
                "Net Profit": format_currency(revenue_2024 - expense_2024),
                "Profit Margin": f"{financial_summary['key_metrics']['efficiency_metrics']['profit_margin_2024']:.1f}%"
            },
            "Director's Investment": {
                "Total Injections": format_currency(dla_summary.get("total_capital_injections", 0)),
                "Total Withdrawals": format_currency(dla_summary.get("total_withdrawals", 0)),
                "Net Investment": format_currency(dla_summary.get("net_director_investment", 0))
            },
            "Key Insights": {
                "Business Growth": f"Exponential revenue growth from {format_currency(revenue_2021)} (2021) to {format_currency(revenue_2024)} (2024)",
                "Profitability": "Achieved profitability with positive margins",
                "Business Model": "Shifted from Modeling Fees to Sales as primary revenue",
                "Customer Base": f"{financial_summary['key_metrics']['customer_metrics']['customer_count']} customers identified"
            },
            "Data Quality Notes": data_gaps if data_gaps else ["Data quality acceptable for analysis"]
        }
    }
    
    exec_file = os.path.join(REPORTS_PATH, "executive_summary.json")
    with open(exec_file, 'w') as f:
        json.dump(exec_summary, f, indent=2)
    
    print(f"✅ Executive summary saved to {exec_file}")
    
    # Create data for visualization
    viz_data = {
        "annual_overview": [
            {
                "year": year,
                "revenue": financial_summary["annual_performance"][year]["revenue"]["total"],
                "expenses": financial_summary["annual_performance"][year]["expenses"]["total"],
                "profit": financial_summary["annual_performance"][year]["profitability"]["gross_profit"],
                "profit_margin": financial_summary["annual_performance"][year]["profitability"]["gross_margin"]
            }
            for year in range(2020, 2026)
        ],
        "revenue_breakdown_2024": revenue_by_year.get(2024, {}).get("breakdown_by_type", {}) if isinstance(revenue_by_year.get(2024), dict) else {},
        "expense_categories_2024": expenses_by_year.get(2024, {}).get("breakdown_by_category", {}) if isinstance(expenses_by_year.get(2024), dict) else {},
        "top_customers": [
            {"name": name, "revenue": data["total_revenue"]}
            for name, data in list(revenue_data.get("customer_analysis", {}).items())[:10]
        ],
        "director_loan_trend": [
            {
                "year": year,
                "balance": financial_summary["annual_performance"][year]["directors_loan"]["closing_balance"]
            }
            for year in range(2020, 2026)
            if year in financial_summary["annual_performance"] and financial_summary["annual_performance"][year]["directors_loan"]["closing_balance"] != 0
        ]
    }
    
    viz_file = os.path.join(OUTPUT_PATH, "visualization_data.json")
    with open(viz_file, 'w') as f:
        json.dump(viz_data, f, indent=2)
    
    print(f"✅ Visualization data saved to {viz_file}")
    
    print("\n📊 Financial Summary Complete!")
    print(f"   Total Revenue: {format_currency(financial_summary['overview']['total_revenue'])}")
    print(f"   Total Expenses: {format_currency(financial_summary['overview']['total_expenses'])}")
    print(f"   Net Profit: {format_currency(financial_summary['overview']['net_profit'])}")
    print(f"   Revenue Growth: {financial_summary['overview']['revenue_growth']:.1f}%")


if __name__ == "__main__":
    main()
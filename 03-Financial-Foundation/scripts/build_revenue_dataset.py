#!/usr/bin/env python3
"""
Build comprehensive revenue dataset from all transactions (2020 onwards)
Uses the complete transaction dataset to build accurate revenue analysis
"""

import json
import os
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any

from utils import parse_xero_date, format_currency, normalize_contact_name

# Paths
DATA_PATH = "../../01-XERO-Data/data"
OUTPUT_PATH = "../data/revenue"

# Revenue account mapping
REVENUE_ACCOUNTS = {
    "200": {"name": "Sales", "category": "Operating Revenue"},
    "250": {"name": "Modeling Fees", "category": "Operating Revenue"},
    "260": {"name": "Other Revenue", "category": "Operating Revenue"},
    "270": {"name": "Interest Income", "category": "Non-Operating Revenue"},
    "280": {"name": "Realised Currency Gains", "category": "Non-Operating Revenue"}
}


def extract_pl_revenue(year: int) -> Dict[str, Any]:
    """Extract revenue data from P&L report for a specific year"""
    revenue_data = {
        "year": year,
        "total_revenue": 0,
        "revenue_by_type": {},
        "has_detail": False
    }
    
    pl_file = os.path.join(DATA_PATH, f"reports/financial/profit-loss-{year}.json")
    try:
        with open(pl_file, 'r') as f:
            pl_data = json.load(f)
        
        # Navigate through the P&L structure to find revenue section
        report = pl_data.get("report", {})
        
        for row_section in report.get("Rows", []):
            if row_section.get("RowType") == "Section" and row_section.get("Title") in ["Income", "Revenue"]:
                # Found revenue section
                for row in row_section.get("Rows", []):
                    if row.get("RowType") == "Row":
                        cells = row.get("Cells", [])
                        if len(cells) >= 2:
                            account_name = cells[0].get("Value", "")
                            # Handle string values that might have commas
                            amount_str = str(cells[1].get("Value", "0")).replace(",", "")
                            try:
                                amount = float(amount_str)
                            except:
                                amount = 0
                            
                            if account_name and amount > 0:
                                revenue_data["revenue_by_type"][account_name] = amount
                                revenue_data["total_revenue"] += amount
                                revenue_data["has_detail"] = True
        
    except Exception as e:
        print(f"Error loading P&L for {year}: {e}")
    
    return revenue_data


def extract_revenue_transactions() -> List[Dict]:
    """Extract revenue from all transactions (2020 onwards)"""
    revenue_transactions = []
    
    # Load all transactions
    try:
        with open("../data/all_transactions_2020_onwards.json", 'r') as f:
            data = json.load(f)
        
        transactions = data.get("transactions", [])
        
        for txn in transactions:
            # Check if it's a revenue transaction
            if txn.get("category") == "Revenue":
                # Parse the date to get year, month, quarter
                date_str = txn.get("date")
                if date_str:
                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    revenue_transactions.append({
                        "date": date_str,
                        "year": date_obj.year,
                        "month": date_obj.month,
                        "quarter": f"Q{(date_obj.month - 1) // 3 + 1}",
                        "id": txn.get("id"),
                        "invoice_number": txn.get("invoice_number", ""),
                        "customer": normalize_contact_name(txn.get("contact", "Unknown")),
                        "description": txn.get("description", ""),
                        "account_code": txn.get("account_code", ""),
                        "account_name": txn.get("account_name", ""),
                        "category": txn.get("sub_category", "Revenue"),
                        "amount": float(txn.get("amount", 0)),
                        "currency": txn.get("currency", "GBP"),
                        "status": txn.get("status", ""),
                        "type": txn.get("type", ""),
                        "sub_type": txn.get("sub_type", ""),
                        "source": txn.get("source", "")
                    })
    
    except Exception as e:
        print(f"Error extracting revenue transactions: {e}")
    
    return revenue_transactions


def analyze_customer_revenue(transactions: List[Dict]) -> Dict[str, Any]:
    """Analyze revenue by customer"""
    customer_analysis = defaultdict(lambda: {
        "total_revenue": 0,
        "transaction_count": 0,
        "first_transaction": None,
        "last_transaction": None,
        "revenue_by_year": defaultdict(float),
        "revenue_by_type": defaultdict(float)
    })
    
    for txn in transactions:
        customer = txn.get("customer", "Unknown")
        amount = txn.get("amount", 0)
        year = txn.get("year")
        account_name = txn.get("account_name", "")
        date = txn.get("date")
        
        customer_data = customer_analysis[customer]
        customer_data["total_revenue"] += amount
        customer_data["transaction_count"] += 1
        customer_data["revenue_by_year"][year] += amount
        customer_data["revenue_by_type"][account_name] += amount
        
        # Track first and last transaction dates
        if not customer_data["first_transaction"] or date < customer_data["first_transaction"]:
            customer_data["first_transaction"] = date
        if not customer_data["last_transaction"] or date > customer_data["last_transaction"]:
            customer_data["last_transaction"] = date
    
    # Convert to regular dict and calculate metrics
    result = {}
    for customer, data in customer_analysis.items():
        result[customer] = {
            "total_revenue": data["total_revenue"],
            "transaction_count": data["transaction_count"],
            "average_transaction": data["total_revenue"] / data["transaction_count"] if data["transaction_count"] > 0 else 0,
            "first_transaction": data["first_transaction"],
            "last_transaction": data["last_transaction"],
            "revenue_by_year": dict(data["revenue_by_year"]),
            "revenue_by_type": dict(data["revenue_by_type"])
        }
    
    return result


def main():
    """Build comprehensive revenue dataset"""
    print("Building comprehensive revenue dataset...")
    
    # Create output directory
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # 1. Extract all revenue transactions from the comprehensive dataset
    print("\n1. Extracting revenue transactions...")
    revenue_transactions = extract_revenue_transactions()
    print(f"   Found {len(revenue_transactions)} revenue transactions")
    
    # 2. Aggregate revenue by year
    revenue_by_year = defaultdict(lambda: {
        "total": 0,
        "by_type": defaultdict(float),
        "by_currency": defaultdict(float),
        "transaction_count": 0
    })
    
    for txn in revenue_transactions:
        year = txn["year"]
        amount = txn["amount"]
        account_name = txn["account_name"]
        currency = txn["currency"]
        
        revenue_by_year[year]["total"] += amount
        revenue_by_year[year]["by_type"][account_name] += amount
        revenue_by_year[year]["by_currency"][currency] += amount
        revenue_by_year[year]["transaction_count"] += 1
    
    print("\n2. Revenue by year:")
    for year in sorted(revenue_by_year.keys()):
        data = revenue_by_year[year]
        print(f"   {year}: {format_currency(data['total'])} ({data['transaction_count']} transactions)")
    
    # 3. Revenue trend analysis
    print("\n3. Revenue trend analysis:")
    years = sorted(revenue_by_year.keys())
    if len(years) >= 2:
        first_year = years[0]
        last_year = years[-1]
        first_revenue = revenue_by_year[first_year]["total"]
        last_revenue = revenue_by_year[last_year]["total"]
        
        if first_revenue > 0:
            growth_rate = ((last_revenue - first_revenue) / first_revenue) * 100
            print(f"   Growth from {first_year} to {last_year}: {growth_rate:.1f}%")
            print(f"   {first_year}: {format_currency(first_revenue)}")
            print(f"   {last_year}: {format_currency(last_revenue)}")
    
    # 4. Customer analysis
    print("\n4. Analyzing customer revenue...")
    customer_analysis = analyze_customer_revenue(revenue_transactions)
    
    # Sort customers by total revenue
    top_customers = sorted(
        customer_analysis.items(), 
        key=lambda x: x[1]["total_revenue"], 
        reverse=True
    )[:10]
    
    print("   Top 10 customers by revenue:")
    for customer, data in top_customers:
        print(f"   - {customer}: {format_currency(data['total_revenue'])} ({data['transaction_count']} transactions)")
    
    # 5. Create consolidated revenue dataset
    total_revenue = sum(data["total"] for data in revenue_by_year.values())
    years_with_data = sorted(revenue_by_year.keys())
    
    revenue_dataset = {
        "extraction_date": datetime.now().isoformat(),
        "data_period": f"{min(years_with_data)}-{max(years_with_data)}",
        "summary": {
            "total_revenue": total_revenue,
            "years_covered": len(years_with_data),
            "transaction_count": len(revenue_transactions),
            "customer_count": len(customer_analysis),
            "revenue_growth_rate": (
                ((revenue_by_year.get(2024, {}).get("total", 0) - 
                  revenue_by_year.get(2021, {}).get("total", 0)) / 
                 revenue_by_year.get(2021, {}).get("total", 1) * 100)
                if revenue_by_year.get(2021, {}).get("total", 0) > 0 else 0
            )
        },
        "revenue_by_year": {
            year: {
                "total": data["total"],
                "breakdown_by_type": dict(data["by_type"]),
                "breakdown_by_currency": dict(data["by_currency"]),
                "transaction_count": data["transaction_count"]
            }
            for year, data in revenue_by_year.items()
        },
        "customer_analysis": customer_analysis,
        "transaction_count": len(revenue_transactions),
        "detailed_transactions": revenue_transactions
    }
    
    # Save dataset
    output_file = os.path.join(OUTPUT_PATH, "revenue_dataset_2020_2025.json")
    with open(output_file, 'w') as f:
        json.dump(revenue_dataset, f, indent=2)
    
    print(f"\n✅ Revenue dataset saved to {output_file}")
    
    # Save customer analysis separately
    customer_file = os.path.join(OUTPUT_PATH, "customer_revenue_analysis.json")
    with open(customer_file, 'w') as f:
        json.dump({
            "extraction_date": datetime.now().isoformat(),
            "analysis_period": f"{min(years_with_data)}-{max(years_with_data)}",
            "customer_count": len(customer_analysis),
            "customers": customer_analysis
        }, f, indent=2)
    
    print(f"✅ Customer analysis saved to {customer_file}")
    
    # Create summary report
    summary = {
        "Revenue Summary": {
            "Period": revenue_dataset["data_period"],
            "Total Revenue": format_currency(revenue_dataset["summary"]["total_revenue"]),
            "Transaction Count": revenue_dataset["summary"]["transaction_count"],
            "Customer Count": revenue_dataset["summary"]["customer_count"],
            "Growth Rate (2021-2024)": f"{revenue_dataset['summary']['revenue_growth_rate']:.1f}%"
        },
        "Annual Revenue": {
            str(year): format_currency(data["total"])
            for year, data in revenue_dataset["revenue_by_year"].items()
        },
        "Revenue by Type": {
            account_name: sum(
                year_data["breakdown_by_type"].get(account_name, 0)
                for year_data in revenue_dataset["revenue_by_year"].values()
            )
            for account_name in REVENUE_ACCOUNTS.values()
            for account_name in [account_name["name"]]
        },
        "Data Source": {
            "Type": "Transaction-level data",
            "Coverage": "Complete coverage from 2020 onwards",
            "Sources": "Bank transactions, invoices, payments"
        }
    }
    
    summary_file = os.path.join(OUTPUT_PATH, "revenue_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"✅ Summary report saved to {summary_file}")


if __name__ == "__main__":
    main()
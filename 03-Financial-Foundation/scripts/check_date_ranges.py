#!/usr/bin/env python3
"""
Check date ranges in all data files to understand data availability
"""

import json
import os
from datetime import datetime
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils import parse_xero_date

DATA_PATH = "../../01-XERO-Data/data"

def check_file_dates(filepath, date_field="Date", name=""):
    """Check date range in a JSON file"""
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # Handle different file structures
        if isinstance(data, list):
            items = data
        elif isinstance(data, dict):
            # Try different possible keys
            for key in ["BankTransactions", "Invoices", "manualJournals", "Payments"]:
                if key in data:
                    items = data[key]
                    break
            else:
                print(f"  {name}: Unknown structure")
                return
        else:
            print(f"  {name}: Unexpected type")
            return
        
        dates = []
        for item in items:
            date_str = item.get(date_field) or item.get("DateString")
            if date_str:
                parsed = parse_xero_date(date_str)
                if parsed:
                    dates.append(parsed)
        
        if dates:
            min_date = min(dates)
            max_date = max(dates)
            print(f"  {name}: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')} ({len(dates)} records)")
        else:
            print(f"  {name}: No valid dates found")
            
    except Exception as e:
        print(f"  {name}: Error - {e}")

def main():
    print("Checking date ranges in all data files...\n")
    
    print("Core Data:")
    check_file_dates(os.path.join(DATA_PATH, "core/bank_transactions.json"), name="Bank Transactions")
    check_file_dates(os.path.join(DATA_PATH, "core/invoices.json"), name="Core Invoices")
    check_file_dates(os.path.join(DATA_PATH, "../invoices-all.json"), name="All Invoices")
    
    print("\nSupplementary Data:")
    check_file_dates(os.path.join(DATA_PATH, "supplementary/payments/payments.json"), name="Payments")
    check_file_dates(os.path.join(DATA_PATH, "supplementary/misc/manual-journals.json"), name="Manual Journals")
    check_file_dates(os.path.join(DATA_PATH, "supplementary/misc/credit-notes.json"), name="Credit Notes")
    
    print("\nFinancial Reports:")
    # Check if we have historical P&L data
    for year in range(2020, 2026):
        filepath = os.path.join(DATA_PATH, f"reports/financial/profit-loss-{year}.json")
        if os.path.exists(filepath):
            print(f"  P&L {year}: Available")
        else:
            print(f"  P&L {year}: Not found")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Build comprehensive expense dataset from all transactions (2020 onwards)
Uses the complete transaction dataset to build accurate expense analysis
"""

import json
import os
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any

from utils import parse_xero_date, format_currency, normalize_contact_name, load_chart_of_accounts

# Paths
DATA_PATH = "../../01-XERO-Data/data"
OUTPUT_PATH = "../data/expenses"

# Major expense categories based on chart of accounts
EXPENSE_CATEGORIES = {
    # Direct Costs
    "310": {"name": "General Expenses", "category": "Direct Costs"},
    "320": {"name": "Advertising & Marketing", "category": "Direct Costs"},
    "325": {"name": "Freight & Courier", "category": "Direct Costs"},
    
    # Administrative Expenses
    "400": {"name": "Advertising & Marketing", "category": "Administrative"},
    "401": {"name": "Bank Fees", "category": "Administrative"},
    "402": {"name": "Bank Service Charges", "category": "Administrative"},
    "404": {"name": "Cleaning", "category": "Administrative"},
    "405": {"name": "Consulting & Accounting", "category": "Administrative"},
    "408": {"name": "Depreciation", "category": "Administrative"},
    "412": {"name": "Entertainment", "category": "Administrative"},
    "420": {"name": "Insurance", "category": "Administrative"},
    "429": {"name": "General Expenses", "category": "Administrative"},
    "433": {"name": "Office Expenses", "category": "Administrative"},
    "437": {"name": "Postage", "category": "Administrative"},
    "441": {"name": "Rent", "category": "Administrative"},
    "445": {"name": "Repairs and Maintenance", "category": "Administrative"},
    "449": {"name": "Subscriptions", "category": "Administrative"},
    "453": {"name": "Telephone & Internet", "category": "Administrative"},
    "461": {"name": "Travel - National", "category": "Administrative"},
    "463": {"name": "Travel - International", "category": "Administrative"},
    "469": {"name": "Transport - Personal Vehicles", "category": "Administrative"},
    "470": {"name": "Transport - Company Vehicles", "category": "Administrative"},
    "473": {"name": "Computer Expenses", "category": "Administrative"},
    "474": {"name": "Credit Card Interest", "category": "Administrative"},
    "475": {"name": "Interest Expense", "category": "Administrative"},
    "477": {"name": "Directors' Remuneration", "category": "Administrative"},
    "478": {"name": "Wages and Salaries", "category": "Administrative"},
    "485": {"name": "Employment Costs", "category": "Administrative"},
    "493": {"name": "Company Formation Expenses", "category": "Administrative"},
    "495": {"name": "Fines", "category": "Administrative"},
    "499": {"name": "Website Costs", "category": "Administrative"},
    
    # Financial Costs
    "290": {"name": "Currency Losses", "category": "Financial"},
    "300": {"name": "Bank charges", "category": "Financial"}
}


def extract_expense_transactions() -> List[Dict]:
    """Extract expense transactions from all transactions (2020 onwards)"""
    expense_transactions = []
    
    # Load all transactions
    try:
        with open("../data/all_transactions_2020_onwards.json", 'r') as f:
            data = json.load(f)
        
        transactions = data.get("transactions", [])
        
        for txn in transactions:
            # Check if it's an expense transaction
            if txn.get("category") == "Expense":
                # Parse the date to get year, month, quarter
                date_str = txn.get("date")
                if date_str:
                    date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    
                    # Get expense category info
                    account_code = txn.get("account_code", "")
                    expense_info = EXPENSE_CATEGORIES.get(account_code, {
                        "name": txn.get("account_name", "Other Expenses"),
                        "category": txn.get("sub_category", "Other")
                    })
                    
                    expense_transactions.append({
                        "date": date_str,
                        "year": date_obj.year,
                        "month": date_obj.month,
                        "quarter": f"Q{(date_obj.month - 1) // 3 + 1}",
                        "id": txn.get("id"),
                        "invoice_number": txn.get("invoice_number", ""),
                        "supplier": normalize_contact_name(txn.get("contact", "Unknown")),
                        "description": txn.get("description", ""),
                        "account_code": account_code,
                        "account_name": expense_info.get("name", ""),
                        "category": expense_info.get("category", "Other"),
                        "amount": abs(float(txn.get("amount", 0))),  # Ensure positive for expenses
                        "currency": txn.get("currency", "GBP"),
                        "status": txn.get("status", ""),
                        "reference": txn.get("reference", ""),
                        "type": txn.get("type", ""),
                        "sub_type": txn.get("sub_type", ""),
                        "source": txn.get("source", "")
                    })
    
    except Exception as e:
        print(f"Error extracting expense transactions: {e}")
    
    return expense_transactions


def analyze_supplier_expenses(transactions: List[Dict]) -> Dict[str, Any]:
    """Analyze expenses by supplier"""
    supplier_analysis = defaultdict(lambda: {
        "total_spend": 0,
        "transaction_count": 0,
        "first_transaction": None,
        "last_transaction": None,
        "spend_by_year": defaultdict(float),
        "spend_by_category": defaultdict(float)
    })
    
    for txn in transactions:
        supplier = txn.get("supplier", "Unknown")
        amount = txn.get("amount", 0)
        year = txn.get("year")
        category = txn.get("category", "")
        date = txn.get("date")
        
        supplier_data = supplier_analysis[supplier]
        supplier_data["total_spend"] += amount
        supplier_data["transaction_count"] += 1
        supplier_data["spend_by_year"][year] += amount
        supplier_data["spend_by_category"][category] += amount
        
        # Track first and last transaction dates
        if not supplier_data["first_transaction"] or date < supplier_data["first_transaction"]:
            supplier_data["first_transaction"] = date
        if not supplier_data["last_transaction"] or date > supplier_data["last_transaction"]:
            supplier_data["last_transaction"] = date
    
    # Convert to regular dict and calculate metrics
    result = {}
    for supplier, data in supplier_analysis.items():
        result[supplier] = {
            "total_spend": data["total_spend"],
            "transaction_count": data["transaction_count"],
            "average_transaction": data["total_spend"] / data["transaction_count"] if data["transaction_count"] > 0 else 0,
            "first_transaction": data["first_transaction"],
            "last_transaction": data["last_transaction"],
            "spend_by_year": dict(data["spend_by_year"]),
            "spend_by_category": dict(data["spend_by_category"])
        }
    
    return result


def main():
    """Build comprehensive expense dataset"""
    print("Building comprehensive expense dataset...")
    
    # Create output directory
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # 1. Extract all expense transactions
    print("\n1. Extracting expense transactions...")
    expense_transactions = extract_expense_transactions()
    print(f"   Found {len(expense_transactions)} expense transactions")
    
    # 2. Aggregate expenses by year
    expenses_by_year = defaultdict(lambda: {
        "total": 0,
        "by_category": defaultdict(float),
        "by_currency": defaultdict(float),
        "transaction_count": 0
    })
    
    for txn in expense_transactions:
        year = txn["year"]
        amount = txn["amount"]
        category = txn["category"]
        currency = txn["currency"]
        
        expenses_by_year[year]["total"] += amount
        expenses_by_year[year]["by_category"][category] += amount
        expenses_by_year[year]["by_currency"][currency] += amount
        expenses_by_year[year]["transaction_count"] += 1
    
    print("\n2. Expenses by year:")
    for year in sorted(expenses_by_year.keys()):
        data = expenses_by_year[year]
        print(f"   {year}: {format_currency(data['total'])} ({data['transaction_count']} transactions)")
    
    # 3. Category analysis
    print("\n3. Expense category analysis:")
    total_by_category = defaultdict(float)
    for txn in expense_transactions:
        total_by_category[txn["category"]] += txn["amount"]
    
    # Sort categories by total spend
    sorted_categories = sorted(total_by_category.items(), key=lambda x: x[1], reverse=True)
    for category, total in sorted_categories[:5]:
        print(f"   {category}: {format_currency(total)}")
    
    # 4. Supplier analysis
    print("\n4. Analyzing supplier expenses...")
    supplier_analysis = analyze_supplier_expenses(expense_transactions)
    
    # Sort suppliers by total spend
    top_suppliers = sorted(
        supplier_analysis.items(), 
        key=lambda x: x[1]["total_spend"], 
        reverse=True
    )[:10]
    
    print("   Top 10 suppliers by spend:")
    for supplier, data in top_suppliers:
        print(f"   - {supplier}: {format_currency(data['total_spend'])} ({data['transaction_count']} transactions)")
    
    # 5. Create consolidated expense dataset
    total_expenses = sum(data["total"] for data in expenses_by_year.values())
    years_with_data = sorted(expenses_by_year.keys())
    
    expense_dataset = {
        "extraction_date": datetime.now().isoformat(),
        "data_period": f"{min(years_with_data)}-{max(years_with_data)}",
        "summary": {
            "total_expenses": total_expenses,
            "years_covered": len(years_with_data),
            "transaction_count": len(expense_transactions),
            "supplier_count": len(supplier_analysis),
            "expense_growth_rate": (
                ((expenses_by_year.get(2024, {}).get("total", 0) - 
                  expenses_by_year.get(2021, {}).get("total", 0)) / 
                 expenses_by_year.get(2021, {}).get("total", 1) * 100)
                if expenses_by_year.get(2021, {}).get("total", 0) > 0 else 0
            )
        },
        "expenses_by_year": {
            year: {
                "total": data["total"],
                "breakdown_by_category": dict(data["by_category"]),
                "breakdown_by_currency": dict(data["by_currency"]),
                "transaction_count": data["transaction_count"]
            }
            for year, data in expenses_by_year.items()
        },
        "expenses_by_category": dict(total_by_category),
        "supplier_analysis": supplier_analysis,
        "transaction_count": len(expense_transactions),
        "detailed_transactions": expense_transactions
    }
    
    # Save dataset
    output_file = os.path.join(OUTPUT_PATH, "expense_dataset_2020_2025.json")
    with open(output_file, 'w') as f:
        json.dump(expense_dataset, f, indent=2)
    
    print(f"\n✅ Expense dataset saved to {output_file}")
    
    # Save supplier analysis separately
    supplier_file = os.path.join(OUTPUT_PATH, "supplier_expense_analysis.json")
    with open(supplier_file, 'w') as f:
        json.dump({
            "extraction_date": datetime.now().isoformat(),
            "analysis_period": f"{min(years_with_data)}-{max(years_with_data)}",
            "supplier_count": len(supplier_analysis),
            "suppliers": supplier_analysis
        }, f, indent=2)
    
    print(f"✅ Supplier analysis saved to {supplier_file}")
    
    # Create summary report
    summary = {
        "Expense Summary": {
            "Period": expense_dataset["data_period"],
            "Total Expenses": format_currency(expense_dataset["summary"]["total_expenses"]),
            "Transaction Count": expense_dataset["summary"]["transaction_count"],
            "Supplier Count": expense_dataset["summary"]["supplier_count"],
            "Growth Rate (2021-2024)": f"{expense_dataset['summary']['expense_growth_rate']:.1f}%"
        },
        "Annual Expenses": {
            str(year): format_currency(data["total"])
            for year, data in expense_dataset["expenses_by_year"].items()
        },
        "Top Categories": {
            category: format_currency(amount)
            for category, amount in sorted_categories[:5]
        },
        "Data Source": {
            "Type": "Transaction-level data",
            "Coverage": "Complete coverage from 2020 onwards",
            "Sources": "Bank transactions, invoices, payments"
        }
    }
    
    summary_file = os.path.join(OUTPUT_PATH, "expense_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"✅ Summary report saved to {summary_file}")


if __name__ == "__main__":
    main()
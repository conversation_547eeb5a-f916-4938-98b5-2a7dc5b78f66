{"extraction_date": "2025-07-29T07:40:04.011386", "transaction_count": 141, "transactions": [{"date": "2020-08-04T03:00:00", "year": 2020, "id": "ea00cfd0-ccf3-4fc9-9fc0-f688eb18f724", "type": "Capital Injection", "amount": 2500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "Capital Injection", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 2500.0}, {"date": "2020-09-24T03:00:00", "year": 2020, "id": "b7cc6459-2155-4929-a92a-da64570b73be", "type": "Capital Injection", "amount": 10000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "Capital Injection", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 12500.0}, {"date": "2020-12-18T02:00:00", "year": 2020, "id": "88c33ff0-3109-4b8d-a4f8-e555a1dd8b76", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "Capital Injection", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 18500.0}, {"date": "2020-12-28T02:00:00", "year": 2020, "id": "1329b0c7-171e-4151-bc04-0591f548ba5c", "type": "Withdrawal/Repayment", "amount": -1159.15, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "To Bilal Itani - Bilal Itani", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 17340.85}, {"date": "2020-12-28T02:00:00", "year": 2020, "id": "062d13c9-e6b1-4492-bda6-64a0703455af", "type": "Withdrawal/Repayment", "amount": -1000.0, "currency": "USD", "contact": "Bilal itani", "reference": "", "description": "To Bilal Itani - Bilal Itani", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 16340.************}, {"date": "2021-03-05T02:00:00", "year": 2021, "id": "97c96362-7cf3-446c-b5e6-2fcd798615d9", "type": "Capital Injection", "amount": 355.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 16695.85}, {"date": "2021-03-19T02:00:00", "year": 2021, "id": "9e764ef6-79be-4fc8-8fad-8c67de617069", "type": "Capital Injection", "amount": 1723.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 18418.85}, {"date": "2021-03-31T03:00:00", "year": 2021, "id": "6cb1d3ee-d878-4a60-8ef4-6e269e8c2332", "type": "Capital Injection", "amount": 2154.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 20572.85}, {"date": "2021-04-06T03:00:00", "year": 2021, "id": "698a5210-3aa9-4de8-8de2-24fcd2e0189c", "type": "Capital Injection", "amount": 710.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 21282.85}, {"date": "2021-05-10T03:00:00", "year": 2021, "id": "********-c5a6-4754-a401-32f8b99836d0", "type": "Capital Injection", "amount": 1985.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 23267.85}, {"date": "2021-05-15T03:00:00", "year": 2021, "id": "1f39ec79-a519-409b-a296-d252aa4d889e", "type": "Capital Injection", "amount": 1000.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 24267.85}, {"date": "2021-05-25T03:00:00", "year": 2021, "id": "bdfb99b8-7ab1-413c-b198-2909c7af88a9", "type": "Capital Injection", "amount": 75.0, "currency": "GBP", "contact": "Bilal itani", "reference": "DIRECTORS LOAN", "description": "DIRECTORS LOAN", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 24342.85}, {"date": "2021-07-09T03:00:00", "year": 2021, "id": "a6699ad7-db29-4c13-85d1-16fa81120c5c", "type": "Capital Injection", "amount": 6700.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 31042.85}, {"date": "2021-07-09T03:00:00", "year": 2021, "id": "3700fcb5-4d2b-4a6f-a6cf-05bf92717d70", "type": "Capital Injection", "amount": 6700.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 37742.85}, {"date": "2021-07-09T03:00:00", "year": 2021, "id": "2f27db10-6ef3-4b41-8fb1-46608171c5ea", "type": "Capital Injection", "amount": 6700.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 44442.85}, {"date": "2021-08-12T03:00:00", "year": 2021, "id": "0e3c39e8-fa2d-4377-8338-72fb4ed427b9", "type": "Capital Injection", "amount": 890.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 45332.85}, {"date": "2021-08-12T03:00:00", "year": 2021, "id": "44cacb72-3723-430f-802e-4a81d9318c20", "type": "Capital Injection", "amount": 4484.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 49816.85}, {"date": "2021-08-12T03:00:00", "year": 2021, "id": "fd7778c1-35b0-44f6-a99b-cff902197ca8", "type": "Capital Injection", "amount": 890.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 50706.85}, {"date": "2021-08-12T03:00:00", "year": 2021, "id": "3a515839-ef2c-43d1-bc0c-11f01c83eb6f", "type": "Capital Injection", "amount": 4484.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 55190.85}, {"date": "2021-09-04T03:00:00", "year": 2021, "id": "972cf461-dca0-435b-bbb7-8f26c0209ace", "type": "Capital Injection", "amount": 5.8, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 55196.65}, {"date": "2021-09-04T03:00:00", "year": 2021, "id": "78c9480a-56f1-421d-bded-ce6a53f4afe9", "type": "Capital Injection", "amount": 5.8, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 55202.************}, {"date": "2021-09-10T03:00:00", "year": 2021, "id": "********-f3a6-40df-a9fc-583c553e1d11", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 60202.************}, {"date": "2021-09-10T03:00:00", "year": 2021, "id": "e51219ce-c6aa-4690-8f43-d37e71a9b6a8", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 65202.************}, {"date": "2021-11-08T02:00:00", "year": 2021, "id": "d8b4fd80-6480-437c-8383-80dd682ecac0", "type": "Capital Injection", "amount": 7500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 72702.4**********}, {"date": "2021-11-08T02:00:00", "year": 2021, "id": "8131c89e-0680-45f0-8871-4e0e551235f5", "type": "Capital Injection", "amount": 7500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 80202.4**********}, {"date": "2021-12-02T02:00:00", "year": 2021, "id": "c4247937-e451-45e1-bc5d-e5f2b471fa95", "type": "Capital Injection", "amount": 355.0, "currency": "GBP", "contact": "Bilal itani", "reference": "Directors Loan", "description": "Directors Loan", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 80557.4**********}, {"date": "2021-12-10T02:00:00", "year": 2021, "id": "b82b26a6-b140-4913-958c-cf23269d4f20", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 86557.4**********}, {"date": "2021-12-10T02:00:00", "year": 2021, "id": "5120a5c8-1eca-4c76-9296-94f9ccf412ac", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 92557.4**********}, {"date": "2022-01-14T02:00:00", "year": 2022, "id": "b1ce1995-249d-4f3d-82f2-78068d24c2b4", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 98557.4**********}, {"date": "2022-01-14T02:00:00", "year": 2022, "id": "742f8050-44f2-44ec-a68a-b7955a4aa3fb", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 104557.4**********}, {"date": "2022-02-08T02:00:00", "year": 2022, "id": "e70f6f27-3b43-4687-8c9f-188f64d4d2ae", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 113557.4**********}, {"date": "2022-02-08T02:00:00", "year": 2022, "id": "da25bc3c-c683-4621-afc9-259058e6ed97", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 122557.4**********}, {"date": "2022-03-10T02:00:00", "year": 2022, "id": "350816ba-e1d4-4bde-973e-25895730e382", "type": "Capital Injection", "amount": 7000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 129557.4**********}, {"date": "2022-03-10T02:00:00", "year": 2022, "id": "202f6c80-4232-4ee0-a629-63948103a59e", "type": "Capital Injection", "amount": 7000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 136557.45}, {"date": "2022-04-08T03:00:00", "year": 2022, "id": "188cdc4a-5134-473c-8762-87db8d737171", "type": "Capital Injection", "amount": 8000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 144557.45}, {"date": "2022-04-09T03:00:00", "year": 2022, "id": "322c27d5-0b09-498b-be9c-14171161b56f", "type": "Withdrawal/Repayment", "amount": -168.77, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 144388.***********}, {"date": "2022-04-21T03:00:00", "year": 2022, "id": "6c07f248-aca5-4006-a196-6188559c51a8", "type": "Withdrawal/Repayment", "amount": -300.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 144088.***********}, {"date": "2022-05-01T03:00:00", "year": 2022, "id": "7101f39e-cf04-400d-b628-93ec88e15d1f", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 153088.***********}, {"date": "2022-05-17T03:00:00", "year": 2022, "id": "786cab0c-4afb-4e1f-9e0d-76849480a75a", "type": "Capital Injection", "amount": 2300.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 155388.***********}, {"date": "2022-06-01T03:00:00", "year": 2022, "id": "4c1699f8-5817-46ee-9041-1d4ca0cf1162", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 160388.***********}, {"date": "2022-06-02T03:00:00", "year": 2022, "id": "0409597c-90bc-4df5-9da6-97ec03a4efd9", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 165388.***********}, {"date": "2022-06-04T03:00:00", "year": 2022, "id": "a7d9d28a-1394-4f2f-b97f-f84a6bc958e8", "type": "Withdrawal/Repayment", "amount": -173.73, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 165214.95}, {"date": "2022-06-09T03:00:00", "year": 2022, "id": "55a0ec55-8390-4f2c-8694-4b5a4bb97ced", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 170214.95}, {"date": "2022-06-09T03:00:00", "year": 2022, "id": "b5125646-e32a-4684-a7fb-b2596bbb37a1", "type": "Withdrawal/Repayment", "amount": -281.45, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 169933.5}, {"date": "2022-06-16T03:00:00", "year": 2022, "id": "b1ad42b0-6074-4849-9842-e7f8c4c8711e", "type": "Withdrawal/Repayment", "amount": -133.45, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 169800.05}, {"date": "2022-06-23T03:00:00", "year": 2022, "id": "********-3c1b-4d2a-a0a7-442b7fb208a2", "type": "Capital Injection", "amount": 4000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 173800.05}, {"date": "2022-07-05T03:00:00", "year": 2022, "id": "e3413352-f65d-44a0-a592-fe41fe49192a", "type": "Withdrawal/Repayment", "amount": -869.06, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 172930.99}, {"date": "2022-07-05T03:00:00", "year": 2022, "id": "005b69fc-d14e-4267-8f2c-e6d652e7c45f", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 177930.99}, {"date": "2022-07-09T03:00:00", "year": 2022, "id": "56e95ab4-0ccb-42d2-8511-cb207c0d2044", "type": "Capital Injection", "amount": 300.0, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 178230.99}, {"date": "2022-07-09T03:00:00", "year": 2022, "id": "455bec78-c990-4219-9c00-003975ff4fc0", "type": "Withdrawal/Repayment", "amount": -274.29, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 177956.***********}, {"date": "2022-07-15T03:00:00", "year": 2022, "id": "5a1917ff-9288-431d-839e-bfa500ce1662", "type": "Withdrawal/Repayment", "amount": -278.3, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 177678.4}, {"date": "2022-07-21T03:00:00", "year": 2022, "id": "b0005a10-18fd-48c6-8971-edbed4819f91", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal Itan", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 183678.4}, {"date": "2022-07-28T03:00:00", "year": 2022, "id": "abab75e8-283d-4ccb-b1a6-9006a03dcf6d", "type": "Capital Injection", "amount": 1500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 185178.4}, {"date": "2022-08-12T03:00:00", "year": 2022, "id": "76abf6eb-d7ee-43c1-af52-b5ee92dfeb8b", "type": "Capital Injection", "amount": 312.15, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 185490.55}, {"date": "2022-08-12T03:00:00", "year": 2022, "id": "9ddcfcd7-6810-4a6f-a441-8679151b4a2e", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 191490.55}, {"date": "2022-08-22T03:00:00", "year": 2022, "id": "077e1e51-138a-4d49-9844-3eb0016740ee", "type": "Capital Injection", "amount": 8000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 199490.55}, {"date": "2022-09-06T03:00:00", "year": 2022, "id": "a55ae937-d21d-46b7-a9f5-1aee26252eb8", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 208490.55}, {"date": "2022-09-09T03:00:00", "year": 2022, "id": "bcae1e41-5fcb-489c-b10f-bda84f893d6f", "type": "Withdrawal/Repayment", "amount": -331.58, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 208158.97}, {"date": "2022-09-14T03:00:00", "year": 2022, "id": "d828e0fd-b0f9-4be1-a938-4cab6db062b5", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 217158.97}, {"date": "2022-09-14T03:00:00", "year": 2022, "id": "e2b3a035-8d4b-420b-a32c-a73048a15471", "type": "Withdrawal/Repayment", "amount": -400.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 216758.97}, {"date": "2022-09-17T03:00:00", "year": 2022, "id": "4ef3826e-2e53-429a-9965-2d6d6e12309c", "type": "Withdrawal/Repayment", "amount": -110.0, "currency": "USD", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 216648.97}, {"date": "2022-09-28T03:00:00", "year": 2022, "id": "********-2558-4687-9c73-6fd435b9b488", "type": "Withdrawal/Repayment", "amount": -92.6, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 216556.37}, {"date": "2022-09-28T03:00:00", "year": 2022, "id": "e32d7b96-a1a0-4b7f-b8e4-2c365afa4141", "type": "Capital Injection", "amount": 100.0, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 216656.37}, {"date": "2022-09-28T03:00:00", "year": 2022, "id": "80383f12-0332-4baf-bfa5-a2b51bc88891", "type": "Capital Injection", "amount": 2500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 219156.37}, {"date": "2022-10-06T03:00:00", "year": 2022, "id": "3ae3ea28-77ab-4e69-8db2-d10c3f5361a6", "type": "Withdrawal/Repayment", "amount": -268.01, "currency": "GBP", "contact": "Extra exchange", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 218888.36}, {"date": "2022-10-06T03:00:00", "year": 2022, "id": "71977a9c-4e7d-4b6b-bdfc-00e37cf81178", "type": "Capital Injection", "amount": 12000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 230888.36}, {"date": "2022-10-06T03:00:00", "year": 2022, "id": "125f6266-d8ff-48ca-b3bd-d1caefabec6d", "type": "Withdrawal/Repayment", "amount": -507.8, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 230380.56}, {"date": "2022-10-12T03:00:00", "year": 2022, "id": "aa1cb6b5-3614-45be-90ce-2a79f1322a07", "type": "Withdrawal/Repayment", "amount": -315.79, "currency": "USD", "contact": "WHISH Money", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 230064.77}, {"date": "2022-10-15T03:00:00", "year": 2022, "id": "627db58e-713a-48e4-921e-1bfae7cfcd61", "type": "Withdrawal/Repayment", "amount": -197.75, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "Money transfer", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 229867.02}, {"date": "2022-10-20T03:00:00", "year": 2022, "id": "f7139a03-816c-478b-b9b0-611da840c166", "type": "Withdrawal/Repayment", "amount": -4594.18, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 225272.84}, {"date": "2022-10-20T03:00:00", "year": 2022, "id": "08082be4-25f6-4791-a0e6-c4b5bbd5177a", "type": "Capital Injection", "amount": 15000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 240272.84}, {"date": "2022-11-02T02:00:00", "year": 2022, "id": "550d00b1-5343-4360-81d2-7ac686cd9d66", "type": "Capital Injection", "amount": 6000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 246272.84}, {"date": "2022-11-10T02:00:00", "year": 2022, "id": "ea30c0e7-8ad5-42bc-a781-f4a6bc66bbec", "type": "Withdrawal/Repayment", "amount": -155.5, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 246117.34}, {"date": "2022-11-11T02:00:00", "year": 2022, "id": "5b4f34cc-e13e-490e-bdc6-ed11370f653f", "type": "Capital Injection", "amount": 4000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 250117.34}, {"date": "2022-11-14T02:00:00", "year": 2022, "id": "4f2e8eae-93fb-4ced-8a4c-e74946b9ed24", "type": "Withdrawal/Repayment", "amount": -1022.11, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 249095.23}, {"date": "2022-11-18T02:00:00", "year": 2022, "id": "cc1e80f8-5014-4af2-bdc9-ae0e0b8ce4e3", "type": "Withdrawal/Repayment", "amount": -583.11, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 248512.***********}, {"date": "2022-11-18T02:00:00", "year": 2022, "id": "ec56e587-7c72-4a32-893a-fdd0c34a3489", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "TRANSFER-*********", "description": "Received money from Graf GmbH Draht-und Bandstahlverformung with reference Zahlung", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 253512.***********}, {"date": "2022-11-25T02:00:00", "year": 2022, "id": "5d355aa1-9213-4a98-a53b-2e904366f37a", "type": "Withdrawal/Repayment", "amount": -109.32, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "Money transfer", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 253402.***********}, {"date": "2022-11-25T02:00:00", "year": 2022, "id": "824d86f1-f649-40b3-b30c-6040697c8bd7", "type": "Capital Injection", "amount": 5500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 258902.***********}, {"date": "2022-12-07T02:00:00", "year": 2022, "id": "eaf4b07a-ad58-410e-9ff4-e2888718dbb4", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "TRANSFER-*********", "description": "Received money from IKA-Fenster GmbH with reference -Zahlung-", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 263902.***********}, {"date": "2022-12-21T02:00:00", "year": 2022, "id": "41dcc802-ca36-4724-9b11-2942d29d2520", "type": "Capital Injection", "amount": 9000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 272902.***********}, {"date": "2022-12-21T02:00:00", "year": 2022, "id": "2b7c1123-ab6a-41de-8614-8fd91aeda272", "type": "Withdrawal/Repayment", "amount": -204.57, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "Money transfer", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 272698.***********}, {"date": "2022-12-21T02:00:00", "year": 2022, "id": "9f0c69c0-2b64-4dc9-a914-0c43e892611a", "type": "Withdrawal/Repayment", "amount": -565.4, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 272132.83}, {"date": "2023-01-11T02:00:00", "year": 2023, "id": "021b75e0-4c59-4876-a1c7-243bddcdb82b", "type": "Withdrawal/Repayment", "amount": -176.23, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 271956.***********}, {"date": "2023-01-13T02:00:00", "year": 2023, "id": "a07a3bb7-07ec-4f2b-b225-842561ee3802", "type": "Withdrawal/Repayment", "amount": -103.57, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 271853.03}, {"date": "2023-01-16T02:00:00", "year": 2023, "id": "d1ef9c41-b01b-47a1-bfb4-d5bbf76d3d96", "type": "Withdrawal/Repayment", "amount": -98.31, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 271754.***********}, {"date": "2023-01-26T02:00:00", "year": 2023, "id": "b163cb08-863c-40e5-9d49-ddcff8ae7e7f", "type": "Withdrawal/Repayment", "amount": -187.33, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 271567.39}, {"date": "2023-01-29T02:00:00", "year": 2023, "id": "0bfcd958-2a2e-48cf-9bd9-c7473fdc09cd", "type": "Capital Injection", "amount": 5000.0, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 276567.39}, {"date": "2023-01-29T02:00:00", "year": 2023, "id": "642af8a4-4fac-4b8a-8e6b-600e1aefb74b", "type": "Withdrawal/Repayment", "amount": -242.11, "currency": "USD", "contact": "WHISH Money", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 276325.28}, {"date": "2023-02-01T02:00:00", "year": 2023, "id": "178377f4-25f3-4c98-b4d9-bdbbfc17d4ff", "type": "Withdrawal/Repayment", "amount": -486.62, "currency": "GBP", "contact": "MAZEN AKKAOUI", "reference": "", "description": "Sent money to MAZEN AKKAOUI (USD 600.00 at 1.23300 rate)", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 275838.***********}, {"date": "2023-02-03T02:00:00", "year": 2023, "id": "4bc06c4b-1f61-4d21-9f5a-916a3a1fea04", "type": "Withdrawal/Repayment", "amount": -301.29, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 275537.***********}, {"date": "2023-02-03T02:00:00", "year": 2023, "id": "74b83775-a3ae-4a81-af2b-ddbee32a45d2", "type": "Withdrawal/Repayment", "amount": -3.47, "currency": "USD", "contact": "WHISH Money", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 275533.**********}, {"date": "2023-02-03T02:00:00", "year": 2023, "id": "35be0b48-c520-4c5e-bbea-59626ccca124", "type": "Withdrawal/Repayment", "amount": -0.23, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 275533.**********}, {"date": "2023-02-07T02:00:00", "year": 2023, "id": "5290b958-9b63-4240-8ab8-a0f626c9224c", "type": "Withdrawal/Repayment", "amount": -271.55, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 275262.**********}, {"date": "2023-02-09T02:00:00", "year": 2023, "id": "6422bb67-2bdc-47b7-a5fd-47944bb80c9b", "type": "Withdrawal/Repayment", "amount": -432.79, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 274829.***********}, {"date": "2023-02-20T02:00:00", "year": 2023, "id": "824b2b03-5d6a-4985-ba5a-37ad18de8a12", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 279829.***********}, {"date": "2023-02-24T02:00:00", "year": 2023, "id": "ba7de752-6774-476f-bc55-9897fb1b2e1e", "type": "Withdrawal/Repayment", "amount": -61.28, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 279768.**********}, {"date": "2023-03-02T02:00:00", "year": 2023, "id": "7a659695-63de-418c-939d-c0f1899312cf", "type": "Withdrawal/Repayment", "amount": -357.62, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 279410.**********}, {"date": "2023-03-02T02:00:00", "year": 2023, "id": "4d4e7b0d-3d7c-40a8-a2af-14d76e28786f", "type": "Capital Injection", "amount": 5200.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 284610.**********}, {"date": "2023-03-03T02:00:00", "year": 2023, "id": "899a3602-c042-4aa0-b8e9-b4bf23407704", "type": "Withdrawal/Repayment", "amount": -59.56, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 284550.**********}, {"date": "2023-03-13T02:00:00", "year": 2023, "id": "fcd234ed-b685-426c-90c9-43623d101dc0", "type": "Withdrawal/Repayment", "amount": -86.79, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "Money transfer", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 284464.***********}, {"date": "2023-03-13T02:00:00", "year": 2023, "id": "75d8a1a8-2524-4b8b-8cc6-a1f75918dca7", "type": "Withdrawal/Repayment", "amount": -0.3, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 284463.***********}, {"date": "2023-04-04T03:00:00", "year": 2023, "id": "976e2e14-4cde-43c6-8014-2f8ee372f9d0", "type": "Capital Injection", "amount": 5000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 289463.***********}, {"date": "2023-04-04T03:00:00", "year": 2023, "id": "fe6bab01-75cb-4970-a81d-a6b7a6b2d4d4", "type": "Withdrawal/Repayment", "amount": -335.88, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 289127.***********}, {"date": "2023-05-15T03:00:00", "year": 2023, "id": "e4d0b18b-9424-4938-a767-41ee63be9c1e", "type": "Withdrawal/Repayment", "amount": -116.81, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 289011.***********}, {"date": "2023-05-15T03:00:00", "year": 2023, "id": "49e607ad-a603-4470-aabb-2a6daabfa600", "type": "Withdrawal/Repayment", "amount": -138.08, "currency": "USD", "contact": "WHISH Money", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 288873.**********}, {"date": "2023-05-26T03:00:00", "year": 2023, "id": "40a7c38d-d45f-4901-bf1d-3aa140eb1291", "type": "Capital Injection", "amount": 3500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 292373.**********}, {"date": "2023-05-27T03:00:00", "year": 2023, "id": "199a9a6b-72d1-49b8-8508-63e8e1be1816", "type": "Withdrawal/Repayment", "amount": -343.49, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 292029.***********}, {"date": "2023-06-16T03:00:00", "year": 2023, "id": "7c5bf7cd-bdd0-4e88-8fba-824d03c36058", "type": "Withdrawal/Repayment", "amount": -1096.39, "currency": "EUR", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 290933.**********}, {"date": "2023-06-16T03:00:00", "year": 2023, "id": "87775b33-39fa-4bde-868c-44025e229c35", "type": "Capital Injection", "amount": 20000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 310933.**********}, {"date": "2023-08-03T03:00:00", "year": 2023, "id": "99713b75-e315-48db-8058-56de0d9f8f67", "type": "Withdrawal/Repayment", "amount": -631.21, "currency": "GBP", "contact": "MAZEN AKKAOUI", "reference": "", "description": "Sent money to MAZEN AKKAOUI (USD 600.00 at 1.23300 rate)", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 310301.**********}, {"date": "2023-08-07T03:00:00", "year": 2023, "id": "a08c8fe2-6e29-4a6c-9f4e-ee607249e809", "type": "Withdrawal/Repayment", "amount": -300.0, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 310001.**********}, {"date": "2023-08-15T03:00:00", "year": 2023, "id": "4c686e68-76d8-489f-a1c5-e7670d39cf02", "type": "Withdrawal/Repayment", "amount": -71.42, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "Card transaction of JOD issued by Carrefour Jordan AMMAN (JOD 50.640 at 0.70900 rate)", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309930.**********}, {"date": "2023-08-17T03:00:00", "year": 2023, "id": "e3e7364f-ffc3-407d-94c3-916d47e5706a", "type": "Capital Injection", "amount": 2.19, "currency": "GBP", "contact": "<PERSON>", "reference": "CARD-*********", "description": "Card transaction of -. JOD issued by Carrefour Jordan AMMAN", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309932.**********}, {"date": "2023-09-07T03:00:00", "year": 2023, "id": "6c47f08b-e10b-435a-abc7-e6a34d67ad57", "type": "Withdrawal/Repayment", "amount": -800.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309132.**********}, {"date": "2023-09-28T03:00:00", "year": 2023, "id": "b685b1fb-ec65-4a19-b6a8-04e496caaab2", "type": "Withdrawal/Repayment", "amount": -19.75, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309112.**********}, {"date": "2023-10-05T03:00:00", "year": 2023, "id": "0965a21e-79e6-47c6-8264-2437dd6dd295", "type": "Withdrawal/Repayment", "amount": -29.59, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "Card transaction of JOD issued by Carrefour Jordan AMMAN (JOD 50.640 at 0.70900 rate)", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309083.**********}, {"date": "2023-10-10T03:00:00", "year": 2023, "id": "48369f79-8897-48f4-8ac1-e431c6b4db40", "type": "Withdrawal/Repayment", "amount": -48.21, "currency": "GBP", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309035.**********}, {"date": "2023-11-07T02:00:00", "year": 2023, "id": "bec10e3c-731a-493b-af00-49d862533a01", "type": "Capital Injection", "amount": 3500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 312535.**********}, {"date": "2024-01-25T02:00:00", "year": 2024, "id": "d571e7ae-b5bb-41f0-a5df-cb1f81b3082a", "type": "Withdrawal/Repayment", "amount": -157.54, "currency": "GBP", "contact": "MAZEN AKKAOUI", "reference": "", "description": "Sent money to MAZEN AKKAOUI (USD 600.00 at 1.23300 rate)", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 312377.**********}, {"date": "2024-01-26T02:00:00", "year": 2024, "id": "bcd940d1-a7ae-4f92-a73f-57c5d7b14fd7", "type": "Capital Injection", "amount": 1000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 313377.**********}, {"date": "2024-02-21T02:00:00", "year": 2024, "id": "c9bb0256-78a0-4f74-9e55-ce739605d4c3", "type": "Capital Injection", "amount": 2000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 315377.**********}, {"date": "2024-03-05T02:00:00", "year": 2024, "id": "9343161b-d4b0-48a5-b39d-5b0b152716d0", "type": "Withdrawal/Repayment", "amount": -1200.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 314177.**********}, {"date": "2024-04-04T03:00:00", "year": 2024, "id": "0ff4cf43-7bcd-4d1e-b722-ffbed0384b48", "type": "Withdrawal/Repayment", "amount": -1200.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 312977.**********}, {"date": "2024-04-30T03:00:00", "year": 2024, "id": "787117ac-6e08-441c-aad4-81889526fc74", "type": "Withdrawal/Repayment", "amount": -1281.1, "currency": "USD", "contact": "<PERSON><PERSON>", "reference": "", "description": "Loan repayment ", "account_code": "900", "account_name": "Loan", "match_reason": "Description keyword match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 311696.**********}, {"date": "2024-05-06T03:00:00", "year": 2024, "id": "e67b5aaa-de8c-404e-9ae7-dd6b38dbe229", "type": "Withdrawal/Repayment", "amount": -1200.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 310496.**********}, {"date": "2024-06-07T03:00:00", "year": 2024, "id": "cf35bef5-e721-49be-85b3-b85ed49949cd", "type": "Withdrawal/Repayment", "amount": -1200.0, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 309296.**********}, {"date": "2024-07-05T03:00:00", "year": 2024, "id": "7ccad814-77ac-4538-a62e-028e05b7089f", "type": "Withdrawal/Repayment", "amount": -1481.1, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 307815.***********}, {"date": "2024-08-07T03:00:00", "year": 2024, "id": "cc76e53f-b92d-46c4-8287-34939b5bc68f", "type": "Withdrawal/Repayment", "amount": -1181.05, "currency": "USD", "contact": "Mazen al AKKAOUI", "reference": "", "description": "", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 306634.***********}, {"date": "2024-11-07T02:00:00", "year": 2024, "id": "b4f24c1d-adea-4d88-9ca1-369bc73700d4", "type": "Capital Injection", "amount": 2000.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 308634.***********}, {"date": "2024-11-20T02:00:00", "year": 2024, "id": "dad81ec3-6cc9-4767-b829-240c9178321e", "type": "Withdrawal/Repayment", "amount": -947.04, "currency": "GBP", "contact": "MAZEN AKKAOUI", "reference": "", "description": "Sent money to MAZEN AKKAOUI (USD 600.00 at 1.23300 rate)", "account_code": "477", "account_name": "Salaries", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "spend", "source": "bank_transactions", "running_balance": 307687.**********}, {"date": "2024-12-02T02:00:00", "year": 2024, "id": "02e3565a-4179-49a3-8d41-afec4efe2408", "type": "Capital Injection", "amount": 500.0, "currency": "EUR", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 308187.**********}, {"date": "2025-02-13T02:00:00", "year": 2025, "id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "type": "Capital Injection", "amount": 1023.53, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309210.**********}, {"date": "2025-02-22T02:00:00", "year": 2025, "id": "0f89af09-0bec-4148-b26d-fd509efbd9b7", "type": "Capital Injection", "amount": 313.13, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309523.**********}, {"date": "2025-02-28T02:00:00", "year": 2025, "id": "7d6f9a5b-75ed-4872-a7cc-216c6ed651b4", "type": "Capital Injection", "amount": 234.19, "currency": "GBP", "contact": "Bilal itani", "reference": "", "description": "", "account_code": "835", "account_name": "Directors' Loan Account", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309758.**********}, {"date": "2025-04-03T03:00:00", "year": 2025, "id": "35fcd353-df19-4587-a96a-f076b2a9505f", "type": "Capital Injection", "amount": 106.38, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309864.**********}, {"date": "2025-04-03T03:00:00", "year": 2025, "id": "c3907779-e986-430a-a0d5-77e8559bacff", "type": "Capital Injection", "amount": 101.31, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 309965.**********}, {"date": "2025-04-05T03:00:00", "year": 2025, "id": "0def9c26-c86a-4640-9a10-7c61f25ba25b", "type": "Capital Injection", "amount": 101.31, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 310067.**********}, {"date": "2025-04-10T03:00:00", "year": 2025, "id": "c2579a0f-cf85-4bc0-a170-5133feeb807c", "type": "Capital Injection", "amount": 101.31, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 310168.**********}, {"date": "2025-04-14T03:00:00", "year": 2025, "id": "16ca73cc-e2dd-4a69-9e9e-38aae3f8c090", "type": "Capital Injection", "amount": 35.0, "currency": "GBP", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 310203.**********}, {"date": "2025-07-01T03:00:00", "year": 2025, "id": "********-df70-4756-8c24-3f0c61dd3dcd", "type": "Capital Injection", "amount": 10.13, "currency": "USD", "contact": "<PERSON>", "reference": "", "description": "", "account_code": "900", "account_name": "Loan", "match_reason": "Contact name match", "transaction_type": "bank_transaction", "sub_type": "receive", "source": "bank_transactions", "running_balance": 310213.**********}]}
{"validation_date": "2025-07-29T07:42:39.903655", "validation_results": {"duplicates": {"total_transactions": 4831, "unique_ids": 4831, "duplicate_ids": 0, "duplicates_detail": {}, "potential_duplicates": 552, "potential_duplicates_sample": [{"date": "2020-11-21T02:00:00", "amount": 1.99, "description": "apple.com/bill - <PERSON><PERSON><PERSON>", "contact": "Apple", "type": "bank_transaction", "id": "08749eea-43c6-4248-b766-8f84f47956cc"}, {"date": "2020-11-21T02:00:00", "amount": 1.99, "description": "apple.com/bill - <PERSON><PERSON><PERSON>", "contact": "Apple", "type": "bank_transaction", "id": "80d4750b-5afb-4a13-a578-d26427403bb8"}, {"date": "2021-06-28T03:00:00", "amount": 4.24, "description": "", "contact": "Transferwise", "type": "bank_transaction", "id": "************************************"}, {"date": "2021-07-09T03:00:00", "amount": 6700.0, "description": "", "contact": "Bilal itani", "type": "bank_transaction", "id": "3700fcb5-4d2b-4a6f-a6cf-05bf92717d70"}, {"date": "2021-07-09T03:00:00", "amount": 471.63, "description": "", "contact": "Seed Legals", "type": "bank_transaction", "id": "8a5dd9c6-7857-46fe-9b94-f255777d8509"}]}, "date_coverage": {"date_range": {"start": "2020-08-04T03:00:00", "end": "2025-07-22T03:00:00"}, "coverage_by_year": {"2020": {"months_with_data": 5, "total_transactions": 139}, "2021": {"months_with_data": 12, "total_transactions": 634}, "2022": {"months_with_data": 12, "total_transactions": 1491}, "2023": {"months_with_data": 12, "total_transactions": 1026}, "2024": {"months_with_data": 12, "total_transactions": 1308}, "2025": {"months_with_data": 7, "total_transactions": 233}}, "missing_months": ["2025-05"], "date_parse_errors": 0, "date_error_samples": []}, "source_comparison": {"bank_transactions": {"source_count": 4888, "extracted_count": 4610, "coverage_percentage": 94.*************}, "invoices": {"source_count": 120, "extracted_count": 118, "coverage_percentage": 98.**************}, "payments": {"source_count": 103, "extracted_count": 103, "coverage_percentage": 100.0}}, "financial_validation": {"revenue_validation": {"transaction_total": 396907.87, "summary_total": 396907.87, "match": true, "transaction_count": 146, "customer_count": 22}, "expense_validation": {"transaction_total": 1006680.05, "summary_total": 1006680.**********, "match": true, "transaction_count": 4572, "supplier_count": 292}, "calculation_checks": {"profit_calculation": {"summary_profit": -609772.**********, "calculated_profit": -609772.**********, "match": true}, "data_consistency": {"revenue_match": true, "expense_match": true}}}}, "summary": {"has_duplicates": false, "duplicate_count": 0, "date_coverage_complete": false, "missing_months": ["2025-05"], "source_data_coverage": {"bank_transactions": "94.3%", "invoices": "98.3%", "payments": "100.0%"}, "calculations_valid": true}, "recommendations": ["Missing data for 1 months. Verify if this is expected.", "Low coverage for bank_transactions: 94.3%. Check extraction logic."]}
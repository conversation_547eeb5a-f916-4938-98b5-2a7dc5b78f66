{"analysis_date": "2025-07-29T07:08:40.403470", "data_summary": {"total_payments": 103, "total_invoices": 120, "total_bank_transactions": 4888, "receive_transactions": 300}, "orphaned_payments": {"count": 0, "percentage": 0.0, "total_by_currency": {}, "top_contacts": [], "status_distribution": {}, "payments": []}, "unrecorded_bank_receipts": {"total_receive": 300, "unmatched_count": 228, "unmatched_percentage": 76.0, "top_contacts": [["<PERSON>", 48], ["Bilal itani", 45], ["Toters", 40], ["<PERSON>", 21], ["TBA", 12], ["<PERSON>", 8], ["BD", 8], ["Google", 4], ["Amazon", 3], ["AWS", 3]], "amount_ranges": {"Small (<50)": 129, "Medium (50-500)": 21, "Large (500-1000)": 5, "Very Large (>1000)": 73}, "total_by_currency": {"EUR": 266557.**********, "GBP": 18842.************, "USD": 242865.***********}, "transactions": [{"BankTransactionID": "6bfbb71f-6680-4577-a95a-6da056698b6d", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.1698, "HasAttachments": false, "Contact": {"ContactID": "51e5453e-3f23-45ff-8b16-6b9b48068040", "Name": "Bilal itani", "HasValidationErrors": false}, "DateString": "2021-07-09T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 6601.22, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 6601.22, "AccountCode": "835", "Quantity": 1.0, "LineItemID": "9447b673-4282-48c5-8e1d-7e498e544910", "AccountID": "86766e26-36fc-4264-b352-2949cfa25a30"}, {"Description": "Shares are recorded in thousands", "UnitAmount": 0.12, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 98.78, "AccountCode": "950", "Quantity": 845.0, "LineItemID": "d62a7600-e326-4e2a-bd88-de89edab5b06", "AccountID": "9b4efce7-575b-4db5-9e21-585cbe18196d"}], "SubTotal": 6700.0, "TotalTax": 0.0, "Total": 6700.0, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "fb36cc67-4fad-42ed-b123-455a18e73441", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "4458fda2-3e3d-412d-8bb0-490d5555e394", "Name": "Amazon", "HasValidationErrors": false}, "DateString": "2021-07-14T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"Description": "Card transaction of 1.00 USD issued by Aws Emea aws.amazon.co", "UnitAmount": 0.73, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 0.73, "AccountCode": "466", "Quantity": 1.0, "LineItemID": "********-67bb-4554-af64-6b026d212df0", "AccountID": "b66afb7a-2bb2-4ae8-81f6-809ed3c2f5c4"}], "SubTotal": 0.73, "TotalTax": 0.0, "Total": 0.73, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "0b8dabbf-7e18-46d1-ac1a-e7a7e25f589a", "BankAccount": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07", "Name": "WISE GBP"}, "Type": "RECEIVE", "IsReconciled": true, "HasAttachments": false, "Contact": {"ContactID": "70b9d5b0-d7c8-4dd4-95a9-fa2eeed26b14", "Name": "AWS", "HasValidationErrors": false}, "DateString": "2021-07-14T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 0.73, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 0.73, "AccountCode": "464", "Quantity": 1.0, "LineItemID": "7bfb71ef-d4b4-49d4-a9a5-f933ccec0a75", "AccountID": "06f64d86-832d-4e86-9718-0f777b928f4f"}], "SubTotal": 0.73, "TotalTax": 0.0, "Total": 0.73, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"BankTransactionID": "375bc278-9fbd-4e13-8c3f-f5abb67c236b", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.1683, "HasAttachments": false, "Contact": {"ContactID": "e03a7916-802c-4a9d-81fc-d72a4851b0e5", "Name": "<PERSON><PERSON><PERSON>", "HasValidationErrors": false}, "DateString": "2021-07-23T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 0.86, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 0.86, "AccountCode": "400", "Quantity": 1.0, "LineItemID": "0d9b9c8b-f811-41b1-b557-27d47ccd7ff9", "AccountID": "d109eb52-5dcc-43fb-8774-f64b3c73cae7"}], "SubTotal": 0.86, "TotalTax": 0.0, "Total": 0.86, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "a4dee236-a212-42fc-a861-3382394a8a46", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.17323, "HasAttachments": false, "Contact": {"ContactID": "4458fda2-3e3d-412d-8bb0-490d5555e394", "Name": "Amazon", "HasValidationErrors": false}, "DateString": "2021-08-04T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 0.86, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 0.86, "AccountCode": "463", "Quantity": 1.0, "LineItemID": "feea6641-daae-471a-b61e-3eee26fd3130", "AccountID": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e"}], "SubTotal": 0.86, "TotalTax": 0.0, "Total": 0.86, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "eb74da67-a042-4239-be69-3d1ed92fc781", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.17736, "HasAttachments": false, "Contact": {"ContactID": "afa44c2a-f0d4-4cf2-8c50-951bd66a118d", "Name": "<PERSON>", "HasValidationErrors": false}, "DateString": "2021-08-12T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 431.28, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 431.28, "AccountCode": "477", "Quantity": 1.0, "LineItemID": "********-48fc-453d-9625-f610d32a2615", "AccountID": "324bc493-9109-424c-8f85-ba43c00d702b"}], "SubTotal": 431.28, "TotalTax": 0.0, "Total": 431.28, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "fd7778c1-35b0-44f6-a99b-cff902197ca8", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.17736, "HasAttachments": false, "Contact": {"ContactID": "51e5453e-3f23-45ff-8b16-6b9b48068040", "Name": "Bilal itani", "HasValidationErrors": false}, "DateString": "2021-08-12T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 890.0, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 890.0, "AccountCode": "835", "Quantity": 1.0, "LineItemID": "b7d9f5c4-db76-47f8-b435-8b9832d21666", "AccountID": "86766e26-36fc-4264-b352-2949cfa25a30"}], "SubTotal": 890.0, "TotalTax": 0.0, "Total": 890.0, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "3a515839-ef2c-43d1-bc0c-11f01c83eb6f", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.17736, "HasAttachments": false, "Contact": {"ContactID": "51e5453e-3f23-45ff-8b16-6b9b48068040", "Name": "Bilal itani", "HasValidationErrors": false}, "DateString": "2021-08-12T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 4484.0, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 4484.0, "AccountCode": "835", "Quantity": 1.0, "LineItemID": "1952eced-ecbf-43fe-9856-d5f5eee738ae", "AccountID": "86766e26-36fc-4264-b352-2949cfa25a30"}], "SubTotal": 4484.0, "TotalTax": 0.0, "Total": 4484.0, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "95d000ab-3b49-4533-9950-803439991ab2", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.17736, "HasAttachments": false, "Contact": {"ContactID": "c63f6465-b949-418e-8d72-d34369d3f44d", "Name": "Adobe", "HasValidationErrors": false}, "DateString": "2021-08-12T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 1.0, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 1.0, "AccountCode": "463", "Quantity": 1.0, "LineItemID": "d39e58bb-15c5-43f8-8332-7349ddfe5e1d", "AccountID": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e"}], "SubTotal": 1.0, "TotalTax": 0.0, "Total": 1.0, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}, {"BankTransactionID": "cba598f6-7ccd-4d81-a6e0-bee446207d7d", "BankAccount": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209", "Name": "Wise EUR"}, "Type": "RECEIVE", "IsReconciled": true, "CurrencyRate": 1.16674, "HasAttachments": false, "Contact": {"ContactID": "f9b0aa9a-e2c6-4f5a-9c19-b1cbc8898acc", "Name": "<PERSON><PERSON>der", "HasValidationErrors": false}, "DateString": "2021-08-27T00:00:00", "Date": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [{"UnitAmount": 12.78, "TaxType": "NONE", "TaxAmount": 0.0, "LineAmount": 12.78, "AccountCode": "463", "Quantity": 1.0, "LineItemID": "dbd3b200-d14c-48c3-8bde-9f8318021f76", "AccountID": "4336fc1c-b354-4b93-bccc-e3ab1292fe9e"}], "SubTotal": 12.78, "TotalTax": 0.0, "Total": 12.78, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "EUR"}], "matched_count": 12, "match_rate": 4.0}, "matching_analysis": {"total_matched": 44, "total_unmatched": 58, "match_rate": 42.**************, "confidence_distribution": {"low": 42, "medium": 2}, "match_methods": {"amount_fuzzy_contact_match": 23, "amount_exact": 19, "amount_exact_contact_match": 1, "amount_exact_date_proximity": 1}, "sample_matches": [{"payment_id": "f2127656-625d-4e69-a1b9-d05018811f02", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "e82f127b-263f-4cee-a335-4b3a0b7d7563", "bank_amount": 703.0, "bank_date": "2021-07-09T00:00:00", "confidence": 0.5, "match_method": "amount_fuzzy_contact_match", "currency": "GBP"}, {"payment_id": "********-d0af-4e13-8952-c278e4e06d72", "payment_amount": 799.26, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "e8b185ae-40fe-47e3-a858-db54a3f4d898", "bank_amount": 800.0, "bank_date": "2024-06-04T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "USD"}, {"payment_id": "f85b73db-6eaa-48c2-9f92-28e13470fa84", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "e82f127b-263f-4cee-a335-4b3a0b7d7563", "bank_amount": 703.0, "bank_date": "2021-07-09T00:00:00", "confidence": 0.5, "match_method": "amount_fuzzy_contact_match", "currency": "GBP"}, {"payment_id": "0bcf9d8a-08c0-4e30-9393-a1fc5e846e3c", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "bank_amount": 1023.53, "bank_date": "2025-02-13T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}, {"payment_id": "a5639110-c986-4a69-a1a0-722b7d814498", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "bank_amount": 1023.53, "bank_date": "2025-02-13T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}, {"payment_id": "5f05a8d9-567b-4603-84eb-66682d2ba574", "payment_amount": 720.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "e82f127b-263f-4cee-a335-4b3a0b7d7563", "bank_amount": 703.0, "bank_date": "2021-07-09T00:00:00", "confidence": 0.5, "match_method": "amount_fuzzy_contact_match", "currency": "GBP"}, {"payment_id": "363d287d-cb1c-475c-a34d-883483af5134", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "bank_amount": 1023.53, "bank_date": "2025-02-13T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}, {"payment_id": "7f17692e-5d18-4d84-bd88-7c8a50b73b31", "payment_amount": 1020.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "280d1cea-eabf-4a30-9bdc-2296df06b680", "bank_amount": 1023.53, "bank_date": "2025-02-13T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}, {"payment_id": "b0eb35a7-e866-40eb-99e2-4f7505d28023", "payment_amount": 703.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "e82f127b-263f-4cee-a335-4b3a0b7d7563", "bank_amount": 703.0, "bank_date": "2021-07-09T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}, {"payment_id": "b7a4f71a-310b-411a-9d14-685d6a863c52", "payment_amount": 17.0, "payment_date": "/Date(*************+0000)/", "bank_transaction_id": "65fbf7b8-a70d-40e2-8c6f-8f869cf2a607", "bank_amount": 16.99, "bank_date": "2024-03-03T00:00:00", "confidence": 0.5, "match_method": "amount_exact", "currency": "GBP"}], "unmatched_payments": [{"PaymentID": "0bfe9e16-9936-482b-a7a6-d491a6af4b4e", "Date": "/Date(*************+0000)/", "BankAmount": 800.0, "Amount": 800.0, "CurrencyRate": 1.20745, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "18a8f767-d0a7-4b23-85c1-d8e5e37a5209"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "664a2475-c35f-4aca-a6b3-55bb7e927134", "InvoiceNumber": "INV-0014", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "4fcb7eef-0d7d-4b4d-981b-d564a59cd0be", "Name": "High Tech XL Group B.V", "HasValidationErrors": false}, "CurrencyCode": "EUR"}, "HasValidationErrors": false}, {"PaymentID": "********-09d7-410c-9b05-09f6322ed265", "Date": "/Date(*************+0000)/", "BankAmount": 347.06, "Amount": 400.0, "Reference": "ch_3Ltv62S5gCcGK53K0X2Nb2bt", "CurrencyRate": 1.152538, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "a381477d-6ccb-4046-8db1-25c82b169ada", "InvoiceNumber": "INV-0029", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "d2e3bdbe-cd8e-4ebd-bfb7-b128d313c727", "Name": "INCORP", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "c14db7ba-c2e9-4dba-837d-8fb46bf25e21", "Date": "/Date(*************+0000)/", "BankAmount": 408.57, "Amount": 479.0, "Reference": "ch_3LzaMWS5gCcGK53K1HxwsTZn", "CurrencyRate": 1.172382, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "d967c303-b2cb-43ea-a4c2-eaff49c4223f", "InvoiceNumber": "INV-0036", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "5a331741-5d73-4c8e-9e19-971718fcd829", "Name": "ESC S.A.L", "HasValidationErrors": false}, "CurrencyCode": "USD"}, "HasValidationErrors": false}, {"PaymentID": "487c27d8-2b80-4748-9a9f-0f4511c30479", "Date": "/Date(*************+0000)/", "BankAmount": 2880.0, "Amount": 2880.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "27221c70-8ba6-4d20-840c-dd41a3145101", "InvoiceNumber": "INV-0002", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "3e368d03-7ff0-4ac0-b407-3535f722b7c8", "Name": "L'atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "9eb01a1b-507e-4136-88b4-d6a38c5f9678", "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "66bc063d-0587-40b7-b53b-0bc66c86edab", "InvoiceNumber": "INV-0011", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "********-5bc5-44fb-9112-f2f0fc825e6a", "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "DateString": "2022-06-03T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 240.0, "Amount": 240.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "f0c6677e-7100-44fa-8a83-31bce6486f6b", "InvoiceNumber": "INV-0013", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "da057d0a-8e26-4c57-8cb2-39cbf0d367d4", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "b36d33ad-5f02-4c00-87f7-37a75a8f1e4b", "InvoiceNumber": "INV-0016", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "8c6ad0d5-29d5-4dd8-8139-b34e41cf65d4", "Date": "/Date(*************+0000)/", "BankAmount": 720.0, "Amount": 720.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "cc4af716-40f8-411c-b83b-d73689d9a4e7", "InvoiceNumber": "INV-0012", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "c747c258-2071-4d72-9a05-a4f1abae2c8d", "Name": "L’atelier Nawbar", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "3c89168e-5c2e-47d4-8bf5-0a351b6c5b5d", "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "08c0f60f-e8d3-48a3-b3dc-0b2206e126a4", "DateString": "2022-06-03T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 135.0, "Amount": 135.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ac441337-ea9f-4fe1-9c94-6fb2e2f1ab79", "InvoiceNumber": "INV-0017", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}, {"PaymentID": "36354d50-1b5d-4a84-b90d-b363410a1f8d", "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "BatchPayment": {"Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "BatchPaymentID": "8f0baa0d-ca31-48db-8e03-91706fe3e2a2", "DateString": "2022-06-28T00:00:00", "Date": "/Date(*************+0000)/", "Type": "RECBATCH", "Status": "AUTHORISED", "TotalAmount": 375.0, "UpdatedDateUTC": "/Date(*************+0000)/", "IsReconciled": true}, "Date": "/Date(*************+0000)/", "BankAmount": 105.0, "Amount": 105.0, "CurrencyRate": 1.0, "PaymentType": "ACCRECPAYMENT", "Status": "AUTHORISED", "UpdatedDateUTC": "/Date(*************+0000)/", "HasAccount": true, "IsReconciled": true, "Account": {"AccountID": "fd0eacc0-cb45-4d16-afd9-faaf14438f07"}, "Invoice": {"Type": "ACCREC", "InvoiceID": "ac441337-ea9f-4fe1-9c94-6fb2e2f1ab79", "InvoiceNumber": "INV-0017", "IsDiscounted": false, "HasErrors": false, "Contact": {"ContactID": "b8642502-8ab8-43d0-95fb-c02b59361a9c", "Name": "KITU KALI LIMITED", "HasValidationErrors": false}, "CurrencyCode": "GBP"}, "HasValidationErrors": false}]}, "financial_impact": {"orphaned_payments_total": {}, "unrecorded_receipts_total": {"EUR": 266557.**********, "GBP": 18842.************, "USD": 242865.***********}}}
#!/usr/bin/env python3
"""
Full Reconciliation Pipeline
Integrates data loading, advanced matching, and database storage
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from advanced_matcher_lite import AdvancedPaymentMatcherLite
from reconciliation_db import ReconciliationDatabase

class FullReconciliationPipeline:
    def __init__(self, xero_data_path: str = "../01-XERO-Data"):
        self.xero_data_path = xero_data_path
        self.matcher = AdvancedPaymentMatcherLite(xero_data_path)
        self.db = ReconciliationDatabase()
        self.payments = []
        self.transactions = []
        
    def load_data(self):
        """Load all data from Xero files"""
        print("Loading Xero data...")
        
        # Load payments
        payments_path = os.path.join(self.xero_data_path, "data/supplementary/payments/payments.json")
        with open(payments_path, 'r') as f:
            payments_data = json.load(f)
            self.payments = payments_data.get('payments', [])
        
        # Load bank transactions
        bank_path = os.path.join(self.xero_data_path, "data/core/bank_transactions.json")
        with open(bank_path, 'r') as f:
            bank_data = json.load(f)
            self.transactions = bank_data.get('bankTransactions', [])
        
        print(f"Loaded {len(self.payments)} payments and {len(self.transactions)} transactions")
        
        # Cache data in database
        self._cache_data()
    
    def _cache_data(self):
        """Cache payment and transaction data in database"""
        print("Caching data in database...")
        
        # Cache payments
        for payment in self.payments:
            if payment.get('Status') != 'DELETED':
                self.db.cache_payment(payment)
        
        # Cache bank transactions
        receive_count = 0
        for transaction in self.transactions:
            if transaction.get('Type') == 'RECEIVE' and transaction.get('Status') != 'DELETED':
                self.db.cache_bank_transaction(transaction)
                receive_count += 1
        
        print(f"Cached {len(self.payments)} payments and {receive_count} RECEIVE transactions")
    
    def run_matching(self) -> Dict[str, Any]:
        """Run advanced matching algorithm"""
        print("\nRunning advanced matching...")
        
        # Learn client patterns
        self.matcher.learn_client_patterns(self.payments, self.transactions)
        
        # Match payments
        results = self.matcher.match_all_payments(self.payments, self.transactions)
        
        # Save learned patterns to database
        for contact_id, pattern in self.matcher.client_patterns.items():
            self.db.save_client_pattern(contact_id, pattern)
        
        return results
    
    def save_matches(self, matching_results: Dict[str, Any]):
        """Save all matches to database"""
        print("\nSaving matches to database...")
        
        saved_count = 0
        for match in matching_results['matches']:
            try:
                self.db.save_match(
                    payment_id=match['payment_id'],
                    bank_transaction_id=match['bank_transaction_id'],
                    confidence=match['confidence'],
                    method='_'.join(match['matched_rules']),
                    status='auto',
                    notes=f"Auto-matched on {datetime.now().strftime('%Y-%m-%d')}"
                )
                saved_count += 1
            except Exception as e:
                print(f"Error saving match: {e}")
        
        print(f"Saved {saved_count} matches to database")
    
    def generate_comprehensive_report(self, matching_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive reconciliation report"""
        # Get database statistics
        db_stats = self.db.get_statistics()
        
        # Get unmatched items from database
        unmatched_payments = self.db.get_unmatched_payments()
        unmatched_transactions = self.db.get_unmatched_transactions()
        
        # Calculate financial impact
        unmatched_payment_total = sum(p['amount'] for p in unmatched_payments if p['amount'])
        unmatched_transaction_total = sum(t['amount'] for t in unmatched_transactions if t['amount'])
        
        # Group unmatched by currency
        payment_by_currency = {}
        for payment in unmatched_payments:
            currency = payment.get('currency', 'GBP')
            if currency not in payment_by_currency:
                payment_by_currency[currency] = 0
            payment_by_currency[currency] += payment.get('amount', 0)
        
        transaction_by_currency = {}
        for transaction in unmatched_transactions:
            currency = transaction.get('currency', 'GBP')
            if currency not in transaction_by_currency:
                transaction_by_currency[currency] = 0
            transaction_by_currency[currency] += transaction.get('amount', 0)
        
        report = {
            'report_date': datetime.now().isoformat(),
            'data_summary': {
                'total_payments': db_stats['total_payments'],
                'total_receipts': db_stats['total_receipts'],
                'total_matches': db_stats['total_matches'],
                'match_rate': db_stats['match_rate']
            },
            'matching_performance': {
                'confidence_distribution': db_stats['confidence_distribution'],
                'method_distribution': db_stats['method_distribution'],
                'average_confidence': matching_results['summary']['average_confidence']
            },
            'unmatched_analysis': {
                'unmatched_payments': {
                    'count': len(unmatched_payments),
                    'total_by_currency': payment_by_currency,
                    'top_contacts': self._get_top_contacts(unmatched_payments),
                    'date_range': self._get_date_range(unmatched_payments, 'payment_date')
                },
                'unmatched_transactions': {
                    'count': len(unmatched_transactions),
                    'total_by_currency': transaction_by_currency,
                    'top_contacts': self._get_top_contacts(unmatched_transactions),
                    'date_range': self._get_date_range(unmatched_transactions, 'transaction_date')
                }
            },
            'recommendations': self._generate_recommendations(db_stats, unmatched_payments, unmatched_transactions)
        }
        
        return report
    
    def _get_top_contacts(self, items: List[Dict], limit: int = 10) -> List[Tuple[str, int]]:
        """Get top contacts from a list of items"""
        from collections import Counter
        contacts = Counter()
        for item in items:
            contact = item.get('contact_name') or item.get('payment_contact') or 'Unknown'
            contacts[contact] += 1
        return contacts.most_common(limit)
    
    def _get_date_range(self, items: List[Dict], date_field: str) -> Dict[str, str]:
        """Get date range from items"""
        dates = []
        for item in items:
            date_str = item.get(date_field)
            if date_str:
                try:
                    if '/Date(' in str(date_str):
                        timestamp_ms = int(date_str.replace('/Date(', '').replace('+0000)/', ''))
                        date = datetime.fromtimestamp(timestamp_ms / 1000)
                    else:
                        date = datetime.fromisoformat(str(date_str).split('T')[0])
                    dates.append(date)
                except:
                    pass
        
        if dates:
            return {
                'earliest': min(dates).strftime('%Y-%m-%d'),
                'latest': max(dates).strftime('%Y-%m-%d')
            }
        return {'earliest': 'N/A', 'latest': 'N/A'}
    
    def _generate_recommendations(self, db_stats: Dict, unmatched_payments: List, 
                                 unmatched_transactions: List) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Match rate recommendations
        match_rate = db_stats['match_rate']
        if match_rate < 50:
            recommendations.append("Low match rate detected. Consider reviewing matching rules and client patterns.")
        elif match_rate < 80:
            recommendations.append("Moderate match rate. Manual review recommended for high-value unmatched items.")
        
        # Confidence recommendations
        low_confidence = db_stats['confidence_distribution'].get('low', 0)
        if low_confidence > 10:
            recommendations.append(f"{low_confidence} low-confidence matches found. Manual verification recommended.")
        
        # Unmatched recommendations
        if len(unmatched_payments) > 20:
            recommendations.append("High number of unmatched payments. Check for missing bank transaction imports.")
        
        if len(unmatched_transactions) > 50:
            recommendations.append("Many unmatched bank receipts. Consider creating payment records for regular income.")
        
        # Pattern recommendations
        recommendations.append("Run weekly reconciliation to maintain high match rates.")
        recommendations.append("Review and approve high-confidence matches to train the system.")
        
        return recommendations
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """Save report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"full_reconciliation_report_{timestamp}.json"
        
        report_path = os.path.join("../reports", filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\nReport saved to: {report_path}")
        return report_path
    
    def print_summary(self, report: Dict[str, Any]):
        """Print human-readable summary"""
        print("\n" + "="*70)
        print("COMPREHENSIVE RECONCILIATION REPORT")
        print("="*70)
        print(f"Report Date: {report['report_date']}")
        
        summary = report['data_summary']
        print(f"\nData Overview:")
        print(f"  Total Payments: {summary['total_payments']}")
        print(f"  Total Bank Receipts: {summary['total_receipts']}")
        print(f"  Total Matches: {summary['total_matches']}")
        print(f"  Match Rate: {summary['match_rate']:.1f}%")
        
        perf = report['matching_performance']
        print(f"\nMatching Performance:")
        print(f"  Average Confidence: {perf['average_confidence']:.2f}")
        print("  Confidence Distribution:")
        for level, count in perf['confidence_distribution'].items():
            if count:
                print(f"    {level.capitalize()}: {count}")
        
        unmatched = report['unmatched_analysis']
        print(f"\nUnmatched Payments: {unmatched['unmatched_payments']['count']}")
        for currency, amount in unmatched['unmatched_payments']['total_by_currency'].items():
            print(f"  {currency}: {amount:,.2f}")
        
        print(f"\nUnmatched Bank Receipts: {unmatched['unmatched_transactions']['count']}")
        for currency, amount in unmatched['unmatched_transactions']['total_by_currency'].items():
            print(f"  {currency}: {amount:,.2f}")
        
        print("\nRecommendations:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
        
        print("\n" + "="*70)


def main():
    """Run full reconciliation pipeline"""
    pipeline = FullReconciliationPipeline()
    
    try:
        # Load data
        pipeline.load_data()
        
        # Run matching
        matching_results = pipeline.run_matching()
        
        # Save matches to database
        pipeline.save_matches(matching_results)
        
        # Generate comprehensive report
        report = pipeline.generate_comprehensive_report(matching_results)
        
        # Save and display report
        report_path = pipeline.save_report(report)
        pipeline.print_summary(report)
        
        print(f"\nFull reconciliation complete!")
        print(f"Database: ../data/reconciliation.db")
        print(f"Report: {report_path}")
        
    except Exception as e:
        print(f"Error during reconciliation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
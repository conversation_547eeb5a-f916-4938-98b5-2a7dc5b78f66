#!/usr/bin/env python3
"""
Payment Reconciliation Script
Analyzes payments without invoices and unrecorded bank transactions
"""

import json
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
import re

# Add parent directory to path to access Xero data
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class PaymentReconciliation:
    def __init__(self, xero_data_path: str = "../01-XERO-Data"):
        self.xero_data_path = xero_data_path
        self.payments = []
        self.invoices = []
        self.bank_transactions = []
        self.contacts = {}
        
    def load_data(self):
        """Load all necessary data files"""
        print("Loading data files...")
        
        # Load payments
        payments_path = os.path.join(self.xero_data_path, "data/supplementary/payments/payments.json")
        with open(payments_path, 'r') as f:
            payments_data = json.load(f)
            self.payments = payments_data.get('payments', [])
            
        # Load invoices from consolidated core data
        invoices_path = os.path.join(self.xero_data_path, "data/core/invoices.json")
        with open(invoices_path, 'r') as f:
            invoices_data = json.load(f)
            self.invoices = invoices_data.get('invoices', [])
            
        # Load bank transactions from consolidated core data
        bank_tx_path = os.path.join(self.xero_data_path, "data/core/bank_transactions.json")
        with open(bank_tx_path, 'r') as f:
            bank_data = json.load(f)
            self.bank_transactions = bank_data.get('bankTransactions', [])
            
        # Load contacts
        contacts_path = os.path.join(self.xero_data_path, "data/core/contacts.json")
        with open(contacts_path, 'r') as f:
            contacts_data = json.load(f)
            contacts_list = contacts_data.get('contacts', []) if isinstance(contacts_data, dict) else contacts_data
            self.contacts = {c['ContactID']: c['Name'] for c in contacts_list if 'ContactID' in c}
            
        print(f"Loaded: {len(self.payments)} payments, {len(self.invoices)} invoices, "
              f"{len(self.bank_transactions)} bank transactions, {len(self.contacts)} contacts")
    
    def analyze_orphaned_payments(self) -> Dict[str, Any]:
        """Find payments that reference non-existent invoices"""
        # Create invoice lookup
        invoice_ids = set(inv['InvoiceID'] for inv in self.invoices if 'InvoiceID' in inv)
        
        orphaned_payments = []
        for payment in self.payments:
            if 'Invoice' in payment and payment['Invoice']:
                invoice_id = payment['Invoice'].get('InvoiceID')
                if invoice_id and invoice_id not in invoice_ids:
                    orphaned_payments.append(payment)
        
        # Analyze orphaned payments
        total_by_currency = defaultdict(float)
        contact_counts = Counter()
        status_counts = Counter()
        
        for payment in orphaned_payments:
            # Currency totals
            if payment['Invoice'] and payment['Invoice'].get('CurrencyCode'):
                currency = payment['Invoice']['CurrencyCode']
                amount = payment.get('Amount', 0)
                total_by_currency[currency] += amount
            
            # Contact counts
            if payment['Invoice'] and payment['Invoice'].get('Contact'):
                contact_name = payment['Invoice']['Contact'].get('Name', 'Unknown')
                contact_counts[contact_name] += 1
                
            # Status counts
            status_counts[payment.get('Status', 'Unknown')] += 1
        
        return {
            'count': len(orphaned_payments),
            'percentage': (len(orphaned_payments) / len(self.payments) * 100) if self.payments else 0,
            'total_by_currency': dict(total_by_currency),
            'top_contacts': contact_counts.most_common(10),
            'status_distribution': dict(status_counts),
            'payments': orphaned_payments[:10]  # First 10 for example
        }
    
    def match_payment_to_transaction(self, payment: Dict, transaction: Dict, tolerance: float = 0.01) -> Tuple[bool, float, str]:
        """Match a payment to a bank transaction with fuzzy logic
        
        Returns: (is_match, confidence_score, match_method)
        """
        confidence = 0.0
        match_reasons = []
        
        # Extract amounts
        payment_amount = payment.get('Amount', 0)
        bank_amount = transaction.get('Total', 0)
        
        # Check currency match
        payment_currency = payment.get('Invoice', {}).get('CurrencyCode', 'GBP')
        bank_currency = transaction.get('CurrencyCode', 'GBP')
        
        if payment_currency != bank_currency:
            return False, 0.0, "Currency mismatch"
        
        # Amount matching with tolerance
        amount_diff = abs(payment_amount - bank_amount)
        amount_tolerance = bank_amount * tolerance
        
        if amount_diff <= amount_tolerance:
            confidence += 0.5
            match_reasons.append("amount_exact")
        elif amount_diff <= bank_amount * 0.05:  # 5% tolerance for fees
            confidence += 0.3
            match_reasons.append("amount_fuzzy")
        else:
            return False, 0.0, "Amount mismatch"
        
        # Date proximity matching
        payment_date = payment.get('Date', '')
        bank_date_str = transaction.get('DateString', '')
        
        if payment_date and bank_date_str:
            try:
                # Parse payment date (Unix timestamp format)
                if '/Date(' in payment_date:
                    timestamp_ms = int(payment_date.replace('/Date(', '').replace('+0000)/', ''))
                    payment_dt = datetime.fromtimestamp(timestamp_ms / 1000)
                else:
                    payment_dt = datetime.fromisoformat(payment_date.split('T')[0])
                
                # Parse bank date (ISO format)
                bank_dt = datetime.fromisoformat(bank_date_str.split('T')[0])
                
                date_diff = abs((payment_dt - bank_dt).days)
                
                if date_diff == 0:
                    confidence += 0.3
                    match_reasons.append("date_exact")
                elif date_diff <= 3:
                    confidence += 0.2
                    match_reasons.append("date_proximity")
                elif date_diff <= 7:
                    confidence += 0.1
                    match_reasons.append("date_week")
            except Exception:
                # Skip date matching if parsing fails
                pass
        
        # Contact matching
        payment_contact = payment.get('Invoice', {}).get('Contact', {}).get('ContactID')
        bank_contact = transaction.get('Contact', {}).get('ContactID')
        
        if payment_contact and bank_contact and payment_contact == bank_contact:
            confidence += 0.2
            match_reasons.append("contact_match")
        
        # Reference matching
        payment_ref = str(payment.get('Reference', '')).lower()
        bank_ref = str(transaction.get('Reference', '')).lower()
        
        if payment_ref and bank_ref:
            if payment_ref in bank_ref or bank_ref in payment_ref:
                confidence += 0.15
                match_reasons.append("reference_match")
        
        # Invoice number in bank reference
        invoice_num = payment.get('Invoice', {}).get('InvoiceNumber', '')
        if invoice_num and invoice_num.lower() in bank_ref:
            confidence += 0.2
            match_reasons.append("invoice_ref")
        
        match_method = "_".join(match_reasons) if match_reasons else "no_match"
        is_match = confidence >= 0.5  # 50% confidence threshold
        
        return is_match, confidence, match_method
    
    def analyze_unrecorded_bank_receipts(self) -> Dict[str, Any]:
        """Find bank RECEIVE transactions without matching payment records"""
        # Get all RECEIVE transactions
        receive_txs = [tx for tx in self.bank_transactions if tx.get('Type') == 'RECEIVE']
        
        # Create payment amount lookup
        payment_amounts = defaultdict(list)
        for payment in self.payments:
            amount = payment.get('Amount', 0)
            payment_amounts[amount].append(payment)
        
        # Find unmatched RECEIVE transactions with fuzzy matching
        unmatched = []
        matched_transactions = set()
        
        for tx in receive_txs:
            if tx.get('Status') == 'DELETED':
                continue  # Skip deleted transactions
                
            best_match = None
            best_confidence = 0.0
            best_method = ""
            
            # Try to match with each payment
            for payment in self.payments:
                if payment.get('Status') == 'DELETED':
                    continue
                    
                is_match, confidence, method = self.match_payment_to_transaction(payment, tx)
                if is_match and confidence > best_confidence:
                    best_match = payment
                    best_confidence = confidence
                    best_method = method
            
            if not best_match:
                unmatched.append(tx)
            else:
                matched_transactions.add(tx['BankTransactionID'])
        
        # Analyze unmatched transactions
        contact_counts = Counter()
        amount_ranges = {
            'Small (<50)': 0,
            'Medium (50-500)': 0,
            'Large (500-1000)': 0,
            'Very Large (>1000)': 0
        }
        total_by_currency = defaultdict(float)
        
        for tx in unmatched:
            # Contact analysis
            contact_name = tx.get('Contact', {}).get('Name', 'Unknown')
            contact_counts[contact_name] += 1
            
            # Amount range analysis
            amount = tx.get('Total', 0)
            if amount < 50:
                amount_ranges['Small (<50)'] += 1
            elif amount < 500:
                amount_ranges['Medium (50-500)'] += 1
            elif amount < 1000:
                amount_ranges['Large (500-1000)'] += 1
            else:
                amount_ranges['Very Large (>1000)'] += 1
                
            # Currency totals
            currency = tx.get('CurrencyCode', 'Unknown')
            total_by_currency[currency] += amount
        
        return {
            'total_receive': len(receive_txs),
            'unmatched_count': len(unmatched),
            'unmatched_percentage': (len(unmatched) / len(receive_txs) * 100) if receive_txs else 0,
            'top_contacts': contact_counts.most_common(10),
            'amount_ranges': amount_ranges,
            'total_by_currency': dict(total_by_currency),
            'transactions': unmatched[:10],  # First 10 for example
            'matched_count': len(matched_transactions),
            'match_rate': (len(matched_transactions) / len(receive_txs) * 100) if receive_txs else 0
        }
    
    def generate_matching_report(self) -> Dict[str, Any]:
        """Generate detailed matching report between payments and bank transactions"""
        matched_pairs = []
        unmatched_payments = []
        confidence_distribution = defaultdict(int)
        match_method_counts = Counter()
        
        # Try to match each payment
        for payment in self.payments:
            if payment.get('Status') == 'DELETED':
                continue
                
            best_match = None
            best_confidence = 0.0
            best_method = ""
            
            # Check all bank transactions
            for tx in self.bank_transactions:
                if tx.get('Type') != 'RECEIVE' or tx.get('Status') == 'DELETED':
                    continue
                    
                is_match, confidence, method = self.match_payment_to_transaction(payment, tx)
                if is_match and confidence > best_confidence:
                    best_match = tx
                    best_confidence = confidence
                    best_method = method
            
            if best_match:
                matched_pairs.append({
                    'payment_id': payment['PaymentID'],
                    'payment_amount': payment.get('Amount', 0),
                    'payment_date': payment.get('Date', ''),
                    'bank_transaction_id': best_match['BankTransactionID'],
                    'bank_amount': best_match.get('Total', 0),
                    'bank_date': best_match.get('DateString', ''),
                    'confidence': best_confidence,
                    'match_method': best_method,
                    'currency': payment.get('Invoice', {}).get('CurrencyCode', 'GBP')
                })
                
                # Track confidence distribution
                if best_confidence >= 0.9:
                    confidence_distribution['high'] += 1
                elif best_confidence >= 0.7:
                    confidence_distribution['medium'] += 1
                else:
                    confidence_distribution['low'] += 1
                    
                # Track match methods
                match_method_counts[best_method] += 1
            else:
                unmatched_payments.append(payment)
        
        return {
            'total_matched': len(matched_pairs),
            'total_unmatched': len(unmatched_payments),
            'match_rate': (len(matched_pairs) / len(self.payments) * 100) if self.payments else 0,
            'confidence_distribution': dict(confidence_distribution),
            'match_methods': dict(match_method_counts),
            'sample_matches': matched_pairs[:10],
            'unmatched_payments': unmatched_payments[:10]
        }
    
    def generate_reconciliation_summary(self) -> Dict[str, Any]:
        """Generate overall reconciliation summary"""
        orphaned = self.analyze_orphaned_payments()
        unrecorded = self.analyze_unrecorded_bank_receipts()
        matching = self.generate_matching_report()
        
        return {
            'analysis_date': datetime.now().isoformat(),
            'data_summary': {
                'total_payments': len(self.payments),
                'total_invoices': len(self.invoices),
                'total_bank_transactions': len(self.bank_transactions),
                'receive_transactions': unrecorded['total_receive']
            },
            'orphaned_payments': orphaned,
            'unrecorded_bank_receipts': unrecorded,
            'matching_analysis': matching,
            'financial_impact': {
                'orphaned_payments_total': orphaned['total_by_currency'],
                'unrecorded_receipts_total': unrecorded['total_by_currency']
            }
        }
    
    def save_report(self, report: Dict[str, Any], filename: str = "reconciliation_report.json"):
        """Save reconciliation report to file"""
        # Get the parent directory of the scripts folder
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        report_path = os.path.join(parent_dir, "reports", filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Report saved to: {report_path}")
        
    def print_summary(self, report: Dict[str, Any]):
        """Print a human-readable summary"""
        print("\n" + "="*60)
        print("PAYMENT RECONCILIATION SUMMARY")
        print("="*60)
        
        print(f"\nAnalysis Date: {report['analysis_date']}")
        
        print("\n--- Data Overview ---")
        summary = report['data_summary']
        print(f"Total Payments: {summary['total_payments']}")
        print(f"Total Invoices: {summary['total_invoices']}")
        print(f"Total Bank Transactions: {summary['total_bank_transactions']}")
        print(f"RECEIVE Transactions: {summary['receive_transactions']}")
        
        print("\n--- Orphaned Payments ---")
        orphaned = report['orphaned_payments']
        print(f"Count: {orphaned['count']} ({orphaned['percentage']:.1f}%)")
        print("Total by Currency:")
        for currency, total in orphaned['total_by_currency'].items():
            print(f"  {currency}: {total:,.2f}")
        print("Top Affected Contacts:")
        for contact, count in orphaned['top_contacts'][:5]:
            print(f"  {contact}: {count} payments")
            
        print("\n--- Unrecorded Bank Receipts ---")
        unrecorded = report['unrecorded_bank_receipts']
        print(f"Count: {unrecorded['unmatched_count']} ({unrecorded['unmatched_percentage']:.1f}%)")
        print("Amount Distribution:")
        for range_name, count in unrecorded['amount_ranges'].items():
            print(f"  {range_name}: {count}")
        print("Top Sources:")
        for contact, count in unrecorded['top_contacts'][:5]:
            print(f"  {contact}: {count} transactions")
        print(f"\nMatched Transactions: {unrecorded.get('matched_count', 0)} ({unrecorded.get('match_rate', 0):.1f}%)")
        
        print("\n--- Payment-to-Bank Matching ---")
        matching = report.get('matching_analysis', {})
        if matching:
            print(f"Matched Payments: {matching.get('total_matched', 0)} ({matching.get('match_rate', 0):.1f}%)")
            print("Confidence Distribution:")
            for level, count in matching.get('confidence_distribution', {}).items():
                print(f"  {level.capitalize()}: {count}")
            print("Match Methods:")
            for method, count in list(matching.get('match_methods', {}).items())[:5]:
                print(f"  {method}: {count}")


def main():
    """Main execution function"""
    reconciler = PaymentReconciliation()
    
    try:
        # Load data
        reconciler.load_data()
        
        # Generate report
        print("\nAnalyzing payment reconciliation...")
        report = reconciler.generate_reconciliation_summary()
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reconciliation_report_{timestamp}.json"
        reconciler.save_report(report, filename)
        
        # Print summary
        reconciler.print_summary(report)
        
        print("\n" + "="*60)
        print("Analysis complete! Check the reports directory for detailed results.")
        
    except Exception as e:
        print(f"Error during reconciliation: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
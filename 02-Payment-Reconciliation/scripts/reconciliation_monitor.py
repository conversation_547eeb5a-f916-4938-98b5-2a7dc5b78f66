#!/usr/bin/env python3
"""
Reconciliation Monitoring System
Real-time monitoring and alerting for payment reconciliation
"""

import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import time
from collections import defaultdict

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from reconciliation_db import ReconciliationDatabase

class ReconciliationMonitor:
    def __init__(self, db_path: str = "../data/reconciliation.db"):
        self.db = ReconciliationDatabase(db_path)
        self.alerts = []
        self.thresholds = {
            'low_match_rate': 60,  # Alert if match rate < 60%
            'high_value_unmatched': 1000,  # Alert for unmatched items > £1000
            'old_unmatched_days': 30,  # Alert for unmatched items > 30 days old
            'low_confidence_threshold': 0.7,  # Alert for matches below this confidence
            'stale_data_hours': 24  # Alert if no new data in 24 hours
        }
    
    def check_match_rate(self) -> Dict[str, Any]:
        """Check overall match rate and generate alert if below threshold"""
        stats = self.db.get_statistics()
        match_rate = stats['match_rate']
        
        status = 'OK' if match_rate >= self.thresholds['low_match_rate'] else 'ALERT'
        
        return {
            'metric': 'match_rate',
            'value': match_rate,
            'threshold': self.thresholds['low_match_rate'],
            'status': status,
            'message': f"Match rate is {match_rate:.1f}% ({'below' if status == 'ALERT' else 'above'} threshold)"
        }
    
    def check_high_value_unmatched(self) -> List[Dict[str, Any]]:
        """Check for high-value unmatched items"""
        alerts = []
        
        # Check unmatched payments
        unmatched_payments = self.db.get_unmatched_payments()
        for payment in unmatched_payments:
            amount = payment.get('amount', 0)
            if amount > self.thresholds['high_value_unmatched']:
                alerts.append({
                    'metric': 'high_value_unmatched_payment',
                    'payment_id': payment['payment_id'],
                    'invoice_number': payment.get('invoice_number'),
                    'amount': amount,
                    'currency': payment.get('currency', 'GBP'),
                    'contact': payment.get('contact_name'),
                    'date': payment.get('payment_date'),
                    'status': 'ALERT',
                    'message': f"Unmatched payment of {payment.get('currency', 'GBP')} {amount:,.2f}"
                })
        
        # Check unmatched transactions
        unmatched_transactions = self.db.get_unmatched_transactions()
        for transaction in unmatched_transactions:
            amount = transaction.get('amount', 0)
            if amount > self.thresholds['high_value_unmatched']:
                alerts.append({
                    'metric': 'high_value_unmatched_transaction',
                    'transaction_id': transaction['bank_transaction_id'],
                    'amount': amount,
                    'currency': transaction.get('currency', 'GBP'),
                    'contact': transaction.get('contact_name'),
                    'reference': transaction.get('reference'),
                    'date': transaction.get('transaction_date'),
                    'status': 'ALERT',
                    'message': f"Unmatched bank receipt of {transaction.get('currency', 'GBP')} {amount:,.2f}"
                })
        
        return alerts
    
    def check_old_unmatched(self) -> List[Dict[str, Any]]:
        """Check for old unmatched items"""
        alerts = []
        cutoff_date = datetime.now() - timedelta(days=self.thresholds['old_unmatched_days'])
        
        # Check old unmatched payments
        unmatched_payments = self.db.get_unmatched_payments()
        for payment in unmatched_payments:
            try:
                date_str = payment.get('payment_date', '')
                if '/Date(' in str(date_str):
                    timestamp_ms = int(date_str.replace('/Date(', '').replace('+0000)/', ''))
                    payment_date = datetime.fromtimestamp(timestamp_ms / 1000)
                else:
                    payment_date = datetime.fromisoformat(str(date_str).split('T')[0])
                
                if payment_date < cutoff_date:
                    days_old = (datetime.now() - payment_date).days
                    alerts.append({
                        'metric': 'old_unmatched_payment',
                        'payment_id': payment['payment_id'],
                        'invoice_number': payment.get('invoice_number'),
                        'amount': payment.get('amount', 0),
                        'currency': payment.get('currency', 'GBP'),
                        'date': payment.get('payment_date'),
                        'days_old': days_old,
                        'status': 'WARNING',
                        'message': f"Payment unmatched for {days_old} days"
                    })
            except:
                pass
        
        return alerts
    
    def check_low_confidence_matches(self) -> List[Dict[str, Any]]:
        """Check for matches with low confidence"""
        alerts = []
        matches = self.db.get_all_matches()
        
        for match in matches:
            confidence = match.get('match_confidence', 0)
            if confidence < self.thresholds['low_confidence_threshold']:
                alerts.append({
                    'metric': 'low_confidence_match',
                    'payment_id': match['payment_id'],
                    'invoice_number': match.get('invoice_number'),
                    'bank_transaction_id': match['bank_transaction_id'],
                    'confidence': confidence,
                    'method': match.get('match_method'),
                    'payment_amount': match.get('payment_amount'),
                    'bank_amount': match.get('bank_amount'),
                    'currency': match.get('payment_currency', 'GBP'),
                    'status': 'WARNING',
                    'message': f"Low confidence match ({confidence:.2f})"
                })
        
        return alerts
    
    def check_anomalies(self) -> List[Dict[str, Any]]:
        """Check for anomalies in payment patterns"""
        alerts = []
        
        # Check for duplicate amounts on same day
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) as count, amount, currency, 
                       DATE(transaction_date) as date, 
                       GROUP_CONCAT(bank_transaction_id) as transaction_ids
                FROM bank_transaction_details
                WHERE type = 'RECEIVE' AND status != 'DELETED'
                GROUP BY amount, currency, DATE(transaction_date)
                HAVING count > 1
                ORDER BY count DESC
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                if row['count'] > 2:  # More than 2 identical amounts on same day
                    alerts.append({
                        'metric': 'duplicate_transactions',
                        'amount': row['amount'],
                        'currency': row['currency'],
                        'date': row['date'],
                        'count': row['count'],
                        'transaction_ids': row['transaction_ids'].split(','),
                        'status': 'INFO',
                        'message': f"{row['count']} identical amounts of {row['currency']} {row['amount']} on {row['date']}"
                    })
        
        return alerts
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """Generate data for dashboard visualization"""
        stats = self.db.get_statistics()
        
        # Get recent activity
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Recent matches
            cursor.execute("""
                SELECT DATE(created_date) as date, COUNT(*) as count
                FROM reconciliation_matches
                WHERE created_date >= date('now', '-7 days')
                  AND match_status != 'deleted'
                GROUP BY DATE(created_date)
                ORDER BY date DESC
            """)
            recent_matches = [dict(row) for row in cursor.fetchall()]
            
            # Match trend
            cursor.execute("""
                SELECT DATE(created_date) as date, 
                       AVG(match_confidence) as avg_confidence,
                       COUNT(*) as matches
                FROM reconciliation_matches
                WHERE created_date >= date('now', '-30 days')
                  AND match_status != 'deleted'
                GROUP BY DATE(created_date)
                ORDER BY date
            """)
            match_trend = [dict(row) for row in cursor.fetchall()]
        
        # Currency breakdown
        unmatched_payments = self.db.get_unmatched_payments()
        unmatched_transactions = self.db.get_unmatched_transactions()
        
        payment_by_currency = defaultdict(float)
        for p in unmatched_payments:
            payment_by_currency[p.get('currency', 'GBP')] += p.get('amount', 0)
        
        transaction_by_currency = defaultdict(float)
        for t in unmatched_transactions:
            transaction_by_currency[t.get('currency', 'GBP')] += t.get('amount', 0)
        
        return {
            'summary': {
                'match_rate': stats['match_rate'],
                'total_matches': stats['total_matches'],
                'unmatched_payments': stats['unmatched_payments'],
                'unmatched_receipts': stats['unmatched_receipts']
            },
            'confidence_distribution': stats['confidence_distribution'],
            'method_distribution': stats['method_distribution'],
            'recent_activity': recent_matches,
            'match_trend': match_trend,
            'unmatched_by_currency': {
                'payments': dict(payment_by_currency),
                'transactions': dict(transaction_by_currency)
            },
            'last_updated': datetime.now().isoformat()
        }
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all monitoring checks"""
        print("Running reconciliation monitoring checks...")
        
        all_alerts = []
        
        # Check match rate
        match_rate_check = self.check_match_rate()
        if match_rate_check['status'] != 'OK':
            all_alerts.append(match_rate_check)
        
        # Check high value unmatched
        high_value_alerts = self.check_high_value_unmatched()
        all_alerts.extend(high_value_alerts)
        
        # Check old unmatched
        old_alerts = self.check_old_unmatched()
        all_alerts.extend(old_alerts)
        
        # Check low confidence
        low_confidence_alerts = self.check_low_confidence_matches()
        all_alerts.extend(low_confidence_alerts)
        
        # Check anomalies
        anomaly_alerts = self.check_anomalies()
        all_alerts.extend(anomaly_alerts)
        
        # Categorize alerts
        alerts_by_status = defaultdict(list)
        for alert in all_alerts:
            alerts_by_status[alert['status']].append(alert)
        
        return {
            'check_date': datetime.now().isoformat(),
            'total_alerts': len(all_alerts),
            'alerts_by_status': dict(alerts_by_status),
            'all_alerts': all_alerts,
            'dashboard_data': self.generate_dashboard_data()
        }
    
    def save_monitoring_report(self, results: Dict[str, Any], filename: str = None):
        """Save monitoring results to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"monitoring_report_{timestamp}.json"
        
        report_path = os.path.join("../reports", filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        return report_path
    
    def print_summary(self, results: Dict[str, Any]):
        """Print monitoring summary"""
        print("\n" + "="*60)
        print("RECONCILIATION MONITORING REPORT")
        print("="*60)
        print(f"Check Date: {results['check_date']}")
        print(f"Total Alerts: {results['total_alerts']}")
        
        # Alert summary
        for status, alerts in results['alerts_by_status'].items():
            print(f"\n{status} Alerts: {len(alerts)}")
            for alert in alerts[:5]:  # Show first 5
                print(f"  - {alert['message']}")
        
        # Dashboard summary
        dashboard = results['dashboard_data']
        print(f"\nCurrent Status:")
        print(f"  Match Rate: {dashboard['summary']['match_rate']:.1f}%")
        print(f"  Total Matches: {dashboard['summary']['total_matches']}")
        print(f"  Unmatched Payments: {dashboard['summary']['unmatched_payments']}")
        print(f"  Unmatched Receipts: {dashboard['summary']['unmatched_receipts']}")
        
        print("\nUnmatched by Currency:")
        print("  Payments:")
        for currency, amount in dashboard['unmatched_by_currency']['payments'].items():
            print(f"    {currency}: {amount:,.2f}")
        print("  Receipts:")
        for currency, amount in dashboard['unmatched_by_currency']['transactions'].items():
            print(f"    {currency}: {amount:,.2f}")


def main():
    """Run monitoring checks"""
    monitor = ReconciliationMonitor()
    
    # Run all checks
    results = monitor.run_all_checks()
    
    # Save report
    report_path = monitor.save_monitoring_report(results)
    
    # Print summary
    monitor.print_summary(results)
    
    print(f"\nMonitoring report saved to: {report_path}")


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Advanced Payment Matcher with Machine Learning
Uses pattern recognition and client behavior analysis for intelligent matching
"""

import json
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import pickle
import re

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class AdvancedPaymentMatcher:
    def __init__(self, xero_data_path: str = "../01-XERO-Data"):
        self.xero_data_path = xero_data_path
        self.model = None
        self.scaler = StandardScaler()
        self.client_patterns = defaultdict(dict)
        self.currency_rates = {}
        self.model_path = "models/payment_matcher.pkl"
        
    def extract_features(self, payment: Dict, transaction: Dict) -> List[float]:
        """Extract numerical features for ML model"""
        features = []
        
        # Amount features
        payment_amount = payment.get('Amount', 0)
        bank_amount = transaction.get('Total', 0)
        amount_diff = abs(payment_amount - bank_amount)
        amount_ratio = min(payment_amount, bank_amount) / max(payment_amount, bank_amount) if max(payment_amount, bank_amount) > 0 else 0
        
        features.extend([
            amount_diff,
            amount_ratio,
            1 if amount_diff == 0 else 0,  # Exact match
            1 if amount_diff <= bank_amount * 0.01 else 0,  # 1% tolerance
            1 if amount_diff <= bank_amount * 0.05 else 0,  # 5% tolerance
        ])
        
        # Date features
        date_diff_days = self._calculate_date_difference(payment, transaction)
        features.extend([
            date_diff_days if date_diff_days is not None else 999,
            1 if date_diff_days == 0 else 0,  # Same day
            1 if date_diff_days is not None and date_diff_days <= 3 else 0,  # Within 3 days
            1 if date_diff_days is not None and date_diff_days <= 7 else 0,  # Within week
        ])
        
        # Contact features
        same_contact = 1 if self._same_contact(payment, transaction) else 0
        features.append(same_contact)
        
        # Reference features
        ref_similarity = self._reference_similarity(payment, transaction)
        has_invoice_ref = self._has_invoice_reference(payment, transaction)
        features.extend([ref_similarity, has_invoice_ref])
        
        # Currency features
        same_currency = 1 if self._same_currency(payment, transaction) else 0
        features.append(same_currency)
        
        # Client pattern features
        client_score = self._get_client_pattern_score(payment, transaction)
        features.append(client_score)
        
        # Transaction type features
        is_recurring = self._is_recurring_amount(payment, transaction)
        features.append(is_recurring)
        
        return features
    
    def _calculate_date_difference(self, payment: Dict, transaction: Dict) -> Optional[int]:
        """Calculate date difference in days"""
        try:
            # Parse payment date
            payment_date = payment.get('Date', '')
            if '/Date(' in payment_date:
                timestamp_ms = int(payment_date.replace('/Date(', '').replace('+0000)/', ''))
                payment_dt = datetime.fromtimestamp(timestamp_ms / 1000)
            else:
                payment_dt = datetime.fromisoformat(payment_date.split('T')[0])
            
            # Parse transaction date
            bank_date = transaction.get('DateString', '').split('T')[0]
            bank_dt = datetime.fromisoformat(bank_date)
            
            return abs((payment_dt - bank_dt).days)
        except:
            return None
    
    def _same_contact(self, payment: Dict, transaction: Dict) -> bool:
        """Check if payment and transaction have same contact"""
        payment_contact = payment.get('Invoice', {}).get('Contact', {}).get('ContactID')
        bank_contact = transaction.get('Contact', {}).get('ContactID')
        return payment_contact == bank_contact if payment_contact and bank_contact else False
    
    def _same_currency(self, payment: Dict, transaction: Dict) -> bool:
        """Check if payment and transaction have same currency"""
        payment_currency = payment.get('Invoice', {}).get('CurrencyCode', 'GBP')
        bank_currency = transaction.get('CurrencyCode', 'GBP')
        return payment_currency == bank_currency
    
    def _reference_similarity(self, payment: Dict, transaction: Dict) -> float:
        """Calculate reference string similarity"""
        payment_ref = str(payment.get('Reference', '')).lower()
        bank_ref = str(transaction.get('Reference', '')).lower()
        
        if not payment_ref or not bank_ref:
            return 0.0
            
        # Check for substring matches
        if payment_ref in bank_ref or bank_ref in payment_ref:
            return 1.0
            
        # Calculate character overlap
        common_chars = set(payment_ref) & set(bank_ref)
        if not common_chars:
            return 0.0
            
        return len(common_chars) / max(len(set(payment_ref)), len(set(bank_ref)))
    
    def _has_invoice_reference(self, payment: Dict, transaction: Dict) -> float:
        """Check if invoice number appears in bank reference"""
        invoice_num = payment.get('Invoice', {}).get('InvoiceNumber', '')
        bank_ref = str(transaction.get('Reference', '')).lower()
        
        if invoice_num and invoice_num.lower() in bank_ref:
            return 1.0
        return 0.0
    
    def _get_client_pattern_score(self, payment: Dict, transaction: Dict) -> float:
        """Get client payment pattern score"""
        contact_id = payment.get('Invoice', {}).get('Contact', {}).get('ContactID', '')
        if not contact_id or contact_id not in self.client_patterns:
            return 0.0
            
        patterns = self.client_patterns[contact_id]
        amount = payment.get('Amount', 0)
        
        # Check if amount matches typical client payments
        if 'typical_amounts' in patterns:
            for typical_amount in patterns['typical_amounts']:
                if abs(amount - typical_amount) <= typical_amount * 0.05:  # 5% tolerance
                    return 0.8
        
        # Check if timing matches typical pattern
        if 'payment_days' in patterns:
            date_diff = self._calculate_date_difference(payment, transaction)
            if date_diff is not None and date_diff in patterns['payment_days']:
                return 0.7
                
        return 0.0
    
    def _is_recurring_amount(self, payment: Dict, transaction: Dict) -> float:
        """Check if this is a recurring payment amount"""
        amount = payment.get('Amount', 0)
        # Simple check - in practice, would analyze historical data
        common_amounts = [50, 100, 200, 500, 1000, 2000, 5000]
        return 1.0 if amount in common_amounts else 0.0
    
    def learn_client_patterns(self, payments: List[Dict], transactions: List[Dict]):
        """Learn payment patterns for each client"""
        print("Learning client payment patterns...")
        
        # Group payments by client
        client_payments = defaultdict(list)
        for payment in payments:
            if payment.get('Status') == 'DELETED':
                continue
            contact_id = payment.get('Invoice', {}).get('Contact', {}).get('ContactID', '')
            if contact_id:
                client_payments[contact_id].append(payment)
        
        # Analyze patterns for each client
        for contact_id, payments_list in client_payments.items():
            if len(payments_list) < 3:  # Need minimum data
                continue
                
            # Extract typical amounts
            amounts = [p.get('Amount', 0) for p in payments_list]
            amount_counts = Counter(amounts)
            typical_amounts = [amt for amt, count in amount_counts.items() if count >= 2]
            
            # Extract typical payment delays
            payment_days = []
            for payment in payments_list:
                invoice_date = payment.get('Invoice', {}).get('Date', '')
                payment_date = payment.get('Date', '')
                if invoice_date and payment_date:
                    try:
                        days_diff = self._calculate_date_difference(
                            {'Date': invoice_date}, 
                            {'DateString': payment_date}
                        )
                        if days_diff is not None:
                            payment_days.append(days_diff)
                    except:
                        pass
            
            self.client_patterns[contact_id] = {
                'typical_amounts': typical_amounts,
                'payment_days': list(set(payment_days)),
                'payment_count': len(payments_list)
            }
        
        print(f"Learned patterns for {len(self.client_patterns)} clients")
    
    def train_model(self, training_data: List[Tuple[Dict, Dict, bool]]):
        """Train ML model on labeled matches"""
        print("Training ML model...")
        
        X = []
        y = []
        
        for payment, transaction, is_match in training_data:
            features = self.extract_features(payment, transaction)
            X.append(features)
            y.append(1 if is_match else 0)
        
        X = np.array(X)
        y = np.array(y)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train model
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        self.model.fit(X_scaled, y)
        
        # Save model
        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
        with open(self.model_path, 'wb') as f:
            pickle.dump({
                'model': self.model,
                'scaler': self.scaler,
                'client_patterns': self.client_patterns
            }, f)
        
        print(f"Model trained with {len(X)} samples")
    
    def load_model(self) -> bool:
        """Load pre-trained model"""
        if os.path.exists(self.model_path):
            with open(self.model_path, 'rb') as f:
                data = pickle.load(f)
                self.model = data['model']
                self.scaler = data['scaler']
                self.client_patterns = data['client_patterns']
            return True
        return False
    
    def predict_match(self, payment: Dict, transaction: Dict) -> Tuple[bool, float]:
        """Predict if payment matches transaction"""
        if self.model is None:
            # Fallback to rule-based if no model
            return self._rule_based_match(payment, transaction)
        
        features = self.extract_features(payment, transaction)
        features_scaled = self.scaler.transform([features])
        
        # Get prediction and probability
        prediction = self.model.predict(features_scaled)[0]
        probability = self.model.predict_proba(features_scaled)[0][1]
        
        return bool(prediction), float(probability)
    
    def _rule_based_match(self, payment: Dict, transaction: Dict) -> Tuple[bool, float]:
        """Fallback rule-based matching"""
        score = 0.0
        
        # Amount matching
        payment_amount = payment.get('Amount', 0)
        bank_amount = transaction.get('Total', 0)
        amount_diff = abs(payment_amount - bank_amount)
        
        if amount_diff == 0:
            score += 0.4
        elif amount_diff <= bank_amount * 0.01:
            score += 0.3
        elif amount_diff <= bank_amount * 0.05:
            score += 0.2
        else:
            return False, 0.0
        
        # Date matching
        date_diff = self._calculate_date_difference(payment, transaction)
        if date_diff is not None:
            if date_diff == 0:
                score += 0.3
            elif date_diff <= 3:
                score += 0.2
            elif date_diff <= 7:
                score += 0.1
        
        # Contact matching
        if self._same_contact(payment, transaction):
            score += 0.2
        
        # Reference matching
        if self._has_invoice_reference(payment, transaction):
            score += 0.1
        
        return score >= 0.5, score
    
    def match_all_payments(self, payments: List[Dict], transactions: List[Dict]) -> Dict[str, Any]:
        """Match all payments to transactions"""
        print("Matching payments to transactions...")
        
        # Filter active transactions
        active_transactions = [
            tx for tx in transactions 
            if tx.get('Type') == 'RECEIVE' and tx.get('Status') != 'DELETED'
        ]
        
        matches = []
        unmatched_payments = []
        confidence_scores = []
        
        for payment in payments:
            if payment.get('Status') == 'DELETED':
                continue
                
            best_match = None
            best_score = 0.0
            
            for transaction in active_transactions:
                is_match, score = self.predict_match(payment, transaction)
                if is_match and score > best_score:
                    best_match = transaction
                    best_score = score
            
            if best_match:
                matches.append({
                    'payment': payment,
                    'transaction': best_match,
                    'confidence': best_score
                })
                confidence_scores.append(best_score)
            else:
                unmatched_payments.append(payment)
        
        # Calculate statistics
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
        confidence_distribution = {
            'high': sum(1 for s in confidence_scores if s >= 0.8),
            'medium': sum(1 for s in confidence_scores if 0.6 <= s < 0.8),
            'low': sum(1 for s in confidence_scores if s < 0.6)
        }
        
        return {
            'matches': matches,
            'unmatched_payments': unmatched_payments,
            'total_matched': len(matches),
            'total_unmatched': len(unmatched_payments),
            'match_rate': len(matches) / len(payments) * 100 if payments else 0,
            'average_confidence': avg_confidence,
            'confidence_distribution': confidence_distribution
        }


def main():
    """Example usage"""
    matcher = AdvancedPaymentMatcher()
    
    # Load data
    with open('../01-XERO-Data/data/supplementary/payments/payments.json', 'r') as f:
        payments_data = json.load(f)
        payments = payments_data.get('payments', [])
    
    with open('../01-XERO-Data/data/core/bank_transactions.json', 'r') as f:
        bank_data = json.load(f)
        transactions = bank_data.get('bankTransactions', [])
    
    # Learn patterns
    matcher.learn_client_patterns(payments, transactions)
    
    # Try to load existing model or use rule-based
    if not matcher.load_model():
        print("No trained model found, using rule-based matching")
    
    # Match payments
    results = matcher.match_all_payments(payments, transactions)
    
    print(f"\nMatching Results:")
    print(f"Total Matched: {results['total_matched']} ({results['match_rate']:.1f}%)")
    print(f"Average Confidence: {results['average_confidence']:.2f}")
    print(f"Confidence Distribution: {results['confidence_distribution']}")


if __name__ == "__main__":
    main()
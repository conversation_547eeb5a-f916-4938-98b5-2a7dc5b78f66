"""
Utility functions for payment reconciliation
"""

import re
from datetime import datetime
from typing import Union, Optional


def parse_xero_date(date_str: str) -> Optional[datetime]:
    """
    Parse Xero date format /Date(1649894400000+0000)/ to datetime object
    """
    if not date_str:
        return None
        
    match = re.search(r'/Date\((\d+)([+-]\d{4})?\)/', str(date_str))
    if match:
        timestamp_ms = int(match.group(1))
        # Convert milliseconds to seconds
        return datetime.fromtimestamp(timestamp_ms / 1000)
    
    # Try ISO format
    try:
        return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
    except:
        return None


def format_currency(amount: float, currency: str = "GBP") -> str:
    """
    Format amount with currency symbol
    """
    symbols = {
        "GBP": "£",
        "USD": "$",
        "EUR": "€"
    }
    symbol = symbols.get(currency, currency + " ")
    return f"{symbol}{amount:,.2f}"


def calculate_date_range(transactions: list, date_field: str = "Date") -> tuple:
    """
    Calculate date range from list of transactions
    """
    dates = []
    for tx in transactions:
        date_str = tx.get(date_field)
        if date_str:
            parsed_date = parse_xero_date(date_str)
            if parsed_date:
                dates.append(parsed_date)
    
    if dates:
        return min(dates), max(dates)
    return None, None


def group_by_period(transactions: list, period: str = "month") -> dict:
    """
    Group transactions by time period (month, quarter, year)
    """
    grouped = {}
    
    for tx in transactions:
        date_str = tx.get("Date") or tx.get("DateString")
        if not date_str:
            continue
            
        parsed_date = parse_xero_date(date_str)
        if not parsed_date:
            continue
            
        if period == "month":
            key = parsed_date.strftime("%Y-%m")
        elif period == "quarter":
            quarter = (parsed_date.month - 1) // 3 + 1
            key = f"{parsed_date.year}-Q{quarter}"
        elif period == "year":
            key = str(parsed_date.year)
        else:
            key = parsed_date.strftime("%Y-%m-%d")
            
        if key not in grouped:
            grouped[key] = []
        grouped[key].append(tx)
    
    return grouped


def match_amounts_fuzzy(amount1: float, amount2: float, tolerance: float = 0.01) -> bool:
    """
    Check if two amounts match within a tolerance (for currency conversion differences)
    """
    return abs(amount1 - amount2) <= tolerance


def extract_reference_patterns(transactions: list) -> dict:
    """
    Extract common patterns from transaction references
    """
    patterns = {
        'stripe': re.compile(r'(ch_|pi_|pm_)[\w]+'),
        'invoice': re.compile(r'(INV-|Invoice\s*#?)\d+'),
        'payment': re.compile(r'(PAY-|Payment\s*#?)\d+'),
        'bank_transfer': re.compile(r'(transfer|wire|ach)', re.IGNORECASE),
        'paypal': re.compile(r'paypal', re.IGNORECASE)
    }
    
    results = {key: [] for key in patterns}
    
    for tx in transactions:
        ref = str(tx.get('Reference', ''))
        for pattern_name, pattern in patterns.items():
            if pattern.search(ref):
                results[pattern_name].append(tx)
    
    return results
#!/usr/bin/env python3
"""
Reconciliation Database Manager
SQLite database for tracking payment-to-bank transaction matches
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from contextlib import contextmanager

class ReconciliationDatabase:
    def __init__(self, db_path: str = "../data/reconciliation.db"):
        self.db_path = os.path.abspath(db_path)
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._initialize_database()
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def _initialize_database(self):
        """Create database tables if they don't exist"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Reconciliation matches table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS reconciliation_matches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    payment_id TEXT NOT NULL,
                    bank_transaction_id TEXT NOT NULL,
                    match_confidence REAL NOT NULL,
                    match_method TEXT,
                    match_status TEXT DEFAULT 'auto',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT DEFAULT 'system',
                    notes TEXT,
                    UNIQUE(payment_id, bank_transaction_id)
                )
            """)
            
            # Payment details cache
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS payment_details (
                    payment_id TEXT PRIMARY KEY,
                    invoice_number TEXT,
                    invoice_id TEXT,
                    amount REAL,
                    currency TEXT,
                    payment_date TEXT,
                    contact_id TEXT,
                    contact_name TEXT,
                    reference TEXT,
                    status TEXT,
                    data_json TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Bank transaction details cache
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bank_transaction_details (
                    bank_transaction_id TEXT PRIMARY KEY,
                    amount REAL,
                    currency TEXT,
                    transaction_date TEXT,
                    contact_id TEXT,
                    contact_name TEXT,
                    reference TEXT,
                    description TEXT,
                    type TEXT,
                    status TEXT,
                    bank_account_name TEXT,
                    data_json TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Match history table (for audit trail)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS match_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    payment_id TEXT NOT NULL,
                    bank_transaction_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    confidence_before REAL,
                    confidence_after REAL,
                    method_before TEXT,
                    method_after TEXT,
                    changed_by TEXT,
                    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reason TEXT
                )
            """)
            
            # Client patterns table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS client_patterns (
                    contact_id TEXT PRIMARY KEY,
                    contact_name TEXT,
                    typical_amounts TEXT,
                    payment_intervals TEXT,
                    payment_count INTEGER,
                    last_payment_date TEXT,
                    pattern_data TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_payment_id ON reconciliation_matches(payment_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_bank_transaction_id ON reconciliation_matches(bank_transaction_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_match_status ON reconciliation_matches(match_status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_match_confidence ON reconciliation_matches(match_confidence)")
    
    def cache_payment(self, payment: Dict):
        """Cache payment details in database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            invoice = payment.get('Invoice', {})
            contact = invoice.get('Contact', {})
            
            cursor.execute("""
                INSERT OR REPLACE INTO payment_details (
                    payment_id, invoice_number, invoice_id, amount, currency,
                    payment_date, contact_id, contact_name, reference, status, data_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                payment.get('PaymentID'),
                invoice.get('InvoiceNumber'),
                invoice.get('InvoiceID'),
                payment.get('Amount'),
                invoice.get('CurrencyCode', 'GBP'),
                payment.get('Date'),
                contact.get('ContactID'),
                contact.get('Name'),
                payment.get('Reference'),
                payment.get('Status'),
                json.dumps(payment)
            ))
    
    def cache_bank_transaction(self, transaction: Dict):
        """Cache bank transaction details in database"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            contact = transaction.get('Contact', {})
            bank_account = transaction.get('BankAccount', {})
            line_items = transaction.get('LineItems', [])
            description = line_items[0].get('Description', '') if line_items else ''
            
            cursor.execute("""
                INSERT OR REPLACE INTO bank_transaction_details (
                    bank_transaction_id, amount, currency, transaction_date,
                    contact_id, contact_name, reference, description, type,
                    status, bank_account_name, data_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                transaction.get('BankTransactionID'),
                transaction.get('Total'),
                transaction.get('CurrencyCode', 'GBP'),
                transaction.get('DateString'),
                contact.get('ContactID'),
                contact.get('Name'),
                transaction.get('Reference'),
                description,
                transaction.get('Type'),
                transaction.get('Status'),
                bank_account.get('Name'),
                json.dumps(transaction)
            ))
    
    def save_match(self, payment_id: str, bank_transaction_id: str, 
                   confidence: float, method: str, status: str = 'auto',
                   created_by: str = 'system', notes: str = None) -> int:
        """Save a reconciliation match"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO reconciliation_matches (
                        payment_id, bank_transaction_id, match_confidence,
                        match_method, match_status, created_by, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (payment_id, bank_transaction_id, confidence, method, 
                      status, created_by, notes))
                
                # Record in history
                cursor.execute("""
                    INSERT INTO match_history (
                        payment_id, bank_transaction_id, action,
                        confidence_after, method_after, changed_by, reason
                    ) VALUES (?, ?, 'created', ?, ?, ?, ?)
                """, (payment_id, bank_transaction_id, confidence, method, 
                      created_by, notes))
                
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                # Match already exists, update it
                return self.update_match(payment_id, bank_transaction_id, 
                                       confidence, method, created_by, notes)
    
    def update_match(self, payment_id: str, bank_transaction_id: str,
                    confidence: float, method: str, changed_by: str = 'system',
                    notes: str = None) -> int:
        """Update an existing match"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get current values
            cursor.execute("""
                SELECT match_confidence, match_method
                FROM reconciliation_matches
                WHERE payment_id = ? AND bank_transaction_id = ?
            """, (payment_id, bank_transaction_id))
            
            current = cursor.fetchone()
            if not current:
                return self.save_match(payment_id, bank_transaction_id,
                                     confidence, method, 'auto', changed_by, notes)
            
            # Update match
            cursor.execute("""
                UPDATE reconciliation_matches
                SET match_confidence = ?, match_method = ?,
                    modified_date = CURRENT_TIMESTAMP, notes = ?
                WHERE payment_id = ? AND bank_transaction_id = ?
            """, (confidence, method, notes, payment_id, bank_transaction_id))
            
            # Record in history
            cursor.execute("""
                INSERT INTO match_history (
                    payment_id, bank_transaction_id, action,
                    confidence_before, confidence_after,
                    method_before, method_after, changed_by, reason
                ) VALUES (?, ?, 'updated', ?, ?, ?, ?, ?, ?)
            """, (payment_id, bank_transaction_id,
                  current['match_confidence'], confidence,
                  current['match_method'], method, changed_by, notes))
            
            return cursor.rowcount
    
    def delete_match(self, payment_id: str, bank_transaction_id: str,
                    deleted_by: str = 'system', reason: str = None):
        """Delete a match (soft delete by changing status)"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE reconciliation_matches
                SET match_status = 'deleted', 
                    modified_date = CURRENT_TIMESTAMP
                WHERE payment_id = ? AND bank_transaction_id = ?
            """, (payment_id, bank_transaction_id))
            
            # Record in history
            cursor.execute("""
                INSERT INTO match_history (
                    payment_id, bank_transaction_id, action,
                    changed_by, reason
                ) VALUES (?, ?, 'deleted', ?, ?)
            """, (payment_id, bank_transaction_id, deleted_by, reason))
    
    def get_match(self, payment_id: str) -> Optional[Dict]:
        """Get match details for a payment"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT m.*, p.invoice_number, p.amount as payment_amount,
                       p.currency as payment_currency, p.contact_name as payment_contact,
                       b.amount as bank_amount, b.currency as bank_currency,
                       b.contact_name as bank_contact, b.reference as bank_reference
                FROM reconciliation_matches m
                LEFT JOIN payment_details p ON m.payment_id = p.payment_id
                LEFT JOIN bank_transaction_details b ON m.bank_transaction_id = b.bank_transaction_id
                WHERE m.payment_id = ? AND m.match_status != 'deleted'
            """, (payment_id,))
            
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_matches(self, status: str = None, min_confidence: float = None) -> List[Dict]:
        """Get all matches with optional filters"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT m.*, p.invoice_number, p.amount as payment_amount,
                       p.currency as payment_currency, p.contact_name as payment_contact,
                       b.amount as bank_amount, b.currency as bank_currency,
                       b.contact_name as bank_contact, b.reference as bank_reference
                FROM reconciliation_matches m
                LEFT JOIN payment_details p ON m.payment_id = p.payment_id
                LEFT JOIN bank_transaction_details b ON m.bank_transaction_id = b.bank_transaction_id
                WHERE m.match_status != 'deleted'
            """
            
            params = []
            if status:
                query += " AND m.match_status = ?"
                params.append(status)
            
            if min_confidence is not None:
                query += " AND m.match_confidence >= ?"
                params.append(min_confidence)
            
            query += " ORDER BY m.created_date DESC"
            
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_unmatched_payments(self) -> List[Dict]:
        """Get all payments without matches"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT p.*
                FROM payment_details p
                LEFT JOIN reconciliation_matches m ON p.payment_id = m.payment_id
                WHERE m.payment_id IS NULL OR m.match_status = 'deleted'
                ORDER BY p.payment_date DESC
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_unmatched_transactions(self) -> List[Dict]:
        """Get all bank transactions without matches"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT b.*
                FROM bank_transaction_details b
                LEFT JOIN reconciliation_matches m ON b.bank_transaction_id = m.bank_transaction_id
                WHERE b.type = 'RECEIVE' 
                  AND b.status != 'DELETED'
                  AND (m.bank_transaction_id IS NULL OR m.match_status = 'deleted')
                ORDER BY b.transaction_date DESC
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def save_client_pattern(self, contact_id: str, pattern_data: Dict):
        """Save learned client patterns"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO client_patterns (
                    contact_id, contact_name, typical_amounts,
                    payment_intervals, payment_count, pattern_data
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                contact_id,
                pattern_data.get('contact_name'),
                json.dumps(pattern_data.get('typical_amounts', [])),
                json.dumps(pattern_data.get('payment_intervals', [])),
                pattern_data.get('payment_count', 0),
                json.dumps(pattern_data)
            ))
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get reconciliation statistics"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Total matches
            cursor.execute("SELECT COUNT(*) FROM reconciliation_matches WHERE match_status != 'deleted'")
            total_matches = cursor.fetchone()[0]
            
            # Confidence distribution
            cursor.execute("""
                SELECT 
                    SUM(CASE WHEN match_confidence >= 0.9 THEN 1 ELSE 0 END) as high,
                    SUM(CASE WHEN match_confidence >= 0.7 AND match_confidence < 0.9 THEN 1 ELSE 0 END) as medium,
                    SUM(CASE WHEN match_confidence < 0.7 THEN 1 ELSE 0 END) as low
                FROM reconciliation_matches
                WHERE match_status != 'deleted'
            """)
            confidence_dist = dict(cursor.fetchone())
            
            # Method distribution
            cursor.execute("""
                SELECT match_method, COUNT(*) as count
                FROM reconciliation_matches
                WHERE match_status != 'deleted'
                GROUP BY match_method
                ORDER BY count DESC
            """)
            method_dist = {row['match_method']: row['count'] for row in cursor.fetchall()}
            
            # Unmatched counts
            cursor.execute("SELECT COUNT(*) FROM payment_details")
            total_payments = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(*) FROM bank_transaction_details
                WHERE type = 'RECEIVE' AND status != 'DELETED'
            """)
            total_receipts = cursor.fetchone()[0]
            
            return {
                'total_matches': total_matches,
                'total_payments': total_payments,
                'total_receipts': total_receipts,
                'match_rate': (total_matches / total_payments * 100) if total_payments else 0,
                'confidence_distribution': confidence_dist,
                'method_distribution': method_dist,
                'unmatched_payments': total_payments - total_matches,
                'unmatched_receipts': total_receipts - total_matches
            }


def main():
    """Example usage"""
    db = ReconciliationDatabase()
    
    # Example: Save a match
    match_id = db.save_match(
        payment_id='payment123',
        bank_transaction_id='bank456',
        confidence=0.85,
        method='exact_amount_same_contact',
        notes='Matched based on amount and contact'
    )
    
    print(f"Saved match with ID: {match_id}")
    
    # Get statistics
    stats = db.get_statistics()
    print(f"\nDatabase Statistics:")
    print(f"Total Matches: {stats['total_matches']}")
    print(f"Match Rate: {stats['match_rate']:.1f}%")
    print(f"Confidence Distribution: {stats['confidence_distribution']}")


if __name__ == "__main__":
    main()
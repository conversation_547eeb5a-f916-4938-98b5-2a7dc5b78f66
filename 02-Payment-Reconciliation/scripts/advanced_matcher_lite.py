#!/usr/bin/env python3
"""
Advanced Payment Matcher (Lite Version)
Pattern recognition and client behavior analysis without external ML dependencies
"""

import json
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
import statistics
import re

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class AdvancedPaymentMatcherLite:
    def __init__(self, xero_data_path: str = "../01-XERO-Data"):
        self.xero_data_path = xero_data_path
        self.client_patterns = defaultdict(dict)
        self.matching_rules = self._initialize_rules()
        self.learned_matches = defaultdict(list)
        
    def _initialize_rules(self) -> List[Dict]:
        """Initialize matching rules with weights"""
        return [
            {
                'name': 'exact_amount_same_contact',
                'weight': 0.95,
                'check': lambda p, t: (
                    p.get('Amount', 0) == t.get('Total', 0) and
                    self._same_contact(p, t)
                )
            },
            {
                'name': 'exact_amount_same_day',
                'weight': 0.90,
                'check': lambda p, t: (
                    p.get('Amount', 0) == t.get('Total', 0) and
                    self._calculate_date_difference(p, t) == 0
                )
            },
            {
                'name': 'exact_amount_near_date',
                'weight': 0.80,
                'check': lambda p, t: (
                    p.get('Amount', 0) == t.get('Total', 0) and
                    self._calculate_date_difference(p, t) is not None and
                    self._calculate_date_difference(p, t) <= 3
                )
            },
            {
                'name': 'fuzzy_amount_same_contact',
                'weight': 0.75,
                'check': lambda p, t: (
                    self._fuzzy_amount_match(p, t, 0.02) and
                    self._same_contact(p, t)
                )
            },
            {
                'name': 'invoice_ref_in_bank',
                'weight': 0.85,
                'check': lambda p, t: self._has_invoice_reference(p, t)
            },
            {
                'name': 'recurring_payment_pattern',
                'weight': 0.70,
                'check': lambda p, t: self._matches_client_pattern(p, t)
            }
        ]
    
    def _fuzzy_amount_match(self, payment: Dict, transaction: Dict, tolerance: float = 0.05) -> bool:
        """Check if amounts match within tolerance"""
        payment_amount = payment.get('Amount', 0)
        bank_amount = transaction.get('Total', 0)
        
        if payment_amount == 0 or bank_amount == 0:
            return False
            
        diff = abs(payment_amount - bank_amount)
        return diff <= bank_amount * tolerance
    
    def _calculate_date_difference(self, payment: Dict, transaction: Dict) -> Optional[int]:
        """Calculate date difference in days"""
        try:
            # Parse payment date
            payment_date = payment.get('Date', '')
            if '/Date(' in payment_date:
                timestamp_ms = int(payment_date.replace('/Date(', '').replace('+0000)/', ''))
                payment_dt = datetime.fromtimestamp(timestamp_ms / 1000)
            else:
                payment_dt = datetime.fromisoformat(payment_date.split('T')[0])
            
            # Parse transaction date
            bank_date = transaction.get('DateString', '').split('T')[0]
            bank_dt = datetime.fromisoformat(bank_date)
            
            return abs((payment_dt - bank_dt).days)
        except:
            return None
    
    def _same_contact(self, payment: Dict, transaction: Dict) -> bool:
        """Check if payment and transaction have same contact"""
        payment_contact = payment.get('Invoice', {}).get('Contact', {}).get('ContactID')
        bank_contact = transaction.get('Contact', {}).get('ContactID')
        return payment_contact == bank_contact if payment_contact and bank_contact else False
    
    def _has_invoice_reference(self, payment: Dict, transaction: Dict) -> bool:
        """Check if invoice number appears in bank reference"""
        invoice_num = payment.get('Invoice', {}).get('InvoiceNumber', '')
        bank_ref = str(transaction.get('Reference', '')).lower()
        bank_desc = str(transaction.get('LineItems', [{}])[0].get('Description', '')).lower()
        
        if invoice_num:
            invoice_lower = invoice_num.lower()
            return invoice_lower in bank_ref or invoice_lower in bank_desc
        return False
    
    def _matches_client_pattern(self, payment: Dict, transaction: Dict) -> bool:
        """Check if transaction matches client's payment pattern"""
        contact_id = payment.get('Invoice', {}).get('Contact', {}).get('ContactID', '')
        if not contact_id or contact_id not in self.client_patterns:
            return False
            
        patterns = self.client_patterns[contact_id]
        amount = payment.get('Amount', 0)
        
        # Check typical amounts
        if 'typical_amounts' in patterns:
            for typical_amount in patterns['typical_amounts']:
                if abs(amount - typical_amount) <= typical_amount * 0.05:
                    return True
        
        return False
    
    def learn_client_patterns(self, payments: List[Dict], transactions: List[Dict]):
        """Learn payment patterns for each client"""
        print("Learning client payment patterns...")
        
        # Group payments by client
        client_payments = defaultdict(list)
        for payment in payments:
            if payment.get('Status') == 'DELETED':
                continue
            contact_id = payment.get('Invoice', {}).get('Contact', {}).get('ContactID', '')
            if contact_id:
                client_payments[contact_id].append(payment)
        
        # Analyze patterns for each client
        for contact_id, payments_list in client_payments.items():
            if len(payments_list) < 2:  # Need minimum data
                continue
                
            # Extract typical amounts
            amounts = [p.get('Amount', 0) for p in payments_list]
            amount_counts = Counter(amounts)
            
            # Find recurring amounts (appears at least twice)
            typical_amounts = [amt for amt, count in amount_counts.items() if count >= 2]
            
            # Calculate average payment interval
            dates = []
            for payment in payments_list:
                try:
                    date_str = payment.get('Date', '')
                    if '/Date(' in date_str:
                        timestamp_ms = int(date_str.replace('/Date(', '').replace('+0000)/', ''))
                        dates.append(datetime.fromtimestamp(timestamp_ms / 1000))
                except:
                    pass
            
            intervals = []
            if len(dates) > 1:
                dates.sort()
                for i in range(1, len(dates)):
                    intervals.append((dates[i] - dates[i-1]).days)
            
            self.client_patterns[contact_id] = {
                'typical_amounts': typical_amounts,
                'payment_count': len(payments_list),
                'average_interval': statistics.mean(intervals) if intervals else None,
                'contact_name': payments_list[0].get('Invoice', {}).get('Contact', {}).get('Name', 'Unknown')
            }
        
        print(f"Learned patterns for {len(self.client_patterns)} clients")
    
    def calculate_match_score(self, payment: Dict, transaction: Dict) -> Tuple[float, List[str]]:
        """Calculate matching score based on multiple factors"""
        score = 0.0
        matched_rules = []
        
        # Check each rule
        for rule in self.matching_rules:
            try:
                if rule['check'](payment, transaction):
                    score += rule['weight']
                    matched_rules.append(rule['name'])
            except:
                pass
        
        # Additional scoring factors
        
        # Currency match
        if payment.get('Invoice', {}).get('CurrencyCode') == transaction.get('CurrencyCode'):
            score += 0.1
        else:
            score -= 0.5  # Penalty for currency mismatch
        
        # Status check
        if payment.get('Status') == 'AUTHORISED' and transaction.get('Status') != 'DELETED':
            score += 0.05
        
        # Normalize score to 0-1 range
        score = min(max(score, 0), 1)
        
        return score, matched_rules
    
    def match_payment(self, payment: Dict, transactions: List[Dict]) -> Optional[Dict]:
        """Find best matching transaction for a payment"""
        best_match = None
        best_score = 0.0
        best_rules = []
        
        for transaction in transactions:
            if transaction.get('Type') != 'RECEIVE' or transaction.get('Status') == 'DELETED':
                continue
            
            # Skip if different currency
            if payment.get('Invoice', {}).get('CurrencyCode') != transaction.get('CurrencyCode'):
                continue
            
            score, rules = self.calculate_match_score(payment, transaction)
            
            if score > best_score and score >= 0.5:  # Minimum threshold
                best_match = transaction
                best_score = score
                best_rules = rules
        
        if best_match:
            return {
                'transaction': best_match,
                'score': best_score,
                'matched_rules': best_rules
            }
        
        return None
    
    def match_all_payments(self, payments: List[Dict], transactions: List[Dict]) -> Dict[str, Any]:
        """Match all payments to transactions"""
        print("\nMatching payments to transactions...")
        
        matches = []
        unmatched_payments = []
        used_transactions = set()
        score_distribution = defaultdict(int)
        rule_usage = Counter()
        
        # Sort payments by amount (larger first) for better matching
        sorted_payments = sorted(
            [p for p in payments if p.get('Status') != 'DELETED'],
            key=lambda x: x.get('Amount', 0),
            reverse=True
        )
        
        for payment in sorted_payments:
            # Filter out already matched transactions
            available_transactions = [
                t for t in transactions 
                if t.get('BankTransactionID') not in used_transactions
            ]
            
            match_result = self.match_payment(payment, available_transactions)
            
            if match_result:
                matches.append({
                    'payment_id': payment.get('PaymentID'),
                    'payment_amount': payment.get('Amount', 0),
                    'payment_date': payment.get('Date', ''),
                    'invoice_number': payment.get('Invoice', {}).get('InvoiceNumber', ''),
                    'contact_name': payment.get('Invoice', {}).get('Contact', {}).get('Name', ''),
                    'bank_transaction_id': match_result['transaction'].get('BankTransactionID'),
                    'bank_amount': match_result['transaction'].get('Total', 0),
                    'bank_date': match_result['transaction'].get('DateString', ''),
                    'bank_reference': match_result['transaction'].get('Reference', ''),
                    'confidence': match_result['score'],
                    'matched_rules': match_result['matched_rules'],
                    'currency': payment.get('Invoice', {}).get('CurrencyCode', 'GBP')
                })
                
                # Mark transaction as used
                used_transactions.add(match_result['transaction'].get('BankTransactionID'))
                
                # Track statistics
                if match_result['score'] >= 0.9:
                    score_distribution['high'] += 1
                elif match_result['score'] >= 0.7:
                    score_distribution['medium'] += 1
                else:
                    score_distribution['low'] += 1
                
                for rule in match_result['matched_rules']:
                    rule_usage[rule] += 1
            else:
                unmatched_payments.append({
                    'payment_id': payment.get('PaymentID'),
                    'amount': payment.get('Amount', 0),
                    'date': payment.get('Date', ''),
                    'invoice_number': payment.get('Invoice', {}).get('InvoiceNumber', ''),
                    'contact': payment.get('Invoice', {}).get('Contact', {}).get('Name', ''),
                    'currency': payment.get('Invoice', {}).get('CurrencyCode', 'GBP')
                })
        
        # Find unmatched bank transactions
        unmatched_transactions = []
        for transaction in transactions:
            if (transaction.get('Type') == 'RECEIVE' and 
                transaction.get('Status') != 'DELETED' and
                transaction.get('BankTransactionID') not in used_transactions):
                unmatched_transactions.append({
                    'transaction_id': transaction.get('BankTransactionID'),
                    'amount': transaction.get('Total', 0),
                    'date': transaction.get('DateString', ''),
                    'contact': transaction.get('Contact', {}).get('Name', ''),
                    'reference': transaction.get('Reference', ''),
                    'currency': transaction.get('CurrencyCode', 'GBP')
                })
        
        # Calculate summary statistics
        total_payments = len(sorted_payments)
        avg_confidence = (
            sum(m['confidence'] for m in matches) / len(matches) 
            if matches else 0
        )
        
        return {
            'matches': matches,
            'unmatched_payments': unmatched_payments,
            'unmatched_transactions': unmatched_transactions,
            'summary': {
                'total_payments': total_payments,
                'total_matched': len(matches),
                'total_unmatched': len(unmatched_payments),
                'match_rate': len(matches) / total_payments * 100 if total_payments else 0,
                'average_confidence': avg_confidence,
                'confidence_distribution': dict(score_distribution),
                'most_used_rules': rule_usage.most_common(5)
            }
        }
    
    def generate_report(self, results: Dict[str, Any], output_path: str = None):
        """Generate detailed matching report"""
        print("\n" + "="*60)
        print("ADVANCED MATCHING RESULTS")
        print("="*60)
        
        summary = results['summary']
        print(f"\nTotal Payments: {summary['total_payments']}")
        print(f"Matched: {summary['total_matched']} ({summary['match_rate']:.1f}%)")
        print(f"Unmatched: {summary['total_unmatched']}")
        print(f"Average Confidence: {summary['average_confidence']:.2f}")
        
        print("\nConfidence Distribution:")
        for level, count in summary['confidence_distribution'].items():
            print(f"  {level.capitalize()}: {count}")
        
        print("\nMost Used Matching Rules:")
        for rule, count in summary['most_used_rules']:
            print(f"  {rule}: {count} times")
        
        print("\nSample Matches:")
        for match in results['matches'][:5]:
            print(f"  Invoice {match['invoice_number']} → Bank Ref: {match['bank_reference']}")
            print(f"    Amount: {match['currency']} {match['payment_amount']} → {match['bank_amount']}")
            print(f"    Confidence: {match['confidence']:.2f} ({', '.join(match['matched_rules'])})")
        
        # Save detailed report if path provided
        if output_path:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\nDetailed report saved to: {output_path}")


def main():
    """Main execution"""
    matcher = AdvancedPaymentMatcherLite()
    
    # Load data
    print("Loading data...")
    with open('../01-XERO-Data/data/supplementary/payments/payments.json', 'r') as f:
        payments_data = json.load(f)
        payments = payments_data.get('payments', [])
    
    with open('../01-XERO-Data/data/core/bank_transactions.json', 'r') as f:
        bank_data = json.load(f)
        transactions = bank_data.get('bankTransactions', [])
    
    print(f"Loaded {len(payments)} payments and {len(transactions)} bank transactions")
    
    # Learn patterns
    matcher.learn_client_patterns(payments, transactions)
    
    # Match payments
    results = matcher.match_all_payments(payments, transactions)
    
    # Generate report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = f"../reports/advanced_matching_report_{timestamp}.json"
    matcher.generate_report(results, report_path)


if __name__ == "__main__":
    main()